#!/usr/bin/env bash

if [ ! -z "$WWWUSER" ]; then
    usermod -u $WWWUSER sail
fi

if [ ! -d /.composer ]; then
    mkdir /.composer
fi

touch ./storage/logs/laravel.log
chown -R www-data:www-data ./storage/app/public
chown -R www-data:www-data ./storage/framework/sessions
chown -R www-data:www-data ./storage/framework/views
chown -R www-data:www-data ./storage/framework/cache
chown -R www-data:www-data ./storage/logs

chmod -R ugo+rw /.composer

php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear
php artisan filament:optimize-clear
php artisan filament:clear-cached-components
php artisan migrate --force
php artisan storage:link --force
php artisan translations:init --app=1

if [ $# -gt 0 ]; then
    exec gosu $WWWUSER "$@"
else
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
fi
