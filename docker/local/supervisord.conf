[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:apache2]
command=/usr/sbin/apache2ctl -D FOREGROUND
autostart=true
autorestart=true
priority=5
stdout_logfile=/var/log/supervisor/%(program_name)s_stdout.log
stderr_logfile=/var/log/supervisor/%(program_name)s_stderr.log

# [program:php]
# command=%(ENV_SUPERVISOR_PHP_COMMAND)s
# user=sail
# environment=LARAVEL_SAIL="1"
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0

[program:reverb]
command=/usr/bin/php /var/www/html/artisan reverb:start --host=0.0.0.0 --port=8081
directory=/var/www/html
autostart=true
autorestart=true
priority=10
stdout_logfile=/var/log/supervisor/%(program_name)s_stdout.log
stderr_logfile=/var/log/supervisor/%(program_name)s_stderr.log

[program:queue-worker]
command=/usr/bin/php /var/www/html/artisan queue:work --sleep=3 --tries=3 --timeout=60
directory=/var/www/html
autostart=true
autorestart=unexpected
exitcodes=0
priority=8 
numprocs=1 
stdout_logfile=/var/log/supervisor/%(program_name)s_%(process_num)02d_stdout.log
stderr_logfile=/var/log/supervisor/%(program_name)s_%(process_num)02d_stderr.log
stopasgroup=true 
killasgroup=true 
stopsignal=QUIT