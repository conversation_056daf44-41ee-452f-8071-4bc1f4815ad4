FROM node:18-bullseye

ARG WWWGROUP

WORKDIR /var/www/html

ENV TZ=UTC
# ENV SUPERVISOR_PHP_COMMAND="/usr/bin/php -d variables_order=EGPCS /var/www/html/artisan serve --host=0.0.0.0 --port=8000"

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Update package lists and install required packages
RUN apt-get update && \
    apt-get install -y \
        lsb-release \
        apt-transport-https \
        ca-certificates \
        curl \
        software-properties-common \
        supervisor

# Add PHP 8.3 repository and install PHP with required extensions
RUN curl -sSL https://packages.sury.org/php/apt.gpg | apt-key add - && \
    echo "deb https://packages.sury.org/php/ $(lsb_release -sc) main" | tee /etc/apt/sources.list.d/php.list && \
    apt-get update && \
    apt-get install -y \
        php8.3 \
        php8.3-cli \
        php8.3-mbstring \
        php8.3-curl \
        php8.3-mongodb \
        php8.3-xml \
        php8.3-intl \
        php8.3-mysql \
        php8.3-gd \
        php8.3-bcmath \
        php8.3-sqlite3 \
        php8.3-zip \
        apache2 \
        libapache2-mod-php8.3 \
        git \
        unzip \
    && rm -rf /var/lib/apt/lists/*

# Enable required Apache modules
RUN a2enmod expires headers rewrite

# Enable mod_rewrite for Apache
RUN a2enmod rewrite

# Update Apache config to serve from the public folder
COPY ./apache/laravel.conf /etc/apache2/sites-available/000-default.conf

# Enable the new site configuration
RUN a2ensite 000-default.conf

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN setcap "cap_net_bind_service=+ep" /usr/bin/php8.3

# RUN groupadd --force -g $WWWGROUP sail
# RUN useradd -ms /bin/bash --no-user-group -g $WWWGROUP -u 1337 sail

COPY . .

RUN mkdir -p storage/logs bootstrap/cache \
 && chown -R www-data:www-data storage bootstrap/cache \
 && chmod -R 775 storage bootstrap/cache

# Set permissions (optional, but recommended)
RUN chown -R www-data:www-data /var/www/html

ENV COMPOSER_PROCESS_TIMEOUT=600

# Install PHP dependencies
RUN composer install --no-interaction --prefer-dist --optimize-autoloader

# RUN npm install && \
#     npm run build || true

# Build assets with Vite
RUN npm install
RUN npm run build

COPY docker/local/start-container /usr/local/bin/start-container
COPY docker/local/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/local/php.ini /etc/php/8.3/cli/conf.d/99-sail.ini
RUN chmod +x /usr/local/bin/start-container

# app port
EXPOSE 80

# reverb port
EXPOSE 8081/tcp

ENTRYPOINT ["start-container"]
