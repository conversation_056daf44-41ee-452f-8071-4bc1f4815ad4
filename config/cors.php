<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths'                    => ['api/*', 'sanctum/csrf-cookie', '/broadcasting/auth'],

    'allowed_methods'          => ['*'],

    'allowed_origins'          => array_merge(
        explode(',', env('ALLOWED_ORIGINS', '')),
        [
            env("APP_URL"),
            env("APP_URL") . ":5500",
            env("ADMIN_URL"),
            env("ADMIN_URL") . ":5500",
            env("DASHBOARD_URL"),
            env("DASHBOARD_URL") . ":5500",
        ]
    ),

    'allowed_origins_patterns' => [],

    'allowed_headers'          => ['*'],

    'exposed_headers'          => [],

    'max_age'                  => 0,

    'supports_credentials'     => true,

];
