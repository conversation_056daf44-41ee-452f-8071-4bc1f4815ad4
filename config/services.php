<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'app'             => [
        'name' => env('APP_NAME'),
        'url' => env('APP_URL')
    ],

    'mailgun'         => [
        'domain'   => env('MAILGUN_DOMAIN'),
        'secret'   => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme'   => 'https',
    ],

    'postmark'        => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses'             => [
        'key'    => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend'          => [
        'key' => env('RESEND_KEY'),
    ],

    'google'          => [
        'client_id'     => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect'      => '/auth/google/callback',

        'maps_api_key' => env('GOOGLE_MAPS_API_KEY'),
        'map_id' => env('GOOGLE_MAPS_MAP_ID'),
    ],

    'google_analytics'          => [
        'measurement_id'        => env('GOOGLE_ANALYTICS_MEASUREMENT_ID'),
        'api_secret'            => env('GOOGLE_ANALYTICS_API_SECRET'),
    ],

    'github'          => [
        'client_id'     => env('GITHUB_CLIENT_ID'),
        'client_secret' => env('GITHUB_CLIENT_SECRET'),
        'redirect'      => '/auth/github/callback',
    ],

    'facebook'        => [
        'client_id'     => env('FACEBOOK_CLIENT_ID'),
        'client_secret' => env('FACEBOOK_CLIENT_SECRET'),
        'redirect'      => '/auth/facebook/callback',
    ],

    'twitter-oauth-2' => [
        'client_id'     => env('TWITTER_CLIENT_ID'),
        'client_secret' => env('TWITTER_CLIENT_SECRET'),
        'redirect'      => '/auth/twitter-oauth-2/callback',
    ],

    'linkedin-openid' => [
        'client_id'     => env('LINKEDIN_CLIENT_ID'),
        'client_secret' => env('LINKEDIN_CLIENT_SECRET'),
        'redirect'      => '/auth/linkedin-openid/callback',
    ],

    'bitbucket'       => [
        'client_id'     => env('BITBUCKET_CLIENT_ID'),
        'client_secret' => env('BITBUCKET_CLIENT_SECRET'),
        'redirect'      => '/auth/bitbucket/callback',
    ],

    'gitlab'          => [
        'client_id'     => env('GITLAB_CLIENT_ID'),
        'client_secret' => env('GITLAB_CLIENT_SECRET'),
        'redirect'      => '/auth/gitlab/callback',
    ],

    'stripe'          => [
        'secret_key'             => env('STRIPE_SECRET_KEY'),
        'publishable_key'        => env('STRIPE_PUBLISHABLE_KEY'),
        'webhook_signing_secret' => env('STRIPE_WEBHOOK_SIGNING_SECRET'),
        'admin_webhook_events_email_recipients' =>  env('STRIPE_ADMIN_WEBHOOK_EVENTS_EMAIL_RECIPIENTS', null),
    ],

    'paddle'          => [
        'vendor_id'         => env('PADDLE_VENDOR_ID'),
        'client_side_token' => env('PADDLE_CLIENT_SIDE_TOKEN'),
        'vendor_auth_code'  => env('PADDLE_VENDOR_AUTH_CODE'),
        'public_key'        => env('PADDLE_PUBLIC_KEY'),
        'webhook_secret'    => env('PADDLE_WEBHOOK_SECRET'),
        'is_sandbox'        => env('PADDLE_IS_SANDBOX', false),
    ],

    'lemon-squeezy'   => [
        'api_key'        => env('LEMON_SQUEEZY_API_KEY'),
        'store_id'       => env('LEMON_SQUEEZY_STORE_ID'),
        'signing_secret' => env('LEMON_SQUEEZY_SIGNING_SECRET'),
        'is_test_mode'   => env('LEMON_SQUEEZY_IS_TEST_MODE', false),
    ],

    'twilio'          => [
        'sid'   => env('TWILIO_SID'),
        'token' => env('TWILIO_TOKEN'),
        'from'  => env('TWILIO_FROM'),
    ],

    'translation_api' => [
        'key' => env('TRANSLATION_API_KEY', '123'),
        'url' => env('TRANSLATION_API_URL'),
    ],
    'cms_api'         => [
        'url' => env('CMS_URL'),
        'api_id' => 1,
    ],
    'app_main' => [
        'url' => env('APP_URL_ROOT', 'localhost'),
    ],
    'base_url' => [
        'app' => env('APP_URL'),
        'dashboard' => env('DASHBOARD_URL')
    ],
    'ip_location' => [
        'url' => env('IP_LOCATION_URL'),
    ],
    'cdn' => [
        'url' => env('CDN_URL'),
    ],
    'gotenberg' => [
        'url' => env('GOTENBERG_URL'),
    ],
];
