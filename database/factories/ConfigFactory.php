<?php

namespace Database\Factories;

use App\Constants\ConfigValueTypeEnum;
use App\Models\Config;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Config>
 */
class ConfigFactory extends Factory
{
    protected $model = Config::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key' => $this->faker->unique()->slug(),
            'value' => $this->faker->sentence(),
            'value_type' => $this->faker->randomElement(ConfigValueTypeEnum::cases())->value,
            'deletable' => $this->faker->boolean(80), // 80% chance of being deletable
        ];
    }

    /**
     * Indicate that the config is deletable.
     */
    public function deletable(): static
    {
        return $this->state(fn (array $attributes) => [
            'deletable' => true,
        ]);
    }

    /**
     * Indicate that the config is not deletable.
     */
    public function notDeletable(): static
    {
        return $this->state(fn (array $attributes) => [
            'deletable' => false,
        ]);
    }

    /**
     * Indicate that the config is of string type.
     */
    public function string(): static
    {
        return $this->state(fn (array $attributes) => [
            'value_type' => ConfigValueTypeEnum::STRING->value,
            'value' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the config is of JSON type.
     */
    public function json(): static
    {
        return $this->state(fn (array $attributes) => [
            'value_type' => ConfigValueTypeEnum::JSON->value,
            'value' => json_encode(['key' => $this->faker->word(), 'value' => $this->faker->sentence()]),
        ]);
    }

    /**
     * Indicate that the config is of HTML type.
     */
    public function html(): static
    {
        return $this->state(fn (array $attributes) => [
            'value_type' => ConfigValueTypeEnum::HTML->value,
            'value' => '<p>' . $this->faker->paragraph() . '</p>',
        ]);
    }
}
