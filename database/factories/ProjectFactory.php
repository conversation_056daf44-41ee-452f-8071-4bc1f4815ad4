<?php

namespace Database\Factories;

use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectFactory extends Factory
{
    protected $model = Project::class;

    public function definition(): array
    {
        return [
            'project_name' => $this->faker->unique()->words(3, true),
            'project_info' => $this->faker->paragraph(),
            'project_type' => $this->faker->randomElement(['residential', 'commercial', 'industrial']),
            'location_type' => $this->faker->randomElement(['urban', 'rural']),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'street_number' => $this->faker->buildingNumber(),
            'street' => $this->faker->streetName(),
            'city' => $this->faker->city(),
            'country_id' => null,
            'subscription_id' => null,
            'created_by' => User::factory(),
            'last_user_to_interact' => User::factory(),
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }
}

