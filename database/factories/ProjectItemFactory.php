<?php

namespace Database\Factories;

use App\Models\Project;
use App\Models\ProjectItem;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectItemFactory extends Factory
{
    protected $model = ProjectItem::class;

    public function definition(): array
    {
        return [
            'project_id' => Project::factory(),
            'item_name' => $this->faker->unique()->word(),
            'item_info' => [
                'manufacturer' => $this->faker->company(),
                'warranty_years' => $this->faker->numberBetween(1, 5),
            ],
            'item_type' => $this->faker->randomElement(['equipment', 'material']),
            'created_by' => User::factory(),
            'last_user_to_interact' => User::factory(),
        ];
    }
}

