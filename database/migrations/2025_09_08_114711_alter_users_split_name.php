<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
       
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'name')) {
                $table->renameColumn('name', 'last_name');
            }
        });

      
        Schema::table('users', function (Blueprint $table) {
            if (! Schema::hasColumn('users', 'first_name')) {
                $table->string('first_name')->nullable()->after('email');
            }
        });

        
        DB::statement("
            UPDATE users
            SET
              first_name = TRIM(SUBSTRING_INDEX(COALESCE(last_name, ''), ' ', 1)),
              last_name  = TRIM(
                            CASE
                              WHEN LOCATE(' ', COALESCE(last_name,'')) = 0
                                THEN COALESCE(last_name,'')
                              ELSE SUBSTRING(COALESCE(last_name,''), LOCATE(' ', COALESCE(last_name,'')) + 1)
                            END
                          )
        ");

       
        DB::statement("
            UPDATE users
            SET last_name = CASE
                WHEN last_name = '' THEN first_name
                ELSE last_name
            END
        ");

       
        Schema::table('users', function (Blueprint $table) {
            
            $table->string('first_name')->nullable(false)->change();
        });
    }

    public function down(): void
    {
        
        Schema::table('users', function (Blueprint $table) {
            if (! Schema::hasColumn('users', 'name')) {
                $table->string('name')->nullable()->after('email');
            }
        });

      
        DB::statement("
            UPDATE users
            SET name = TRIM(CONCAT(
                COALESCE(first_name, ''),
                CASE WHEN COALESCE(first_name, '') <> '' AND COALESCE(last_name, '') <> '' THEN ' ' ELSE '' END,
                COALESCE(last_name, '')
            ))
        ");

        
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'first_name')) {
                $table->dropColumn('first_name');
            }
        });

       
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'last_name')) {
                $table->renameColumn('last_name', 'name');
            }
        });
    }
};
