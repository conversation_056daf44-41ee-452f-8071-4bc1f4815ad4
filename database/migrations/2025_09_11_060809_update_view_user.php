<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("DROP VIEW IF EXISTS user");
        DB::statement("
            CREATE OR REPLACE VIEW user AS
            SELECT
                id,
                first_name,
                last_name,
                email AS username,
                email_verified_at,
                created_at 
            FROM
                users
            WHERE
                deleted_at IS NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("DROP VIEW IF EXISTS user");
        DB::statement("
            CREATE OR REPLACE VIEW user AS
            SELECT
                id,
                name,
                email AS username,
                email_verified_at,
                created_at 
            FROM
                users
            WHERE
                deleted_at IS NULL
        ");
    }
};
