<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receipts', function (Blueprint $table) {
            $table->id(); // same as 'id = Column(Integer, primary_key=True)'

            $table->uuid('uuid')->unique();
            $table->foreignId('transaction_id')->constrained()->onDelete('cascade');
            $table->string('status')->default(\App\Constants\InvoiceStatus::UNRENEDERED);
            $table->string('filename')->nullable(); 
            
            $table->timestamps(); // adds created_at and updated_at
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipts');
    }
};
