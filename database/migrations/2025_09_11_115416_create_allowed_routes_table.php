<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('allowed_routes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('roleAppId')->index()->constrained('role_apps')->cascadeOnDelete();
            $table->foreignId('routeId')->index()->constrained('routes')->cascadeOnDelete();
            $table->string('methods', 255);
            $table->tinyInteger('deleted')->default(0); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('allowed_routes');
    }
};
