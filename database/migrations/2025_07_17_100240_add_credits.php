<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         // Add credit_count to products
        Schema::table('products', function (Blueprint $table) {
            $table->integer('credit_count')->default(100); // or after any specific column
        });

        // Add credit_balance to subscriptions
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->integer('credit_balance')->default(100); // or after any specific column
        });

        // Optional: force update existing rows if you want to ensure it for existing records
        DB::table('products')->update(['credit_count' => 100]);
        DB::table('subscriptions')->update(['credit_balance' => 100]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
         Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('credit_count');
        });

        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('credit_balance');
        });
    }
};
