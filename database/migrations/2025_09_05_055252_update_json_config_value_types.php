<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
          $jsonKeys = [
           'invoices_receipts.color_theme',
            'invoices_receipts.company_data'
        ];

        DB::table('configs')
            ->whereIn('key', $jsonKeys)
            ->update(['value_type' => 'json']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
