<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['last_user_to_interact']);

            // Now drop the composite unique index
            $table->dropUnique('projects_last_user_to_interact_project_name_unique');

            // Add the new column
            $table->foreignId('created_by')
                ->nullable()
                ->after('subscription_id')
                ->constrained('users')
                ->nullOnDelete();

            // Add the new composite unique
            $table->unique(['created_by', 'project_name'], 'projects_created_by_project_name_unique');

            // Optionally re-add foreign key for last_user_to_interact
            $table->foreign('last_user_to_interact')->references('id')->on('users')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropUnique('projects_created_by_project_name_unique');
            $table->dropConstrainedForeignId('created_by');

            // Drop and re-add to ensure rollback cleanly reverses changes
            $table->dropForeign(['last_user_to_interact']);
            $table->unique(['last_user_to_interact', 'project_name'], 'projects_last_user_to_interact_project_name_unique');
            $table->foreign('last_user_to_interact')->references('id')->on('users')->nullOnDelete();
        });

    }
};
