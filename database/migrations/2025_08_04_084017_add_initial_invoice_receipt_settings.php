<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('configs')->insert([
            [
                'key' => 'invoices_receipts.company_data',
                'value' => json_encode([
                        "companyName" => "Solar Control Energy",
                        "street" => "131 Continental Dr",
                        "suite" => "Suite 305",
                        "city" => "Newark, Delaware 19713",
                        "country" => "United States of America",
                        "email" => "<EMAIL>",
                ]),
            ],
            [
                'key' => 'invoices_receipts.color_theme',
                'value' => json_encode([
                    'primary' => '#FED300',
                    "accent"=>"#0065A2",
                    'docTypeColor' => '#000000',
                ]),
            ],
            [
                'key' => 'invoices_receipts.background_image_url',
                'value' => '{{CDN_URL}}/images/pdf/invoice-receipt/header.png',
            ],
            [
                'key' => 'invoices_receipts.logo_url',
                'value' => '{{CDN_URL}}/images/pdf/invoice-receipt/logo.png',
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
