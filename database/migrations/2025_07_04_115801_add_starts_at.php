<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->timestamp('starts_at')->nullable();
        });

        // Set starts_at to created_at for existing rows
        DB::table('subscriptions')->update([
            'starts_at' => DB::raw('created_at')
        ]);

        // Now enforce non-null constraint
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->timestamp('starts_at')->default(DB::raw('CURRENT_TIMESTAMP'))->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('starts_at');
        });
    }
};
