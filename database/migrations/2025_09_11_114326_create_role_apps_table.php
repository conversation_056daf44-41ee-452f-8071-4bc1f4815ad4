<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_apps', function (Blueprint $table) {
            $table->id(); 
            $table->foreignId('appId')->index()->constrained('app')->cascadeOnDelete();
            $table->string('role', 50);
            $table->tinyInteger('default')->default(0); 
            $table->tinyInteger('deleted')->default(0); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_apps');
    }
};
