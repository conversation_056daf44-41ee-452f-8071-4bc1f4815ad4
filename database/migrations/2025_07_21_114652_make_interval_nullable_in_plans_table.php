<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            //
            $table->unsignedBigInteger('interval_id')->nullable()->change();
            $table->unsignedBigInteger('interval_count')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            //
            $table->unsignedBigInteger('interval_id')->nullable(false)->change();
            $table->unsignedBigInteger('interval_count')->nullable(false)->change();
        });
    }
};
