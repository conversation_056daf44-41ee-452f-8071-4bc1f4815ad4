<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_contact_platform_informations', function (Blueprint $table) {
            $table->id();
            $table->string('value')->nullable(false);
            $table->foreignId('user_id')->constrained();
            $table->foreignId('contact_platform_id')->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_contact_platform_informations');
    }
};
