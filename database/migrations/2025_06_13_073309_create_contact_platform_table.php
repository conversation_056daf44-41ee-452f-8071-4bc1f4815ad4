<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_platforms', function (Blueprint $table) {
            $table->id();
            $table->string('key')->nullable(false);
            $table->string('label')->nullable(false);
            $table->enum('type', ['social', 'messenger', 'network'])->nullable(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_platforms');
    }
};
