<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();

            $table->string('project_name');
            $table->text('project_info')->nullable();
            $table->string('project_type')->nullable();
            $table->string('location_type')->nullable();

            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 10, 7)->nullable();
            $table->string('street_number')->nullable();
            $table->string('street')->nullable();
            $table->string('city')->nullable();

            $table->foreignId('country_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('subscription_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('last_user_to_interact')->nullable()->constrained('users')->nullOnDelete();

            // Composite unique constraint: project_name per user
            $table->unique(['last_user_to_interact', 'project_name']);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
