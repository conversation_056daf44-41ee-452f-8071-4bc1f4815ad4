<?php

use App\Constants\AgeRangeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('account_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_type_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->boolean('is_other')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('account_informations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('account_type_id')->constrained()->cascadeOnDelete();
            $table->foreignId('account_category_id')->nullable()->constrained()->cascadeOnDelete();
            $table->string('profession')->nullable();
            $table->enum('age_range', array_column(AgeRangeEnum::cases(), 'value'))->nullable();
            $table->string('company_name', 255)->nullable();
            $table->string('other_category', 255)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_informations');
        Schema::dropIfExists('account_categories');
        Schema::dropIfExists('account_types');
    }
};
