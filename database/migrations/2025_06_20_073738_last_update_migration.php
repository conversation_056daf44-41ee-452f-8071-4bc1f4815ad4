<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('features', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description');
            $table->string('key');
            $table->integer('ui_order');
            $table->integer('display_for_free');
            $table->integer('allow_for_free');
            $table->integer('hide');
            $table->timestamps();
        });

        Schema::create('feature_product', function (Blueprint $table) {
            $table->id();
            $table->foreignId('feature_id')->constrained()->cascadeOnDelete();
            $table->foreignId('product_id')->constrained()->cascadeOnDelete();
            $table->boolean('enabled')->default(true);
            $table->timestamps();
        });

        Schema::table('products', function (Blueprint $table){
            $table->renameColumn('features', 'feature');
            $table->string('account_type')->nullable(); 
        });

        Schema::table('plans', function (Blueprint $table) {
            $table->integer('credit')->nullable();
            $table->integer('user_count')->nullable();
            $table->string('period')->nullable();
            $table->integer('price_directly')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('features');
        Schema::dropIfExists('feature_product');

        Schema::table('products', function (Blueprint $table) {
            $table->renameColumn('feature', 'features');
            $table->dropColumn('account_type');
        });

        Schema::table('plans', function (Blueprint $table) {
            $table->dropColumn('credit');
            $table->dropColumn('user_count');
            $table->dropColumn('period');
            $table->dropColumn('price_directly');
        });
        //
    }
};
