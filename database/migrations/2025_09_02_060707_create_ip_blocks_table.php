<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ip_blocks', function (Blueprint $table) {
            $table->id();

           
            $table->unsignedBigInteger('country_id');
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('cascade');

            $table->string('cidr');

            $table->timestamps();

            $table->unique(['country_id', 'cidr']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ip_blocks');
    }
};
