<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('invoice_number')->nullable();
        });

        // Populate the column for existing rows
        DB::table('transactions')->orderBy('id')->chunk(100, function ($transactions) {
            foreach ($transactions as $transaction) {
                $reference = Carbon::parse($transaction->created_at)->format('Y-m') . '-' .
                    str_pad((string)$transaction->id, 4, '0', STR_PAD_LEFT);

                DB::table('transactions')
                    ->where('id', $transaction->id)
                    ->update(['invoice_number' => $reference]);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            //
        });
    }
};
