<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('receipts', function (Blueprint $table) {
            $table->string('payment_provider_refund_id')->nullable();
            $table->string('receipt_number')->nullable();
            $table->integer('remaining_to_pay')->nullable();
            $table->integer('amount_already_paid')->nullable();
            $table->integer('amount_paid')->nullable();
            $table->string('receipt_status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('receipts', function (Blueprint $table) {
            //
        });
    }
};
