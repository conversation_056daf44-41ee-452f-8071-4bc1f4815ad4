<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_id')->constrained('plans');
            $table->tinyInteger('step_id')->default(1)->comment('Checkout step from 1 to 3');
            $table->string('cart_uid')->unique()->comment('Unique identifier for cart stored in cookies');
            $table->unsignedInteger('country_id')->nullable()->comment('For future use');
            $table->string('ip_address', 45)->nullable()->comment('For future use');
            $table->string('language_iso_2', 2)->nullable()->comment('For future use');
            $table->timestamp('converted_at')->nullable()->comment('When subscription was purchased');
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->timestamps();

            $table->index(['cart_uid']);
            $table->index(['user_id', 'converted_at']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carts');
    }
};
