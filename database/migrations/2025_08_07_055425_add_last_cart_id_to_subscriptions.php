<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->foreignId('last_cart_id')->nullable()->constrained('carts');
            $table->dropColumn('plan_json');


        });
        DB::statement("
                UPDATE subscriptions s
                JOIN (
                    SELECT c.user_id, c.id AS cart_id, c.created_at
                    FROM carts c
                    WHERE JSON_UNQUOTE(JSON_EXTRACT(plan_json, '$.interval_id')) IS NOT NULL
                    AND c.user_id IS NOT NULL
                    ORDER BY c.user_id, c.created_at DESC
                ) recent_carts
                ON s.user_id = recent_carts.user_id
                AND s.last_cart_id IS NULL
                JOIN (
                    SELECT user_id, MAX(created_at) AS max_created
                    FROM carts
                    WHERE JSON_UNQUOTE(JSON_EXTRACT(plan_json, '$.interval_id')) IS NOT NULL
                    GROUP BY user_id
                ) max_carts
                ON recent_carts.user_id = max_carts.user_id
                AND recent_carts.created_at = max_carts.max_created
                SET s.last_cart_id = recent_carts.cart_id
            ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            //
        });
    }
};
