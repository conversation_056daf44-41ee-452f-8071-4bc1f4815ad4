<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ConfigsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('configs')->upsert([
            [
                'key' => 'deviation_threshold_green_percent',
                'value' => '5',
            ],
            [
                'key' => 'deviation_threshold_orange_percent',
                'value' => '10',
            ],
            [
                'key' => 'account.delete_duration',
                'value' => '1',
            ],
            [
                'key' => 'account.stored_duration',
                'value' => '365',
            ],
        ], ['key'], ['value']);
    }
}
