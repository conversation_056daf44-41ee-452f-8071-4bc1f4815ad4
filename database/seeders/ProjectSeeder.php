<?php

namespace Database\Seeders;

use App\Models\ProjectItem;
use Illuminate\Database\Seeder;
use App\Models\Project;

class ProjectSeeder extends Seeder
{
    public function run(): void
    {
        Project::factory()
            ->count(10)
            ->state([
                'created_by' => 13,
                'last_user_to_interact' => 13,
            ])
            ->has(
                ProjectItem::factory()
                    ->count(5)
                    ->state([
                        'created_by' => 13,
                        'last_user_to_interact' => 13,
                    ]),
                'items'
            )
            ->create();
    }
}

