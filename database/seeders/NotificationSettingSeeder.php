<?php

namespace Database\Seeders;

use App\Constants\NotificationSettingType;
use App\Models\NotificationSetting;
use Illuminate\Database\Seeder;

class NotificationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $preferences = [
            // Email
            ['key' => 'email_receive_pvgis_actuality', 'name' => 'Je souhaite recevoir par Email les actualités de Solar Control Energy', 'type' => NotificationSettingType::Email],
            ['key' => 'email_receive_pvgis_member_actuality', 'name' => "Je souhaite recevoir par Email l'actualité des membres de Solar Control Energy", 'type' => NotificationSettingType::Email],
            ['key' => 'email_online_learning_pvgis', 'name' => 'Études en ligne Solar Control Energy', 'type' => NotificationSettingType::Email],
            ['key' => 'email_partner_offer_pvgis', 'name' => 'Les offres sélectionnées par Solar Control Energy auprès de ses partenaires', 'type' => NotificationSettingType::Email],
            // Data
            ['key' => 'data_share_partner', 'name' => 'J’accèpte que Solar Control Energy partage mes données avec ses partenaires', 'type' => NotificationSettingType::Data],
        ];

        foreach ($preferences as $preference) {
            NotificationSetting::updateOrCreate(['key' => $preference['key']], $preference);
        }
    }
}
