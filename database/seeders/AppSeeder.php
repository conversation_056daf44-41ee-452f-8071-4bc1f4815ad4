<?php
namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AppSeeder extends Seeder
{
    public function run(): void
    {
        $appsToSeed = [
            [
                'name' => 'Tall Stack UI',
                'url'  => config("app.url"),
            ],
            [
                'name' => 'Tall Stack UI Admin',
                'url'  => config("app.admin_url"),
            ],
            [
                'name' => 'Tall Stack UI Dashboard',
                'url'  => config("app.dashboard_url"),
            ]
        ];

        foreach ($appsToSeed as $appData) {
            $app = DB::table('app')->where('url', $appData['url'])->first();

            if ($app) {
                DB::table('app')->where('id', $app->id)->update([
                    'name'       => $appData['name'],
                    'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                ]);
            } else {
                DB::table('app')->insert([
                    'name'       => $appData['name'],
                    'appKey'     => 'app_key_' . Str::random(10), 
                    'secret'     => Str::random(32),            
                    'url'        => $appData['url'],
                    'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                ]);
            }
        }
    }
}
