<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Region;

class RegionSeeder extends Seeder
{
    public function run(): void
    {
        $regions = [
            [
                'id' => 1,
                'name' => 'NORTH AMERICA',
                'import_id' => null,
                'translation' => '{"fr":"AMERIQUE DU NORD"}',
                'status' => true,
            ],
            [
                'id' => 2,
                'name' => 'CENTRAL AMERICA',
                'import_id' => null,
                'translation' => '{"fr":"AMERIQUE CENTRALE"}',
                'status' => true,
            ],
            [
                'id' => 3,
                'name' => 'SOUTH AMERICA',
                'import_id' => null,
                'translation' => '{"fr":"AMERIQUE DU SUD"}',
                'status' => true,
            ],
            [
                'id' => 4,
                'name' => 'EUROPE',
                'import_id' => null,
                'translation' => '{"fr":"EUROPE"}',
                'status' => true,
            ],
            [
                'id' => 5,
                'name' => 'NORTH AFRICA',
                'import_id' => null,
                'translation' => '{"fr":"AFRIQUE DU NORD"}',
                'status' => true,
            ],
            [
                'id' => 6,
                'name' => 'WEST AFRICA',
                'import_id' => null,
                'translation' => '{"fr":"AFRIQUE DE L\'OUEST"}',
                'status' => true,
            ],
            [
                'id' => 7,
                'name' => 'CENTRAL AFRICA',
                'import_id' => null,
                'translation' => '{"fr":"AFRIQUE CENTRALE"}',
                'status' => true,
            ],
            [
                'id' => 8,
                'name' => 'SOUTHERN AFRICA',
                'import_id' => null,
                'translation' => '{"fr":"AFRIQUE AUSTRALE"}',
                'status' => true,
            ],
            [
                'id' => 9,
                'name' => 'EAST AFRICA',
                'import_id' => null,
                'translation' => '{"fr":"AFRIQUE DE L\'EST"}',
                'status' => true,
            ],
            [
                'id' => 10,
                'name' => 'ASIA',
                'import_id' => null,
                'translation' => '{"fr":"ASIE"}',
                'status' => true,
            ],
            [
                'id' => 11,
                'name' => 'OCEANIA',
                'import_id' => null,
                'translation' => '{"fr":"OCEANIE"}',
                'status' => true,
            ],
            [
                'id' => 12,
                'name' => 'MIDDLE EAST',
                'import_id' => null,
                'translation' => '{"fr":"MOYEN ORIENT"}',
                'status' => true,
            ],
        ];

        foreach ($regions as $region) {
            Region::updateOrCreate(['id' => $region['id']], $region);
        }
    }
}
