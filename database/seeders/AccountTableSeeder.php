<?php
namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AccountTableSeeder extends Seeder
{
    public function run(): void
    {
        $now = now();
        $account_types = [
            [ 'name' => 'particular',   'created_at' => $now ],
            [ 'name' => 'professional', 'created_at' => $now ],
            [ 'name' => 'other',        'created_at' => $now ],
        ];

        if (DB::table('account_types')->count() === 0) {
            DB::table('account_types')->upsert($account_types, ['name']);
        }

        // Get type ids
        $typeIds = DB::table('account_types')->pluck('id', 'name');

        $account_categories = [
            [ 'name' => 'artisan_installer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'local_installer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'regional_installer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'national_installer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'engineering_office', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'electrician', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'engineer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'architect', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'real_estate_developer', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'individual_home_builder', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'electric_car_dealership', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'national_electricity_company', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'private_electricity_company', 'account_type_id' => $typeIds['professional'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'other', 'account_type_id' => $typeIds['professional'], 'is_other' => true, 'created_at' => $now ],
            [ 'name' => 'school', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'university', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'higher_education', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'training_center', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'national_technical_institute', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'association', 'account_type_id' => $typeIds['other'], 'is_other' => false, 'created_at' => $now ],
            [ 'name' => 'other', 'account_type_id' => $typeIds['other'], 'is_other' => true, 'created_at' => $now ],
        ];

        if (DB::table('account_categories')->count() === 0) {
            DB::table('account_categories')->upsert($account_categories, ["name", "account_type_id"]);
        }
    }
}
