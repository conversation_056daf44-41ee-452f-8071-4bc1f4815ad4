<?php

namespace Database\Seeders\Testing;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // run only in testing environment
        if (app()->environment() !== 'testing') {
            return;
        }

        DB::table('users')->upsert([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ], ['email'], ['name', 'email']);

    }
}
