<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContactPlatformSeeder extends Seeder
{
    public function run(): void
    {
        $now = now();

        $contact_platforms = [
            [
                'key' => 'fb',
                'label' => 'facebook',
                'type' => 'social',
                'created_at' => $now,
            ],
            [
                'key' => 'linkedin',
                'label' => 'linkedin',
                'type' => 'social',
                'created_at' => $now,
            ],
            [
                'key' => 'x-tweet',
                'label' => 'x',
                'type' => 'social',
                'created_at' => $now,
            ],
            [
                'key' => 'instagram',
                'label' => 'instagram',
                'type' => 'social',
                'created_at' => $now,
            ],
            [
                'key' => 'pinterest',
                'label' => 'pinterest',
                'type' => 'social',
                'created_at' => $now,
            ],
            [
                'key' => 'whatsapp',
                'label' => 'whatsapp',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'wechat',
                'label' => 'wechat',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'messenger',
                'label' => 'messenger',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'telegram',
                'label' => 'telegram',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'discord',
                'label' => 'discord',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'signal',
                'label' => 'signal',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'viber',
                'label' => 'viber',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'line',
                'label' => 'line',
                'type' => 'messenger',
                'created_at' => $now,
            ],
            [
                'key' => 'website',
                'label' => 'website',
                'type' => 'network',
                'created_at' => $now,
            ],
        ];

        if (DB::table('contact_platforms')->count() === 0) {
            DB::table('contact_platforms')->upsert($contact_platforms, ['key']);
        }
    }
}
