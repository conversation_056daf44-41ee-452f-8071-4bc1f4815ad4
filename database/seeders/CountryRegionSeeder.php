<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Country;

class CountryRegionSeeder extends Seeder
{
    public function run(): void
    {
        $map = [
            1 => [ // NORTH AMERICA (incl. Caribbean)
                'United States', 'Canada', 'Greenland', 'Bermuda',
                'Saint Pierre and Miquelon', 'Mexico', 'Anguilla',
                'Antigua and Barbuda', 'Aruba', 'Bahamas', 'Barbados',
                'British Virgin Islands', 'Cayman Islands', 'Cuba', 'Dominica',
                'Dominican Republic', 'Grenada', 'Guadeloupe', 'Haiti',
                'Jamaica', 'Martinique', 'Montserrat', 'Puerto Rico',
                'Saint Bart<PERSON>lemy', 'Saint Kitts and Nevis', 'Saint Lucia',
                'Saint Martin', 'Saint Vincent and the Grenadines',
                'Trinidad and Tobago', 'Turks and Caicos Islands',
                'Curaçao', 'Caribbean Netherlands', 'United States Virgin Islands',
                'Sint Maarten',
            ],
        
            2 => [ // CENTRAL AMERICA
                'Belize', 'Costa Rica', 'El Salvador', 'Guatemala',
                'Honduras', 'Nicaragua', 'Panama',
            ],
        
            3 => [ // SOUTH AMERICA
                'Argentina', 'Bolivia', 'Brazil', 'Chile', 'Colombia', 'Ecuador',
                'Guyana', 'Paraguay', 'Peru', 'Suriname', 'Uruguay', 'Venezuela',
                'French Guiana', 'Falkland Islands', 'South Georgia',
            ],
        
            4 => [ // EUROPE
                'Albania', 'Andorra', 'Austria', 'Belgium', 'Bosnia and Herzegovina',
                'Bulgaria', 'Croatia', 'Cyprus', 'Czechia', 'Denmark', 'Estonia',
                'Finland', 'France', 'Germany', 'Greece', 'Hungary', 'Iceland',
                'Ireland', 'Italy', 'Latvia', 'Lithuania', 'Luxembourg', 'Malta',
                'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia', 'Norway',
                'Poland', 'Portugal', 'Romania', 'San Marino', 'Serbia', 'Slovakia',
                'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine',
                'United Kingdom', 'Vatican City', 'Kosovo',
                'Gibraltar', 'Isle of Man', 'Jersey', 'Guernsey',
                'Åland Islands', 'Svalbard and Jan Mayen', 'Belarus', 'Moldova',
                'Faroe Islands', 'Bouvet Island', 'Liechtenstein', 'Russia',
            ],
        
            5 => [ // NORTH AFRICA
                'Algeria', 'Egypt', 'Libya', 'Morocco', 'Sudan', 'Tunisia',
                'Western Sahara',
            ],
        
            6 => [ // WEST AFRICA
                'Benin', 'Burkina Faso', 'Cape Verde', 'Ivory Coast', 'Gambia', 'Ghana',
                'Guinea', 'Guinea-Bissau', 'Liberia', 'Mali', 'Mauritania', 'Niger',
                'Nigeria', 'Senegal', 'Sierra Leone', 'Togo',
                'Saint Helena, Ascension and Tristan da Cunha',
            ],
        
            7 => [ // CENTRAL AFRICA
                'Cameroon', 'Central African Republic', 'Chad', 'DR Congo',
                'Equatorial Guinea', 'Gabon', 'Republic of the Congo',
                'São Tomé and Príncipe',
            ],
        
            8 => [ // SOUTHERN AFRICA
                'Angola', 'Botswana', 'Eswatini', 'Lesotho', 'Malawi',
                'Mozambique', 'Namibia', 'South Africa', 'Zambia', 'Zimbabwe',
                'French Southern and Antarctic Lands',
            ],
        
            9 => [ // EAST AFRICA
                'Burundi', 'Comoros', 'Djibouti', 'Eritrea', 'Ethiopia', 'Kenya',
                'Madagascar', 'Mauritius', 'Rwanda', 'Seychelles', 'Somalia',
                'South Sudan', 'Tanzania', 'Uganda', 'Mayotte', 'Réunion',
                'British Indian Ocean Territory',
            ],
        
            10 => [ // ASIA
                'Afghanistan', 'Armenia', 'Azerbaijan', 'Bangladesh', 'Bhutan',
                'Brunei', 'Cambodia', 'China', 'Georgia', 'India', 'Indonesia',
                'Japan', 'Kazakhstan', 'Kyrgyzstan', 'Laos', 'Malaysia',
                'Maldives', 'Mongolia', 'Myanmar', 'Nepal', 'North Korea',
                'Pakistan', 'Philippines', 'Singapore', 'South Korea', 'Sri Lanka', 'Taiwan',
                'Tajikistan', 'Thailand', 'Timor-Leste', 'Turkmenistan',
                'Uzbekistan', 'Vietnam', 'Hong Kong', 'Macau',
                'Cocos (Keeling) Islands', 'Christmas Island',
            ],
        
            11 => [ // OCEANIA
                'Australia', 'Fiji', 'Kiribati', 'Marshall Islands', 'Micronesia',
                'Nauru', 'New Zealand', 'Palau', 'Papua New Guinea', 'Samoa',
                'Solomon Islands', 'Tonga', 'Tuvalu', 'Vanuatu',
                'American Samoa', 'Guam', 'Northern Mariana Islands',
                'Cook Islands', 'Niue', 'Tokelau', 'New Caledonia',
                'French Polynesia', 'Wallis and Futuna', 'Norfolk Island',
                'Pitcairn Islands', 'United States Minor Outlying Islands',
                'Antarctica', 'Heard Island and McDonald Islands',
            ],
        
            12 => [ // MIDDLE EAST
                'Bahrain', 'Iran', 'Iraq', 'Israel', 'Jordan', 'Kuwait',
                'Lebanon', 'Oman', 'Palestine', 'Qatar', 'Saudi Arabia',
                'Syria', 'Turkey', 'United Arab Emirates', 'Yemen',
            ],
        ];
        
        
        

        foreach ($map as $regionId => $countries) {
            Country::whereIn('name', $countries)->update(['region_id' => $regionId]);
        }
    }
}
