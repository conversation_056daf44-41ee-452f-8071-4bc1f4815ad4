<?php
/**
 * JUST FOR TEST
 * PHP 8.0+
 * Rôle: Récupère la liste des langues et leurs traductions d'une API
 * puis écrit chaque langue dans: ./lang/<iso>/<iso>.php au format:
 * <?php return [ ... ];
 *
 * Config requise:
 *   - Variable d'environnement TRANSLATION_API_URL
 *     ex: export TRANSLATION_API_URL="https://example.com/api"
 * 
 */
declare(strict_types=1);

// get list of app

/* ===================== Config ===================== */
$appId = 1;
$translationUrl = "https://dev.ts.solar-control.energy";
$appUrl = "http://localhost:5500";
if ($translationUrl === '') {
    fwrite(STDERR, "Erreur: TRANSLATION_API_URL non défini.\n");
    exit(1);
}

$interfaceTranslationUrl = rtrim($translationUrl, "/") . "/interface-translation-value/json/{$appId}";
$langBasePath = __DIR__ . '/../lang';     // dossier racine de sortie
$limit = 50;

/* ===================== HTTP helper ===================== */
function http_get_json(string $url, array $query = []): array {
    $ch = curl_init();
    if (!empty($query)) {
        $url .= (str_contains($url, '?') ? '&' : '?') . http_build_query($query);
    }
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_CONNECTTIMEOUT => 15,
        CURLOPT_TIMEOUT        => 60,
        CURLOPT_HTTPHEADER     => ['Accept: application/json'],
    ]);
    $raw = curl_exec($ch);
    if ($raw === false) {
        $err = curl_error($ch);
        curl_close($ch);
        throw new RuntimeException("HTTP error: {$err}");
    }
    $code = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
    curl_close($ch);
    if ($code < 200 || $code >= 300) {
        throw new RuntimeException("HTTP status {$code} pour {$url}");
    }
    /** @var mixed $decoded */
    $decoded = json_decode($raw, true);
    if (!is_array($decoded)) {
        throw new RuntimeException("Réponse JSON invalide depuis {$url}");
    }
    return $decoded;
}

/* ============ Export helper: short array syntax ============ */
/**
 * Convertit un tableau PHP en texte PHP avec syntaxe courte: [ ... ]
 */
function export_short_array(array $data): string {
    $export = var_export($data, true);
    // Remplacer "array (" par "["
    $export = preg_replace("/^([ ]*)array \\(/m", '$1[', $export);
    // Remplacer ")" fin de bloc par "]"
    $export = preg_replace("/\\)(,)?$/m", ']$1', $export);
    // Normaliser les espaces
    return $export ?? var_export($data, true);
}

/* ===================== FS helpers ===================== */
function ensure_dir(string $path): void {
    if (!is_dir($path) && !mkdir($path, 0777, true) && !is_dir($path)) {
        throw new RuntimeException("Impossible de créer le dossier: {$path}");
    }
}

function write_lang_file(string $iso2, array $payload, string $basePath, $appId): void {
    $iso2 = strtolower(trim($iso2));
    $dir  = rtrim($basePath, '/\\') . DIRECTORY_SEPARATOR . $iso2;
    ensure_dir($dir);
    $file = $dir . DIRECTORY_SEPARATOR . $appId . '.php';

    // Si l'API renvoie un objet racine avec la clé "data", on peut l'ôter.
    // Sinon on prend le JSON tel quel.
    $data = $payload;
    if (array_key_exists('data', $payload) && is_array($payload['data'])) {
        $data = $payload['data'];
    }

    // Écriture du fichier au format Laravel
    $php = "<?php\nreturn " . export_short_array($data) . ";\n";
    if (file_put_contents($file, $php) === false) {
        throw new RuntimeException("Échec d'écriture: {$file}");
    }
    echo "Langue '{$iso2}' écrite: {$file}\n";
}

/* ===================== Main ===================== */
function fetch_all_languages(string $translationUrl, int $limit = 50): array {
    $offset = 0;
    $all = [];

    while (true) {
        $resp = http_get_json(rtrim($translationUrl, "/") . "/languages", [
            'limit'   => $limit,
            'offset'  => $offset,
            'orderBy' => 'id',
            'order'   => 'ASC',
        ]);

        $chunk = $resp['data'] ?? [];
        $meta  = $resp['meta'] ?? [];
        if (!is_array($chunk)) {
            throw new RuntimeException("Structure inattendue pour /languages");
        }
        $all = array_merge($all, $chunk);

        $itemCount = (int)($meta['itemCount'] ?? count($all));
        if (count($all) >= $itemCount) {
            break;
        }
        $offset += $limit;
    }
    return $all;
}

try {
    // 1) Récupère toutes les langues
    $languages = fetch_all_languages($translationUrl, $limit);
    if (empty($languages)) {
        throw new RuntimeException("Aucune langue trouvée.");
    }

    // 2) Pour chaque langue, récupérer les traductions et écrire /translation/<iso>/<iso>.php
    ensure_dir($langBasePath);

    foreach ($languages as $lang) {
        $iso2 = $lang['languageISO2'] ?? null;
        if (!$iso2 || !is_string($iso2)) {
            echo "Langue ignorée: ISO2 manquant.\n";
            continue;
        }

        $url = rtrim($GLOBALS['interfaceTranslationUrl'], "/") . "/" . rawurlencode($iso2);
        try {
            $payload = http_get_json($url);
            write_lang_file($iso2, $payload, $langBasePath, $appId);
        } catch (Throwable $e) {
            fwrite(STDERR, "Erreur sur '{$iso2}': " . $e->getMessage() . "\n");
            // On continue avec les autres langues
        }
    }

    echo "Terminé.\n";
} catch (Throwable $e) {
    fwrite(STDERR, "Erreur fatale: " . $e->getMessage() . "\n");
    exit(1);
}
