#!/bin/bash

APP_NAME="sce"
APP_KEY="base64:TIA4x01Yf6m5oszm43QVDoD91xkPAobYBsVdU4AZpm0="

LOG_FILE="/var/log/crontab/${APP_NAME}-delete-expired-accounts-cron.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: delete_expired_accounts" >> "$LOG_FILE"
response_code=$(curl -X DELETE -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://${APP_NAME}.com/cron_job/delete-expired-accounts?x_app_key=${APP_KEY}")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 5 0 * * * /home/<USER>/${APP_NAME}.com/delete-expired-accounts.sh
