 <!-- HEADER START -->
<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="fr" xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office">
  <head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="x-apple-disable-message-reformatting">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="telephone=no" name="format-detection">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap" rel="stylesheet">
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
    </style>
    <title></title>
    <!--[if (mso 16)]>
    <style type="text/css">
    a {text-decoration: none;}
    </style>
    <![endif]-->
    <!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]-->
    <!--[if gte mso 9]>
<noscript>
         <xml>
           <o:OfficeDocumentSettings>
           <o:AllowPNG></o:AllowPNG>
           <o:PixelsPerInch>96</o:PixelsPerInch>
           </o:OfficeDocumentSettings>
         </xml>
      </noscript>
<![endif]-->
    <style type="text/css">
      * { font-family: "Roboto Condensed", sans-serif !important; letter-spacing: -0.6px !important; } .rollover:hover .rollover-first { max-height: 0px !important; display: none !important; } .rollover:hover .rollover-second { max-height: none !important; display: block !important; } .rollover span { font-size: 0px; } u+.body img~div div { display: none; } #outlook a { padding: 0; } span.MsoHyperlink, span.MsoHyperlinkFollowed { color: inherit; mso-style-priority: 99; } a.es-button { mso-style-priority: 100 !important; text-decoration: none !important; } a[x-apple-data-detectors], #MessageViewBody a { color: inherit !important; text-decoration: none !important; font-size: inherit !important; font-family: inherit !important; font-weight: inherit !important; line-height: inherit !important; } .es-desk-hidden { display: none; float: left; overflow: hidden; width: 0; max-height: 0; line-height: 0; mso-hide: all; } @media only screen and (max-width:600px) { .es-p-default {} *[class="gmail-fix"] { display: none !important } p, a { line-height: 150% !important } h1, h1 a { line-height: 120% !important } h2, h2 a { line-height: 120% !important } h3, h3 a { line-height: 120% !important } h4, h4 a { line-height: 120% !important } h5, h5 a { line-height: 120% !important } h6, h6 a { line-height: 120% !important } .es-header-body p {} .es-content-body p {} .es-footer-body p {} .es-infoblock p {} h1 { font-size: 30px !important; text-align: left } h2 { font-size: 24px !important; text-align: left } h3 { font-size: 20px !important; text-align: left } h4 { font-size: 24px !important; text-align: left } h5 { font-size: 20px !important; text-align: left } h6 { font-size: 16px !important; text-align: left } .es-header-body h1 a, .es-content-body h1 a, .es-footer-body h1 a { font-size: 30px !important } .es-header-body h2 a, .es-content-body h2 a, .es-footer-body h2 a { font-size: 24px !important } .es-header-body h3 a, .es-content-body h3 a, .es-footer-body h3 a { font-size: 20px !important } .es-header-body h4 a, .es-content-body h4 a, .es-footer-body h4 a { font-size: 24px !important } .es-header-body h5 a, .es-content-body h5 a, .es-footer-body h5 a { font-size: 20px !important } .es-header-body h6 a, .es-content-body h6 a, .es-footer-body h6 a { font-size: 16px !important } .es-menu td a { font-size: 14px !important } .es-header-body p, .es-header-body a { font-size: 14px !important } .es-content-body p, .es-content-body a { font-size: 14px !important } .es-footer-body p, .es-footer-body a { font-size: 14px !important } .es-infoblock p, .es-infoblock a { font-size: 12px !important } .es-m-txt-c, .es-m-txt-c h1, .es-m-txt-c h2, .es-m-txt-c h3, .es-m-txt-c h4, .es-m-txt-c h5, .es-m-txt-c h6 { text-align: center !important } .es-m-txt-r, .es-m-txt-r h1, .es-m-txt-r h2, .es-m-txt-r h3, .es-m-txt-r h4, .es-m-txt-r h5, .es-m-txt-r h6 { text-align: right !important } .es-m-txt-j, .es-m-txt-j h1, .es-m-txt-j h2, .es-m-txt-j h3, .es-m-txt-j h4, .es-m-txt-j h5, .es-m-txt-j h6 { text-align: justify !important } .es-m-txt-l, .es-m-txt-l h1, .es-m-txt-l h2, .es-m-txt-l h3, .es-m-txt-l h4, .es-m-txt-l h5, .es-m-txt-l h6 { text-align: left !important } .es-m-txt-r img, .es-m-txt-c img, .es-m-txt-l img { display: inline !important } .es-m-txt-r .rollover:hover .rollover-second, .es-m-txt-c .rollover:hover .rollover-second, .es-m-txt-l .rollover:hover .rollover-second { display: inline !important } .es-m-txt-r .rollover span, .es-m-txt-c .rollover span, .es-m-txt-l .rollover span { line-height: 0 !important; font-size: 0 !important; display: block } .es-spacer { display: inline-table } a.es-button, button.es-button { font-size: 18px !important; padding: 10px 20px 10px 20px !important; line-height: 120% !important } a.es-button, button.es-button, .es-button-border { display: inline-block !important } .es-m-fw, .es-m-fw.es-fw, .es-m-fw .es-button { display: block !important } .es-m-il, .es-m-il .es-button, .es-social, .es-social td, .es-menu { display: inline-block !important } .es-adaptive table, .es-left, .es-right { width: 100% !important } .es-content table, .es-header table, .es-footer table, .es-content, .es-footer, .es-header { width: 100% !important; max-width: 600px !important } .adapt-img { width: 100% !important; height: auto !important } .es-mobile-hidden, .es-hidden { display: none !important } .es-desk-hidden { width: auto !important; overflow: visible !important; float: none !important; max-height: inherit !important; line-height: inherit !important } tr.es-desk-hidden { display: table-row !important } table.es-desk-hidden { display: table !important } td.es-desk-menu-hidden { display: table-cell !important } .es-menu td { width: 1% !important } table.es-table-not-adapt, .esd-block-html table { width: auto !important } .h-auto { height: auto !important } .es-text-8173 .es-text-mobile-size-51, .es-text-8173 .es-text-mobile-size-51 * { font-size: 51px !important; line-height: 150% !important } } @media screen and (max-width:384px) { .mail-message-content { width: 414px !important } }
      p { color: #1264a1 !important }
    </style>
  </head>
  <body>
    <div style="
        display: flex;
        justify-content: center;
        width: 100%;
    ">
        <div style="margin: auto; min-height: 470px;background-image: url({{ cdnBaseUrl }}/images/mail/bg-panel-email.png);width: 600px;background-size: cover;background-repeat: no-repeat;">


          <h1 style="padding: 10px 20px;color: #1264A1;font-size: 3.2rem;font-weight: normal;line-height: 61.2px;margin: 0; text-decoration: none;">
              {{ appName }}
            </h1>
            <div style="margin-top: 60px; display: flex; justify-content: center; width: 100%;">
                <div style="margin: auto; background-color: #ffffff;width: 90%;max-width: 445px;color: #1264a1 !important;min-height: 150px;position: relative;">
                    <div class="content" style="padding: 30px;font-size: 16.7px">
 <!-- HEADER END -->
