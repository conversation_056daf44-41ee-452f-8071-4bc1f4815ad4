<?php

namespace Tests\Feature;

use App\Models\Cart;
use App\Models\Plan;
use App\Models\User;
use App\Services\CartService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Tests\TestCase;

class CartServiceTest extends TestCase
{
    use RefreshDatabase;

    private CartService $cartService;
    private Plan $plan;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test plan and user
        $this->plan = Plan::factory()->create();
        $this->user = User::factory()->create();
        
        // Create CartService instance with mock request
        $request = new Request();
        $this->cartService = new CartService($request);
    }

    public function test_can_create_cart()
    {
        $cart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        
        $this->assertInstanceOf(Cart::class, $cart);
        $this->assertEquals($this->plan->id, $cart->plan_id);
        $this->assertEquals('US', $cart->country_id);
        $this->assertEquals('127.0.0.1', $cart->ip_address);
        $this->assertEquals('en', $cart->language_iso_2);
        $this->assertEquals(1, $cart->step_id);
        $this->assertNotNull($cart->cart_uid);
        $this->assertNull($cart->user_id);
        $this->assertNull($cart->converted_at);
    }

    public function test_can_update_cart_step()
    {
        $cart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        
        $this->cartService->updateCartStep($cart, 2);
        $cart->refresh();
        
        $this->assertEquals(2, $cart->step_id);
        
        // Test that step doesn't downgrade
        $this->cartService->updateCartStep($cart, 1);
        $cart->refresh();
        
        $this->assertEquals(2, $cart->step_id); // Should remain 2
    }

    public function test_can_convert_cart()
    {
        $cart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        $cart->update(['user_id' => $this->user->id]);
        
        $this->cartService->convertCart($cart);
        $cart->refresh();
        
        $this->assertNotNull($cart->converted_at);
        $this->assertTrue($cart->isConverted());
    }

    public function test_can_find_or_create_cart()
    {
        // First call should create a new cart
        $cart1 = $this->cartService->findOrCreateCart($this->plan->id, 'US', '127.0.0.1', 'en');
        
        // Mock cookie to simulate existing cart
        Cookie::shouldReceive('get')
            ->with(CartService::CART_COOKIE_NAME)
            ->andReturn($cart1->cart_uid);
            
        // Second call should return existing cart
        $cart2 = $this->cartService->findOrCreateCart($this->plan->id, 'US', '127.0.0.1', 'en');
        
        $this->assertEquals($cart1->id, $cart2->id);
    }

    public function test_convert_cart_for_user()
    {
        $cart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        $cart->update(['user_id' => $this->user->id]);
        
        $this->cartService->convertCartForUser($this->user->id, $this->plan->id);
        $cart->refresh();
        
        $this->assertTrue($cart->isConverted());
    }

    public function test_cleanup_old_carts()
    {
        // Create old cart
        $oldCart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        $oldCart->update(['created_at' => now()->subDays(35)]);
        
        // Create recent cart
        $recentCart = $this->cartService->createCart($this->plan->id, 'US', '127.0.0.1', 'en');
        
        $deletedCount = $this->cartService->cleanupOldCarts(30);
        
        $this->assertEquals(1, $deletedCount);
        $this->assertDatabaseMissing('carts', ['id' => $oldCart->id]);
        $this->assertDatabaseHas('carts', ['id' => $recentCart->id]);
    }
}
