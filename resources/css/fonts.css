@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Black.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Black.ttf") format("truetype");
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-BlackItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-BlackItalic.ttf") format("truetype");
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Bold.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-BoldItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-BoldItalic.ttf") format("truetype");
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-ExtraBold.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-ExtraBold.ttf") format("truetype");
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-ExtraBoldItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-ExtraBoldItalic.ttf") format("truetype");
    font-weight: 800;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-ExtraLight.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-ExtraLight.ttf") format("truetype");
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-ExtraLightItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-ExtraLightItalic.ttf") format("truetype");
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Italic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Italic.ttf") format("truetype");
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Light.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Light.ttf") format("truetype");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-LightItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Medium.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-MediumItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-MediumItalic.ttf") format("truetype");
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Regular.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-SemiBold.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-SemiBold.ttf") format("truetype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-SemiBoldItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-SemiBoldItalic.ttf") format("truetype");
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-Thin.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-Thin.ttf") format("truetype");
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto Condensed";
    src: url("@fonts/roboto-condensed/woff2/RobotoCondensed-ThinItalic.woff2") format("woff2"),
         url("@fonts/roboto-condensed/RobotoCondensed-ThinItalic.ttf") format("truetype");
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}


@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Black.ttf") format("truetype");
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-BlackItalic.ttf") format("truetype");
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-BoldItalic.ttf") format("truetype");
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-ExtraBold.ttf") format("truetype");
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-ExtraBoldItalic.ttf") format("truetype");
    font-weight: 800;
    font-style: italic;
    font-display: swap;
}


@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-ExtraLightItalic.ttf") format("truetype");
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Italic.ttf") format("truetype");
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Light.ttf") format("truetype");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-LightItalic.ttf") format("truetype");
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-MediumItalic.ttf") format("truetype");

    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Regular.ttf") format("truetype");
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-SemiBold.ttf") format("truetype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-SemiBoldItalic.ttf") format("truetype");
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-Thin.ttf") format("truetype");
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Roboto";
    src: url("@fonts/roboto/Roboto-ThinItalic.ttf") format("truetype");
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}
