.toastify {
    padding: 12px 20px;
    display: inline-block;
    box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(77, 96, 232, 0.3);
    background: white;
    position: fixed;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    border-radius: 2px;
    cursor: pointer;
    text-decoration: none;
    max-width: calc(50% - 20px);
    z-index: 2147483647;
}

.toastify.on {
    opacity: 1;
}

.toast-close {
    background: transparent;
    border: 0;
    color: white;
    cursor: pointer;
    font-family: inherit;
    font-size: 1em;
    opacity: 0.4;
    padding: 0 5px;
}

.toastify-right {
    right: 15px;
}

.toastify-left {
    left: 15px;
}

.toastify-top {
    top: -150px;
}

.toastify-bottom {
    bottom: -150px;
}

.toastify-rounded {
    border-radius: 25px;
}

.toastify-avatar {
    width: 1.5em;
    height: 1.5em;
    margin: -7px 5px;
    border-radius: 2px;
}

.toastify-center {
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    max-width: fit-content;
    max-width: -moz-fit-content;
}

@media only screen and (max-width: 360px) {
    .toastify-right, .toastify-left {
        margin-left: auto;
        margin-right: auto;
        left: 0;
        right: 0;
        max-width: fit-content;
    }
}


/* Remove default Toastify background so our custom card shows */
.toastify-unstyled.toastify {
  @apply bg-transparent shadow-none text-inherit;
}

/* Custom card container inside toast */
.toastify-unstyled .toast-card {
  @apply box-border w-[320px] max-w-[90vw] bg-white
    border border-primary_ui-50
    shadow-[0_6px_16px_rgba(0,0,0,0.12)]
    px-[14px] py-3 leading-[1.45] text-gray-800;
}

/* Header row */
.toastify-unstyled .toast-card .toast-header {
  @apply flex items-center justify-between gap-2 mb-2;
}

/* Title */
.toastify-unstyled .toast-card .toast-title {
  @apply font-bold text-[16px] text-primary_ui-50;
}

/* Badge dot */
.toastify-unstyled .toast-card .toast-badge {
  @apply w-2 h-2 bg-primary_ui-50 rounded-full shrink-0;
}

/* Message text */
.toastify-unstyled .toast-card .toast-message {
  @apply text-[16px] text-gray-700 break-words;
}

/* Links inside message */
.toastify-unstyled .toast-card .toast-message a {
  @apply text-primary_ui-50 underline;
}
.toastify-unstyled .toast-card .toast-message a:hover {
  @apply underline;
}