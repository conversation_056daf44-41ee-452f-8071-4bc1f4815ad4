// import Alpine from 'alpinejs';
// window.Alpine = Alpine;
// Alpine.start();

window.dashboardTable = function (user, subs) {
    return {
        original: [],
        visible: [],

        dropdownOpen: false,
        selectedProjectId: "",
        selectedProjectName: "",
        selectedUser: t('core.user.all'),

        profileDropdownOpen: false,
        selectedProfileName: "",


        searching: false,
        searchResult: [],

        filters: {
            id: "",
            project_name: "",
            project_type: "",
            project_item_type: "",
            created_at: "",
            created_by: "",
            updated_at: "",
        },

        sort: { field: "", asc: true },
        isLoading: true,
        page: 1,
        perPage: 10,
        totalPages: 1,

        sort: { field: "project_name", asc: true }, // default sort

        showConfirmModal: false,
        selected: null,
        allType: [],
        allItemType: [],
        allUsers: [],

        showAllMap: {},
        toggleShowAll(id) {
            this.showAllMap[id] = !this.showAllMap[id];
        },
        isExpanded(id) {
            return !!this.showAllMap[id];
        },

        async init() {
            // await fetch(`/sanctum/csrf-cookie`);
            // await fetch(`/api/test-user`);
            // await fetch(`/api/me`, {
            //     method: "GET",
            //     credentials: "include",
            //     headers: {
            //         "X-CSRF-TOKEN": this.getCsrfFromCookie(),
            //         Accept: "application/json",
            //     },
            // });

            if(user.is_sub_user) {
                this.filters.created_by = user.id;
                this.selectedUser = user.email;
            }

            await this.fetchProjectTypes();
            await this.fetchUserList();
            await this.fetchProjectItemTypes();
            await this.fetchData();
            await this.search();
        },

        getFor(project) {
            if (!project?.items?.length) {
                project.items = [
                    {
                        id: 0,
                        created_at: "",
                        updated_at: "",
                        item_name: "",
                        item_type: "",
                    },
                ];
            }
            return this.isExpanded(project.id)
                ? project.items
                : project.items.slice(0, 3);
        },

        async fetchProjectTypes() {
            const res = await fetch(`/api/projects/types`);
            const data = await res.json();
            this.allType = data?.data?.map(v => ({
                label: t(`core.profile.${v}`),
                value: v
            }));

            return this.allType;
        },

        async fetchUserList() {
            const res = await fetch(`/api/projects/users`,{
                    method: "GET",
                    credentials: "include",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": this.getCsrfFromCookie(),
                        Accept: "application/json",
                    },
                });
            const data = await res.json();
            this.allUsers = data?.data;
            return this.allUsers;
        },

        async fetchProjectItemTypes() {
            const res = await fetch(`/api/projects-item/types`);
            const data = await res.json();
            this.allItemType = data?.data?.map(v => ({
                label: t(`core.simulation_type.${v}`),
                value: v
            }));;
            return this.allItemType;
        },

        formatAddress(a) {
            return `${a.street_number ?? ""} ${a.street ?? ""} <br>
            ${a.city ?? ""}${a.country?.name ? ", " + a.country?.name : ""}`;
        },

        applyFilters() {
            this.page = 1;
            this.fetchData();
        },

        getCsrfFromCookie() {
            const match = document.cookie.match(/XSRF-TOKEN=([^;]+)/);
            return match ? decodeURIComponent(match[1]) : null;
        },

        getRowSpan(project) {
            const items = project.items || [];
            if (this.isExpanded(project.id)) {
                return items.length;
            }
            return Math.min(items.length, 3);
        },
        selectProject(id, name) {
            this.selectedProjectId = id;
            this.selectedProjectName = name;
            this.dropdownOpen = false;

            this.filters.id = id;
            this.applyFilters();
        },
        async search() {
            this.searching = true;
            const params = new URLSearchParams({
                page: 1,
                per_page: 10,
                order_by: "updated_at",
                order: "desc",
                project_name: this.filters.project_name,
                columns: ["id", "project_name"],
            });

            const url = `/api/projects?${params.toString()}`;

            try {
                const response = await fetch(url, {
                    method: "GET",
                    credentials: "include",
                    headers: {
                        "X-CSRF-TOKEN": this.getCsrfFromCookie(),
                        Accept: "application/json",
                        "Content-Type": "application/json",
                    },
                });
                const data = await response.json();

                this.searchResult = data.data || [];
            } catch (err) {
                console.error("Failed to fetch data:", err);
            } finally {
                this.searching = false;
            }
        },

        async fetchData() {
            this.isLoading = true;
            this.original = [];
            this.visible = [];
            const params = new URLSearchParams({
                page: this.page,
                per_page: this.perPage,
                order_by: this.sort.field,
                order: this.sort.asc ? "asc" : "desc",
                id: this.filters.id,
                project_type: this.filters.project_type,
                columns: [
                    "items",
                    "id",
                    "created_at",
                    "updated_at",
                    "project_name",
                    "project_type",
                    "street_number",
                    "street",
                    "city",
                ],
                items_limit: 20,
                created_at: this.filters.created_at,
                created_by: this.filters.created_by,
                updated_at: this.filters.updated_at,
                project_item_type: this.filters.project_item_type,
            });

            const url = `/api/projects?${params.toString()}`;

            try {
                const response = await fetch(url, {
                    method: "GET",
                    credentials: "include",
                    headers: {
                        "X-CSRF-TOKEN": this.getCsrfFromCookie(),
                        Accept: "application/json",
                        "Content-Type": "application/json",
                    },
                });
                const data = await response.json();

                this.original = (data.data || []).reverse();
                this.totalPages = data.meta?.last_page || 1;
                this.visible = [...this.original]; // show as-is
                this.isLoading = false;
            } catch (err) {
                console.error("Failed to fetch data:", err);
            }
        },

        toggleSort(field) {
            this.isLoading = true;
            this.original = [];
            this.visible = [];

            if (this.sort.field === field) {
                this.sort.asc = !this.sort.asc;
            } else {
                this.sort.field = field;
                this.sort.asc = true;
            }

            this.page = 1;
            // Refetch from server with new sort
            this.fetchData();
        },

        goToPage(p) {
            if (p >= 1 && p <= this.totalPages) {
                this.page = p;
                this.fetchData();
            }
        },

        prevPage() {
            if (this.page > 1) {
                this.page--;
                this.fetchData();
            }
        },

        nextPage() {
            if (this.page < this.totalPages) {
                this.page++;
                this.fetchData();
            }
        },

        formatDate(iso) {
            if (!iso) return "";
            return new Intl.DateTimeFormat("fr-FR").format(new Date(iso));
        },

        openDeleteModal(project) {
            this.selected = project;
            this.showConfirmModal = true;
        },

        confirmDelete() {
            const id = this.selected?.id;
            if (!id) return;

            fetch(`/api/projects/${id}`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": document.querySelector(
                        'meta[name="csrf-token"]'
                    )?.content,
                },
            })
                .then((res) => {
                    if (!res.ok) throw new Error();
                    this.fetchData();
                    this.showConfirmModal = false;
                    this.selected = null;
                })
                .catch(() => {
                    alert("Delete failed");
                });
        },

        sortLabel(field) {
            if (this.sort.field !== field) return "";
            return this.sort.asc ? "▲" : "▼";
        },
    };
};
