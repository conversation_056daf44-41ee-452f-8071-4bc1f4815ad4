window.createProject = function (project = {}, subs = null) {
    return {
        latitude: +project?.latitude || -18.9185,
        longitude: +project?.longitude || 47.5211,
        locationType: project?.location_type ?? "address",
        address: {
            country: project?.country ?? "",
            city: project?.city ?? "",
            street: project?.street ?? "",
            number: project?.street_number ?? "",
        },
        nameExist: false,
        isLoading: false,
        profile: project?.project_type ?? "residential",
        project_name: project?.project_name ?? "",
        project_info: project?.project_info ?? "",
        country_id: project?.country_id ?? "",
        subscription_id: subs?.id ?? "",
        error: "",
        success: "",
        showConfirmationScreen: false,
        validation: { project_info: "", project_name: "" },
        dirty: { project_info: false, project_name: false },
        fieldsDisabled: !!project?.id,
        isInfo:
            typeof window.isInfo !== "undefined"
                ? window.isInfo === true
                : false,
        showGeolocationInformation: !(project?.latitude && project?.longitude),
        savedStates: {},

        async init() {
            if (!this.fieldsDisabled) {
                const ip = "{{ getClientIpAddress() }}"; // If dynamic IP from backend needed, keep this one Blade line
                await this.updateAddressFromIp(ip);

                this.$watch("locationType", async (newType, oldType) => {
                    const keyMap = {
                        address: "address",
                        "gps-point": "gpsPoint",
                        "gps-geolocated": "gpsGeolocated",
                    };

                    const oldKey = keyMap[oldType];
                    const newKey = keyMap[newType];

                    this.savedStates[oldKey] = {
                        address: { ...this.address },
                        latitude: this.latitude,
                        longitude: this.longitude,
                    };

                    const slot = this.savedStates[newKey];
                    if (slot) {
                        this.address = { ...slot.address };
                        this.latitude = slot.latitude;
                        this.longitude = slot.longitude;
                    } else if (newType === "gps-geolocated") {
                        await this.geolocateUser();
                    }

                    this.$dispatch("map-update", {
                        lat: this.latitude,
                        lng: this.longitude,
                    });
                });
            }
        },

        async updateAddressFromIp(ip) {
            const controller = new AbortController();
            const signal = controller.signal;

            try {
                const response = await fetch(`/api/ip-location/${ip}`, {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector(
                            "meta[name=csrf-token]"
                        ).content,
                    },
                    signal: signal,
                });

                if (signal.aborted) return;
                if (!response.ok)
                    throw new Error("Network response was not ok");
                const data = await response.json();

                if (data?.country) this.address.country = data.country;
                if (data?.city) this.address.city = data.city;
                if (data?.street) this.address.street = data.street;
                if (data?.number) this.address.number = data.number;
                if (data?.lat) this.latitude = data.lat;
                if (data?.lon) this.longitude = data.lon;
            } catch (status) {
                this.address = {
                    country: "",
                    city: "",
                    street: "",
                    number: "",
                };
            }
        },

        async updateAddressFromCoords() {
            const geocoder = new google.maps.Geocoder();
            const latLng = new google.maps.LatLng(
                this.latitude,
                this.longitude
            );

            try {
                const results = await new Promise((resolve, reject) => {
                    geocoder.geocode({ location: latLng }, (res, status) => {
                        if (status === "OK") resolve(res);
                        else reject(status);
                    });
                });

                if (results[0]) {
                    this.address = {
                        country: "",
                        city: "",
                        street: "",
                        number: "",
                    };
                    results[0].address_components.forEach((component) => {
                        if (component.types.includes("country"))
                            this.address.country = component.long_name;
                        if (component.types.includes("locality"))
                            this.address.city = component.long_name;
                        if (component.types.includes("route"))
                            this.address.street = component.long_name;
                        if (component.types.includes("street_number"))
                            this.address.number = component.long_name;
                    });
                }
            } catch (status) {
                this.address = {
                    country: "",
                    city: "",
                    street: "",
                    number: "",
                };
            }
        },

        async geolocateUser() {
            if (!navigator.geolocation) return;

            this.isLoading = true;

            try {
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject);
                });

                this.latitude = position.coords.latitude;
                this.longitude = position.coords.longitude;
                await this.updateAddressFromCoords();
            } catch (error) {
                // handle geolocation errors if needed
            } finally {
                this.isLoading = false;
            }
        },

        async geocodeAddress() {
            const fullAddress =
                `${this.address.number} ${this.address.street}, ${this.address.city}, ${this.address.country}`.trim();

            if (
                !fullAddress ||
                fullAddress === ", ," ||
                (this.address.city === "" &&
                    this.address.street === "" &&
                    this.address.country === "")
            ) {
                this.latitude = -18.9185;
                this.longitude = 47.5211;
                this.isLoading = false;
                return;
            }

            const requestTimestamp = Date.now();
            this.lastGeocodeRequestTimestamp = requestTimestamp;
            this.isLoading = true;

            const geocoder = new google.maps.Geocoder();
            try {
                const results = await new Promise((resolve, reject) => {
                    geocoder.geocode(
                        { address: fullAddress },
                        (res, status) => {
                            if (status === "OK") resolve(res);
                            else reject(status);
                        }
                    );
                });

                if (this.lastGeocodeRequestTimestamp !== requestTimestamp)
                    return;

                if (results[0]) {
                    const location = results[0].geometry.location;
                    this.latitude = location.lat();
                    this.longitude = location.lng();
                }
            } catch (status) {
                // handle geocode errors
            } finally {
                if (this.lastGeocodeRequestTimestamp === requestTimestamp) {
                    this.isLoading = false;
                }
            }
        },
        async validateField(field) {
            if (field == "project_name") {
                const params = new URLSearchParams({
                    project_name: this.project_name
                });

                const url = `/api/projects/check-name?${params.toString()}`;

                try {
                    const response = await fetch(url, {
                        method: "GET",
                        headers: {
                            Accept: "application/json",
                        },
                    });
                    const data = await response.json();

                    this.nameExist = data.count > 0;
                } catch (err) {
                    console.error("Failed to check project name:", err);
                }
            }
            this.dirty[field] = true;
            this.validateForm();
        },

        validateForm(markAllDirty) {
            let valid = true;

            if (markAllDirty) {
                this.dirty.project_info = true;
                this.dirty.project_name = true;
            }

            this.validation.project_info =
                !this.project_info && this.dirty.project_info
                    ? "This field is required."
                    : "";
            this.validation.project_name =
                !this.project_name && this.dirty.project_name
                    ? "This field is required."
                    : "";

            if (!this.project_info) valid = false;
            if (!this.project_name) valid = false;


            if(this.nameExist){
                this.validation.project_name = "Project name already exists.";
                valid = false;
            }

            return valid;
        },

        canCreate() {
            return this.subscription_id && !this.isLoading && !this.fieldsDisabled;
        },

        async saveProject() {
            if (!this.canCreate()) {
                return;
            }

            this.isLoading = true;
            this.error = "";
            this.success = "";

            const payload = {
                project_name: this.project_name,
                project_info: this.project_info,
                project_type: this.profile,
                location_type: this.locationType,
                latitude: this.latitude,
                longitude: this.longitude,
                street_number: this.address.number,
                street: this.address.street,
                city: this.address.city,
                country: this.address.country,
                country_id: this.country_id,
                subscription_id: this.subscription_id,
            };

            try {
                const response = await fetch("/api/projects", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": document.querySelector(
                            "meta[name=csrf-token]"
                        ).content,
                        Accept: "application/json",
                    },
                    body: JSON.stringify(payload),
                });

                const data = await response.json();

                if (!response.ok) {
                    this.error =
                        data.message ||
                        (data.errors
                            ? Object.values(data.errors).flat().join(", ")
                            : "Unknown error");
                    this.isLoading = false;
                    return;
                }

                this.success = "Project created successfully!";
                console.log(data);
                window.location.href = "/project-info/" + data?.data?.id;
            } catch (e) {
                this.error = "Failed to create project?.";
                this.isLoading = false;
            }
        },
    };
};
