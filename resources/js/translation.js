const rtlLanguages = [
  "ar",
  "iw",
  "fa",
  "ur",
  "yi",
  "dv",
  "ps",
  "sd",
];

function getCurrentLanguage() {
  return window.env.CURRENT_LANGUAGE || 'en';
}

async function translateUrl(urls, currentLanguage) {
  if (urls.length === 0 ) return urls;

  const { TRANSLATION_API_URL,APP_URL } = window.env;

  const urlStr = urls.map((url) => decodeURIComponent(url.replace(APP_URL, "").replace(/#/g, "")));

  try {
    const encodedUrlStr = encodeURIComponent(JSON.stringify(urlStr || []));
    const response = await fetch(
      `${TRANSLATION_API_URL}/translation/${currentLanguage}/translateUrls?v=${window.env.RESOURCE_VERSION}&url=${encodedUrlStr}`,
      {
        method: "GET",
        cache: "force-cache",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.status === 200||response.status === 201) {
      return (await response.json()).map(r=>r.path);
    }
  } catch (ex) { }
  return [];
}

async function translateImageUrls(urls, currentLanguage) {
  const imagePattern = /^https:\/\/cdn\.pvgis\.com\/images\/(.+)\/([^/]+)\/(\d+)-([^/]+)\.(jpg|png|gif|webp|jpeg|svg|bmp)(\?.*)?$/i;

  // Group URLs by folder
  const folderGroups = new Map();

  urls.forEach(url => {
    const match = url.match(imagePattern);
    if (match) {
      const folderName = match[1];
      match[3];
      const imageName = `${match[3]}-${match[4]}.${match[5]}`;

      if (!folderGroups.has(folderName)) {
        folderGroups.set(folderName, new Set());
      }
      folderGroups.get(folderName).add(imageName);
    }
  });

  // old translation url image process
  if (folderGroups.size === 0) {
    return new Map(urls.map(url => [url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`)]));
  }

  try {
    const response = await fetch(`${window.env.CDN_URL}/translate_url_image.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        folder: Array.from(folderGroups.entries()).map(([name, images]) => ({
          name,
          images: Array.from(images)
        })),
        lang: currentLanguage
      })
    });

    if (response.ok) {
      const data = await response.json();
      const urlMap = new Map();

      // Create a map of original URLs to translated URLs
      urls.forEach(url => {
        const match = url.match(imagePattern);
        if (match) {
          const folderName = match[1];
          const imageId = match[3];

          const resFolder = data?.find(folder => folder.name === folderName);
          const translatedUrl = resFolder?.images?.find(image => image.id === imageId);

          if (translatedUrl?.url) {
            urlMap.set(url, `${translatedUrl.url}?v=${window.env.RESOURCE_VERSION}`);
          } else {
            urlMap.set(url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`));
          }
        } else {
          urlMap.set(url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`));
        }
      });

      return urlMap;
    }
  } catch (error) {
    console.error('Error translating image URLs:', error);
  }

  // If request fails, return a map with just language updates
  return new Map(urls.map(url => [url, url.replace(/\/[a-z]{2,3}\//, `/${currentLanguage}/`)]));
}

async function applyURLTranslation(
  currentLanguage = getCurrentLanguage(),
) {
  try {
    const cmsLink = document.querySelectorAll("li[data-cms-link] a, a[data-cms-link]");
    const translatedUrl = await translateUrl(Array.from(cmsLink.values()).map(link => link.href), currentLanguage);

    translatedUrl.forEach((url, i) => {
      if (url !== "/not-found" && url !== "/undefined") {
        cmsLink[i].href = url;
      }
    });

    const imgWithCmsLink = document.querySelectorAll("img[data-cms-link]");
    const imageUrls = Array.from(imgWithCmsLink).map(img => {
      const src = img.getAttribute('src') && img.getAttribute('src') != `${window.env.CDN_URL}/images/loading-ps.svg` ?
        img.getAttribute('src') : img.getAttribute('data-src');
      return src;
    }).filter(Boolean);

    if (imageUrls.length > 0) {
      const translatedUrls = await translateImageUrls(imageUrls, currentLanguage);

      imgWithCmsLink.forEach(img => {
        const src = img.getAttribute('src') && img.getAttribute('src') != `${window.env.CDN_URL}/images/loading-ps.svg` ?
          img.getAttribute('src') : img.getAttribute('data-src');

        if (src && translatedUrls.has(src)) {
          const translatedSrc = translatedUrls.get(src);
          img.setAttribute('src', translatedSrc);
          if (img.hasAttribute('data-src')) {
            img.setAttribute('data-src', translatedSrc);
          }
        }
      });
    }
  } catch (error) {
    console.error("Error in applyURLTranslation:", error);
  } finally {
    // hideLoader();
  }
}

function parseTemplate(template, data) {
  if (!template) return "";

  if (typeof template === 'object') {
    for (let key of Object.keys(template)) {
      template[key] = parseTemplate(template[key], data);
    }

    return template;
  }

  return template.replace(/(\{\{(.+?)\}\})|(\$\{(.+?)\})|(\{(.+?)\})/g, (match) => {
    // Extract the variable name
    if (match.startsWith('${')) {
      match = match.slice(2, -1); // ${var}
    } else if (match.startsWith('{{')) {
      match = match.slice(2, -2); // {{var}}
    } else {
      match = match.slice(1, -1); // {var}
    }

    // Resolve the variable from the data object
    let keys = match.split('.');
    let value = data;

    for (let key of keys) {
      if (value && (typeof value === 'object' || Array.isArray(value)) && key in value) {
        value = value[key];
      } else {
        return '-';
      }
    }

    return value !== undefined ? value : '-';
  });
}
function getValueTranslation (object, propertyPath) {
  const propertyNames = propertyPath.split(".");
  const result = (
    propertyNames.reduce(function (obj, key) {
      return obj && obj[key];
    }, object) ?? propertyPath
  );

  return result
};
function applyTranslation(
  translation
) {
  const elements = document.querySelectorAll("[data-ts]");

  elements.forEach(element => {
    const tsValue = element.getAttribute("data-ts");
    const type = element.getAttribute("data-tstype") || "innerHTML";
    let variable = element.getAttribute("data-tsvar");

    let translated = getValueTranslation(translation, tsValue);
    if (variable) {
      variable = JSON.parse(variable);
      translated = parseTemplate(translated, variable);
    }

    element[type] = translated;
  });
}

function isRtlLanguage(lang) {
  if (rtlLanguages.includes(lang)) {
    document.documentElement.setAttribute("dir", "rtl");
    return true;
  } else {
    document.documentElement.setAttribute("dir", "ltr");
    return false;
  }
}

async function changeLanguage(lang) {
    Alpine?.store('global')?.showLoading()

    isRtlLanguage(lang);

  // loading
  const interfaceTranslationUrl =
    `${env.TRANSLATION_API_URL}/interface-translation-value/json/1/${lang}`;

  const updateLanguageUrl = `/lang/${lang}`;

  try {
    await fetch(updateLanguageUrl, {
      method: 'GET',
      headers: { Accept: 'application/json' }
    });
    const response = await fetch(
      interfaceTranslationUrl
    );

    const translations = await response.json();
    applyTranslation(translations);

  } catch (err) {
    console.error('Failed to check email:', err);
  } finally {
    // hideLoader();
    Alpine?.store('global')?.hideLoading()
  }
}

window.changeLanguage = changeLanguage;

document.addEventListener('DOMContentLoaded', function () {
  applyURLTranslation();
});

/**
 * -----------------------------------------------------------------------------
 * Client-Side Translator
 *
 * This script provides a global `t()` function for translating messages by
 * fetching JSON language files from a translation API.
 * -----------------------------------------------------------------------------
 */
(function () {
  // Static cache, similar to `$loadedLocaleFiles` in the PHP version.
  // It's kept in this closure to be private.
  const loadedLocaleFiles = {};

  /**
   * Translates the given message by fetching a JSON language file.
   *
   * This function is asynchronous and returns a Promise.
   *
   * @param {string} key The translation key (e.g., 'welcome', 'messages.new').
   * @param {object} [replace={}] Placeholder replacements (e.g., { name: 'John' }).
   * @param {string|null} [locale=null] The target locale ID (e.g., '1' for English).
   * @returns {Promise<string>} A promise that resolves to the translated string.
   */
  async function t(key, replace = {}, locale = null) {
    // --- 1. Determine Locale and App ID ---
    // These must be provided globally by your server-side template.
    const effectiveLocale = locale ?? window.getAppLocale();
    const appId = window.APP_ID ?? 0;

    if (!effectiveLocale) {
      console.error("Translator: Locale could not be determined.");
      return key;
    }

    const cacheKey = `${appId}_${effectiveLocale}`;

    // --- 2. Fetch translations if not already cached ---
    if (!loadedLocaleFiles.hasOwnProperty(cacheKey)) {
      const baseApiUrl = import.meta.env.VITE_TRANSLATION_API_URL;

      if (!baseApiUrl) {
        console.error("Translator: Translation API URL is not configured.");
        loadedLocaleFiles[cacheKey] = "::API_ERROR::";
      } else {
        const fullApiUrl = `${baseApiUrl}/interface-translation-value/json/${appId}/${effectiveLocale}`;
        console.debug(`Translator: Fetching from ${fullApiUrl}`);

        try {
          const response = await fetch(fullApiUrl, {
            headers: { Accept: "application/json" },
          });

          if (response.ok) {
            const translations = await response.json();
            if (typeof translations === "object" && translations !== null) {
              loadedLocaleFiles[cacheKey] = translations;
            } else {
              console.warn(
                `Translator: Expected JSON object from ${fullApiUrl}, but received ${typeof translations}.`
              );
              loadedLocaleFiles[cacheKey] = "::API_ERROR::";
            }
          } else if (response.status === 404) {
            console.info(
              `Translator: Locale file not found for app '${appId}', locale '${effectiveLocale}'.`
            );
            loadedLocaleFiles[cacheKey] = "::NOT_FOUND::";
          } else {
            console.error(
              `Translator: API error for app '${appId}', locale '${effectiveLocale}'. Status: ${response.status}`
            );
            loadedLocaleFiles[cacheKey] = "::API_ERROR::";
          }
        } catch (error) {
          console.error(
            `Translator: Network or request exception for app '${appId}', locale '${effectiveLocale}':`,
            error
          );
          loadedLocaleFiles[cacheKey] = "::API_ERROR::";
        }
      }
    }

    // --- 3. Retrieve translation from cache ---
    const currentTranslations = loadedLocaleFiles[cacheKey];

    // Handle cases where fetching failed or no translations exist
    if (
      typeof currentTranslations !== "object" ||
      currentTranslations === null
    ) {
      return key;
    }

    // --- 4. Traverse the translation object using the key ---
    const segments = key.split(".");
    let value = currentTranslations;

    for (const segment of segments) {
      if (
        value &&
        typeof value === "object" &&
        value.hasOwnProperty(segment)
      ) {
        value = value[segment];
      } else {
        // Segment not found, return the original key
        return key;
      }
    }

    // If the final value is an object, the key was incomplete (e.g., 'messages')
    if (typeof value === "object" && value !== null) {
      return key;
    }

    // --- 5. Replace placeholders ---
    let translatedString = String(value);
    if (Object.keys(replace).length > 0) {
      for (const placeholder in replace) {
        // Using a global replaceAll to handle multiple occurrences
        translatedString = translatedString.replaceAll(
          `{{${placeholder}}}`,
          replace[placeholder]
        );
      }
    }

    return translatedString;
  }

  // Expose the `t` function to the global scope
  window.t = t;
})();
