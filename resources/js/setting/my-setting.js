window.mySettings = function (userList) {
    return {
        users: [],
        confirmOpen: false,
        confirming: false,
        pendingId: null,
        pendingIdx: null,

        init() {
            this.users = userList;
        },
        async checkEmail(email) {
            let mailExist = false;
            const params = new URLSearchParams({
                email: email,
            });

            const url = `/api/check-mail?${params.toString()}`;

            try {
                const response = await fetch(url, {
                    method: "GET",
                    headers: {
                        Accept: "application/json",
                    },
                });
                const data = await response.json();
                mailExist = data.count > 0;
            } catch (err) {
                console.error("Failed to check email:", err);
            }
            return mailExist;
        },

        async validateInput(idx) {
            let isValid = true;
            const user = this.users[idx];
            //mail
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailPattern.test(user.email)) {
                this.users[idx].email_error = true;
                isValid = false;
            } else {
                this.users[idx].email_error = false;
                const mailExist = await this.checkEmail(user.email)
                this.users[idx].email_exist_error = false;
                if (mailExist) {
                    this.users[idx].email_exist_error = true;
                    isValid = false;
                }
            }
            //pass
            if (!user.password || user.password.length == 0) {
                this.users[idx].password_error = true;
                isValid = false;
            } else {
                this.users[idx].password_error = false;
            }

            return isValid;
        },

        async saveUser(idx) {
            this.users[idx].loading = true;
            if (!(await this.validateInput(idx))) {
                this.users[idx].loading = false;
                return;
            }
            const url = "/api/sub-users";
            console.log(this.users[idx]);

            const response = await fetch(url, {
                method: "POST",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": this.getCsrfFromCookie(),
                    Accept: "application/json",
                },
                body: JSON.stringify({
                    name: this.users[idx]?.email?.split("@")[0] || "",
                    email: this.users[idx]?.email || "",
                    password: this.users[idx]?.password || "",
                }),
            });
            const data = await response.json();

            this.users[idx] = data.user
            this.users[idx].active = true
            this.users[idx].loading = false;
        },
        askRemove(id, idx) {
            this.pendingId = id;
            this.pendingIdx = idx;
            this.confirmOpen = true;
            this.$nextTick(() => this.$refs.confirmCancel?.focus());
        },

        async confirmRemove() {
            if (!this.pendingId) return;
            this.confirming = true;
            const url = `/api/sub-users/${this.pendingId}`;
            const response = await fetch(url, {
                method: 'DELETE',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.getCsrfFromCookie(),
                    'Accept': 'application/json',
                },
            });
            this.confirming = false;

            if (!response.ok) {
                console.error('Failed to delete user:', response.statusText);
                this.closeConfirm();
                return;
            }

            // Reset the removed user slot
            this.users[this.pendingIdx] = { id: null, name: '', email: '', password: '', active: false };

            this.closeConfirm();
        },

        closeConfirm() {
            this.confirmOpen = false;
            this.pendingId = null;
            this.pendingIdx = null;
        },
        async resetPassword(idx) {
            this.users[idx].password = "";
        },
        getCsrfFromCookie() {
            // const match = document.cookie.match(/XSRF-TOKEN=([^;]+)/);
            // return match ? decodeURIComponent(match[1]) : null;
            return document.querySelector(
                        'meta[name="csrf-token"]'
                    )?.content
        },
        async toggleActive(id, val, idx) {
            if (!id) return;
            this.users[idx].loading = true;

            const url = "/api/sub-users/user-status";
            const response = await fetch(url, {
                method: "POST",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": this.getCsrfFromCookie(),
                    Accept: "application/json",
                },
                body: JSON.stringify({
                    user_id: id,
                    is_blocked: val,
                }),
            });

            const data = await response.json();

            this.users[idx].active = !val;
            this.users[idx].loading = false;
        },
        submitForm() {
            $dispatch("submit");
        },
    };
};
