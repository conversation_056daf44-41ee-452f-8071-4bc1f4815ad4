$gray: #707173;

// * {
//     font-family: "Roboto Condensed", sans-serif !important;
// }

.project-info * {
    font-size: 1.1rem;
}

.radio-blue {
    @apply text-primary_ui-50 focus:ring-primary_ui-50;
}

/* Generic form label & input */
.form-label {
    display: block;
    margin-bottom: 0.1rem;
}

.input-ui {
    @apply border border-gray-400 px-2 py-1.5
           focus:ring-primary_ui-50 border-primary_ui-50;
    height: 23px;
    min-width: 250px;
    margin-bottom: 5px;
    text-align: end;
    border-radius: 3px;
}

input:disabled {
    background-color: #F2F2F2;
    cursor: not-allowed;
    & + .cust-radio {
        background-color: #F2F2F2;
        cursor: not-allowed;
    }
}

label:has(input:disabled + .cust-radio) {
    cursor: not-allowed;
}

.location-coordinate {
    width: 100%;
    max-width: 370px;
    .input-ui {
        width: 100%;
        max-width: 310px;
    }
}

.folder-information {
    width: 100%;
    max-width: 250px;
    .input-ui {
        width: 100%;
    }
}

.profile-choice .input-label {
    color: $gray;
}
