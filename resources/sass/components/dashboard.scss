/* Dashboard visual tweaks not covered by Tailwind utility classes */
.input-filter {
    @apply font-semibold italic text-gray-500 text-sm border bg-white border-primary_ui_high-50 rounded px-2 py-1.5 w-full focus:outline-none focus:ring-2 focus:ring-primary_ui-50 focus:border-primary_ui_high-50 h-[32px] cursor-pointer text-left flex items-center justify-between whitespace-nowrap overflow-hidden text-ellipsis;
}
input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

input[type="date"]:placeholder-shown::-webkit-datetime-edit {
  color: transparent;
}

input[type="date"]::placeholder {
    color: rgba(156, 163, 175, 0.5);
    /* Tailwind's text-gray-400/50 */
}

table.project-table {
    tbody tr {
        transition: background-color .2s ease;
    }

    thead th {
        text-align: left;
    }
}

.subscription-details {
    font-weight: 400;
    font-style: italic;
    font-size: 18px;
    color: #707173;
}

/* Action icons if using Remix Icon CDN; otherwise adjust */
i[class^="ri-"] {
    font-size: 1.15rem;
    line-height: 1;
}
