.pdf-editor {
    font-family: "Roboto Condensed", Arial, sans-serif;

    h4 {
        color: #005da7;
    }

  .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    visibility: hidden;
    opacity: 0;

    .modal-info p {
      margin-top: 10px;
    }

    .modal {
      font-family: "Roboto Condensed", Arial, sans-serif;
      background-color: white;
      padding: 20px;
      z-index: 9999;
      position: absolute;
      top: 0;
      // left: 50%;
      width: 100%;
      height: fit-content;
      // transform: translateX(-50%);
      display: flex;
      gap: 16px;
      border-radius: 0;
    }
  }

  .modal-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    padding-right: 2px;

    .left-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 0.5rem;
    }

    span {
      display: inline-block;
      text-align: end;
      // width: 50px;
      padding-right: 3px;
      cursor: pointer;
    }
  }

  .modal-box-shadow {
    -webkit-box-shadow: 0px 0px 20px -3px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 0px 0px 20px -3px rgba(0, 0, 0, 0.75);
    box-shadow: 0px 0px 20px -3px rgba(0, 0, 0, 0.75);
  }

  .zoom-container {
    padding: 2px;
    float: right;
    align-items: center;
    display: flex;
    justify-content: center;
    flex-direction: row;

    .zoom-button {
      width: 30px;
      font-size: 30pt;
      color: gray;
      border-radius: 5px;
      padding: 3px;
      align-items: flex-end;
      display: flex;
      justify-content: center;
      flex-direction: row;

      &:hover {
        color: rgb(141, 135, 135);
        background: rgb(243, 240, 240);
        cursor: pointer;
      }

      h4 {
        margin-right: 10px;
      }
    }

    .zoom-left {
      margin-right: 10px;
      align-items: center;
      display: flex;
      justify-content: center;
      flex-direction: row;
    }
  }

  .organise {
    margin-right: 20px;
  }

  .preview-container {
    display: flex;
    width: 100%;
    height: 95%;
  }

  .thumbnail-wrapper {
    .fixed-select-all-container {
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 10;
      padding: 10px 4px;
      border-bottom: 1px solid #ccc;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .select-all-container {
        display: flex;
        justify-content: center;
        gap: 6px;

        label,
        input {
          margin: 0;
        }
      }

      .select-all-spacer {
        height: 40px;
        /* Adjust this value to match the height of your fixed container */
      }
    }

    .thumbnail-container {
      width: 350px;
      height: 86vh;
      overflow-y: scroll;
      overflow-x: hidden;
      position: relative;
      padding-top: 20px;
      padding-right: 20px;

      .miniPage {
        width: 70%;
        height: max-content;
        margin: 7px auto;
        box-shadow: 0 0 5px rgba(99, 98, 98, 0.507);
        cursor: grab;
        animation: all 0.3s ease;

        &.dragging,
        &.dragging * {
          cursor: grabbing !important;
        }

        canvas {
          width: 100%;
        }
      }

      .active .miniPage,
      .miniPage:hover {
        border: solid 1px #f00;
      }

      .add-page {
        // border: solid 1px #757474;
        color: rgb(49, 130, 200);
        width: 70%;
        padding: 4px;
        font-size: 12pt;
        margin: 7px auto;
        margin-top: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        justify-content: center;
        align-items: center;

        span {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .mini-page-container {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .thumbnail-action {
        display: flex;
        flex-direction: column;
        gap: 12px;

        span {
          cursor: pointer;
        }

        .disabled {
          color: gray;
          cursor: not-allowed;
        }
      }

      .not-allowed {
        cursor: not-allowed;
      }

      .thumbnail {
        position: relative;
        border: none;
        margin: 0;

        .thumbnail-title {
          text-align: center;
        }
      }
    }
  }

  .page-container {
    width: 100%;
    height: 91vh;
    overflow-y: scroll;
    border: solid 1px rgba(212, 209, 209, 0.774);
    box-shadow: 0 0 20px rgba(167, 167, 167, 0.315) inset;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    position: relative;

    .zoomer {
      width: 65%;
      margin: auto;
      transition: width 0.5s;

      .page {
        width: 100%;
        margin-bottom: 15px;

        canvas {
          box-shadow: 0 0 20px -5px rgba(39, 39, 39, 0.452);
          width: 100%;

          &.portrait {
            width: 75%;
            margin: 0 12.5%;
          }

          &.landscape {
            width: 100%;
          }
        }
      }
    }

    .file-input-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      position: absolute;
      top: 50%;
      left: 50%;
      background: rgb(255, 255, 255);
      width: 100%;
      height: 100%;
      transform: translate(-50%, -50%);
      visibility: hidden;
      opacity: 0;
      z-index: -1;

      &-visible {
        visibility: visible;
        opacity: 1;
        z-index: 1;
      }

      .file-label {
        background-color: #007bff;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
      }

      .file-input {
        display: none;
        /* Hidden by default */
      }

      .file-name {
        font-size: 14px;
        color: #333;
      }
    }

    .upload-container {
      width: 100%;
      height: 100%;

      .cancel-adding-page {
        font-size: 12pt;
        cursor: pointer;
        position: absolute;
        top: 1%;
        z-index: 100;
        right: 1%;
      }
    }
  }
}

.modal-backdrop {
  z-index: 9998;
  position: fixed;
  right: 0px;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent !important;
//   opacity: 0;
//   visibility: hidden;
  transition: 250ms all ease;

  .modal {
    background-color: white;
    border-radius: 8px;
    // padding: 20px;
    width: 480px;
    z-index: 9999;
    position: absolute;
    top: 125px;
    left: 50%;
    height: fit-content;
    transform: translateX(-50%);
    display: flex;
    gap: 16px;

    input {
      text-align: left !important;
      padding-inline: 16px;
      padding-block: 2px;
      height: auto;
    }

    &>div {
      &:last-child {
        flex-grow: 1;
      }

      .modal-info {
        margin-bottom: 16px;
      }

      .save-as-container {
        display: flex;
        flex-direction: column;
        margin-block: 14px;

        &>* {
          width: 100%;
          padding-top: 7px;
        }

        input {
          padding-bottom: 5px;
          text-overflow: ellipsis;

          &.error {
            border: solid 1px rgba(255, 55, 55, 0.911);
          }

          &.error:hover,
          &.error:active,
          &.error:focus {
            outline: solid 2px rgba(255, 0, 0, 0.4) !important;
          }

          &.error+span {
            display: inline;
          }
        }

        span {
          display: none;
          padding-top: 5px;
          line-height: 1;
          font-weight: 400;
          font-size: 16px;
          color: rgb(252, 102, 102);
          padding-inline-start: 16px;
        }
      }

      .btn {
        color: white;
        border-radius: 3px;
        background: #2e639b;
        padding: 7px 0 7px;
        text-align: center;
        width: 270px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    p,
    label {
      margin-bottom: 3px;
    }
  }

  #pdf-modal:dir(rtl) {
    transform: translate(0, 0);
  }

  #pdf-modal {
    width: 100%;
    // height: 100%;
    top: 0;
    border-radius: 0;
    // padding: 0;
  }
}

#modal-backdrop-pdf {
//   display: none;

  input {
    all: revert;
  }
}

#choose-pdf-to-load-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  &>div {
    .form-container {
      display: flex;
      justify-content: space-around;
      margin-top: 20px;

      input {
        all: revert;
      }
    }

    .column {
      display: flex;
      flex-direction: column;
      padding: 10px;
    }

    .checkbox-row {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }

    .checkbox-row label {
      margin: 0 !important;
      margin-left: 10px;
    }

    // background: red !important;
    // padding: 20px;
    transform: translateY(-50%);
    width: 50%;
    background-color: white;

    div#credits,
    h3 {
      margin-inline: 20px;
    }

    .action-container {
      display: flex;
      align-items: center;
      justify-content: end;
      margin-top: 5rem;

      button {
        bottom: 0;
        right: 0;
        border: none;
        background: #005da7;
        margin-left: 15px;
        color: white;
        width: 200px;
        height: 40px;
        /* font-size   : 18px; */
        transition: 0.3s;
        text-transform: uppercase;
        display: flex;
        align-items: center;
        justify-content: center;

        /* font-family: var(--font-bold-condensed); */
        &:hover {
          opacity: 0.8;
        }

        &.disabled {
          background: #53739c;
          cursor: not-allowed;
        }
      }

      a {
        /* font-size: 18px; */
        margin-right: 15px;
        /* font-family: var(--font-condensed); */
      }
    }

    // h3 {
    //   margin-top: 0;
    // }
  }
}

#edit-mode {
  display: none;
}

@media (max-width: 768px) {

  #edit-mode {
    display: block;
    font-size: 20px;
    cursor: pointer;
    color: gray;
    border: 1px solid rgba(128, 128, 128, 0.685);
    border-radius: 5px;
    padding: 0 7px;
  }

  #pdf-modal {
    width: 100%;
    height: 100%;
    top: 0;
    border-radius: 0;
    padding: 0;

    .pdf-editor {
      height: 100%;
      max-height: 100vh;

      >div {
        height: 100%;
        max-height: 100vh;

        >div {
          height: 100%;
          max-height: 100vh;

          .modal-info {
            height: 100%;
            max-height: 100vh;
          }
        }
      }

    }

    .modal-title {
      flex-direction: column-reverse;
      align-items: flex-start;
      gap: 10px;
      padding-block: 15px;
      height: 120px;

      .left-title {
        padding: 5px 15px;

        h4 {
          margin: 0 0 0 0;
        }
      }

      .zoom-container {
        padding: 5px 15px;
        gap: 20px;

        .organise {
          margin: 0;
          font-size: 20px;
          color: #005da7;
        }


        .zoom-button[onclick="closePdfEditorModal('editor-1')"] {
          position: fixed;
          top: 20px;
          right: 20px;
        }

        .zoom-left {

          span {
            width: 37px;
            height: 30px;
          }

          span:not(#download):not(#edit-mode) {
            display: none;
          }

          #download {
            display: flex;
            border: 1px solid rgba(128, 128, 128, 0.685);
            border-radius: 5px;

            svg {
              width: 25px;
              height: 25px;
            }
          }
        }
      }
    }

    .zoomer {
      width: 100%;
      padding: 10px;

      .page {
        display: flex;
        justify-content: center;
        margin-bottom: 25px;

        canvas {
          width: 97%;
          margin: 0;
        }
      }
    }

    .preview-container {
      position: relative;
      height: calc(100% - 120px);

      .thumbnail-wrapper {
        height: 45px;
        // visibility: collapse;
        display: none;

        &:is(.o-mode) {
          // visibility: visible;
          display: block;
          width: 100%;
          height: 100%;
          position: absolute;
          z-index: 1;

          .select-all-checkbox {
            width: 16px;
            margin-inline: 10px 0px;
            height: 16px;
          }
          .page-checkbox{
            width: 19px;
            height: 19px;
          }

          .thumbnail-container {
            width: 100%;
            padding: 0;
            background-color: #fff;

            .thumbnail {
              height: auto;

              .icon-plus {
                width: 25px;
                height: 25px;
              }

              .mini-page-container {
                padding: 15px;

                .thumbnail-action {
                  svg {
                    width: 20px;
                    height: 20px;
                  }
                }
              }
            }

          }
        }
      }

      .thumbnail-container,
      .page-container {
        height: 100%;
        max-height: calc(100vh - 120px);
        overflow-y: scroll;

        &:is(.thumbnail-container) {
          max-height: calc(100vh - 120px - 45px);
        }
      }
    }

  }
}
