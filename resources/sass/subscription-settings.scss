#changesubscription {
    .subscription-options-container{
        position: relative; padding-bottom: 25px; padding-top: 15px;
    }
    .subscription-options-disabler {
                position: absolute;
                top: 0; left: 0; right: 0; bottom: 0;
                background-color: rgba(200, 200, 200, 0.3); /* black with 50% opacity */
                z-index: 10; /* ensures it sits above other content */
                pointer-events: auto; /* default - allows it to block */
    }
}
.custom-alert {


    padding: 15px;
    border-radius: 5px;
    margin-top: 10px;
    &.custom-alert-warning {
        background-color: #ffe0cc;
        color: #ED7A2E;
        border-left: solid 2px #ED7A2E;
    }
    &.custom-alert-danger {
         background-color: #ffe0e0;
        color: #ef4444;
        border-left: solid 2px #ef4444;
    }
}
