<style class="">
    .mask-credits {
        background-color: white;
        position: absolute;
        z-index: 200;
        width: 80%;
        height: 28px;
        top: 455px;
    }

    .info-container {
        display: flex;
        align-items: flex-start;
        margin-top: 0px;
        justify-content: space-between;
    }

    .info-text {
        flex: 1;
        /* Takes up remaining space */
    }

    .info-img {
        display: flex;
        /* Flexbox to align content */
        justify-content: flex-end;
        /* Align image to the right */
    }

    .compass {
        max-width: 100%;
        /* Ensures image is responsive */
        height: auto;
        /* Maintain aspect ratio */
    }

    .info-container p {
        font-size: 12px;
        color: #1b69a7;
        font-family: var(--font-condensed);
        line-height: 3px;
    }

    .header-contents {
        display: flex;
        align-items: center;
    }

    .logos {
        width: 50px;
        height: 50px;
        margin-right: 10px;
    }

    .sub-titles {
        color: #1a73e8;
        margin-left: 20px;
        margin-top: 15px;
    }

    .sub-titles h1 {
        font-size: 24px;
        margin: 0 5px 0 0;
        /* haut, droite, bas, gauche */
        font-family: var(--font-bold-condensed);
        color: #1b69a7;
        font-size: 1.55em;
        font-weight: 300;
        line-height: 1.2;


    }

    .sub-titles p {
        font-size: 22px;
        font-family: var(--font-condensed);
        margin: -5px 0 0 0;
        color: #1b69a7;
        font-weight: 500;
    }

    .user-logo {
        height: 105px;
        width: 105px;
    }

    .user-logo img {
        object-fit: cover;
    }
</style>
