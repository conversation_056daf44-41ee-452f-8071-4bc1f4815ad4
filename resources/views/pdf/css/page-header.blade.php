<style>
    body {
        font-family: var(--font-helvetica-neue-light);
        letter-spacing: 1px;
    }

    .header-content {
        display: flex;
        flex-direction: column;
        color: white;
        text-align: end;
        font-weight: bold;
        font-size: 26px;
        padding: 10px;
    }

    .header-text {
        background-color: #0069b3;
        font-size: 24px;
    }

    h2 {
        padding: 10px 28px 0px 0px;
        font-family: var(--font-bold-condensed);
    }



    .wrapper {
        display: grid;
        grid-template-columns: 1fr 1.40fr;
        grid-gap: 10px;
        padding: 0px 10px 10px 20px;
    }


    .letter {
        display: flex;
        padding: 10px 20px 10px 20px;
        font-size: 18px;
        color: white;
    }


    .right-section {
        display: flex;
        flex-direction: column;
        gap: 0;
    }

    .letter-container {
        display: flex;
        width: 100%;
        margin-bottom: 10px;
    }

    .letter-container-last {
        margin-bottom: 2px;
    }


    .letter-container .letter {
        flex: 0 0 auto;
        text-align: start;
        background-color: #0069b3;
        color: white;
        font-family: var(--font-condensed);
        max-height: 40px;
        white-space: nowrap;
    }

    .letter-container .text {
        flex: 1;
        padding: 10px;
        font-size: 16px;
        background-color: #0069b3;
        color: white;
        text-align: end;
        font-weight: bold;
        max-height: 40px;
        white-space: nowrap;
    }

    .header-logo {
        width: 100%;
        height: auto;
    }

    .footer-logo {
        height: 105px;
        width: 105px;
        margin-right: 25px;
        margin-bottom: 20px;
    }

    .user-logo {
        height: 105px;
        width: 105px;
    }

    .user-logo img {
        object-fit: cover;
    }


    .bottom-section .letter-container .letter {
        background-color: white;
        border: 0.5px solid #5d9ecf;
        border-right: none;
        color: #0069b3;
    }

    .bottom-section .letter-container .text {
        background-color: white;
        border: 0.5px solid #5d9ecf;
        border-left: none;
        color: #0069b3;
    }


    .bottom-section .letter-container .text {
        background-color: white;
        color: #0069b3;
    }

    .flex {
        display: flex;
        /* Active le modèle flex */
        justify-content: start;
        /* Centre les éléments horizontalement */
        align-items: center;
        /* Centre les éléments verticalement */
    }

    .left-section {
        display: flex;
        justify-content: flex-end;
        margin-right: 20px;



    }

    .left-section .content h3 {
        color: #0069b3;
        font-size: 26px;
        font-family: var(--font-bold-condensed);
        text-align: end;
        line-height: 1.15;
    }

    .uppercase {
        text-transform: uppercase;
        /* Transforms text to uppercase */
    }
</style>
