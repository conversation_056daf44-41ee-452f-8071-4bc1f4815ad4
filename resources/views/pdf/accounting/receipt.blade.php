
<script src="<?= $env['CDN_URL'] ?>/assets/js/jquery.js"></script>
<script>
  var env = {
    TRANSLATION_API_URL: "<?= $env['TRANSLATION_API_URL'] ?>",
  };
  var templateVars = <?= json_encode($templateVars) ?>;
  document.querySelector('#language-selector').innerHTML = `${templateVars.language}`;
</script>
<script src="<?= $env['CDN_URL'] ?>/assets/js/translation.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', async function () {

        try {

            await setLanguage(templateVars.language);

        } catch (error) {
            console.error(error);

        } finally {

        }
    });

</script>



<div class="all-body">
  <div class="preview-body">
    <div class="header">
      <div class="logo-title">
        <img src="<?= $settings['logoUrl'] ?>" alt="">
      </div>
      <div class="title">
        <div>
          <span class="uppercase"><?= t('core.receipt') ?></span>
        </div>
      </div>
    </div>

    <div class="content">

      <span class="invoice-number"><span><?= t('core.receipt_number') ?></span> <?= $templateVars['payment']['invoiceNumber'] ?></span>
      <div class="info">
        <div class="recu-info">
          <table>
            <tr>
              <td class="text-start"><strong><span><?= t('core.print.issue_date') ?></span>:</strong></td>
              <td class="text-start"><?= date('d/m/Y', strtotime($templateVars['payment']['effectivePaymentDate'])) ?? '-' ?></td>
            </tr>
            <tr>
              <td class="text-start"><strong><span><?= t('core.print.payment_method') ?></span>:</strong></td>
              <td class="text-start"><?= $templateVars['payment']['paymentMethod'] ?? '-' ?></td>
            </tr>
            <tr>
              <td class="text-start"><strong><span><?= t('core.print.reference') ?></span>:</strong></td>
              <td class="text-start"><?= $templateVars['payment']['stripeReference'] ?? '-' ?></td>
            </tr>
             <tr>
              <td class="text-start"><strong><span><?= t('core.invoice_number') ?></span>:</strong></td>
              <td class="text-start"><?= $templateVars['payment']['invoiceNumber'] ?? '-' ?></td>
            </tr>
          </table>
        </div>

        <div class="customer-info">
          <strong class="bold uppercase"><?= t('core.pdf_invoice.customer') ?></strong><br />
          <span class="customer-contact">
            <?= $templateVars['customerInfo']['customerName'] ?><br />
            <?= $templateVars['customerInfo']['streetAddress'] ?> <?= $templateVars['customerInfo']['postalCode'] ?> <?= $templateVars['customerInfo']['city'] ?><br />
            <?= $templateVars['customerInfo']['country'] ?><br />
            <?= $templateVars['customerInfo']['email'] ?><br />
          </span>
        </div>
      </div>

      <div class="line"></div>
      <div class="copp-info">
        <p class="bold mb-10"><?= t('core.print.product') ?></p>
        <table style="width: 100%;" class="product-table">
          <thead class="table-header">
            <tr>
              <th class="bold" style="text-align: start;padding-left: 10px;width: 50%;"><?= t('core.print.description') ?></th>
              <th class="bold" style="text-align: center;"><?= t('core.print.unit_price') ?></th>
              <th class="bold" style="text-align: center;"><?= t('core.print.quantity') ?></th>
              <th class="bold" style="text-align: center;"><?= t('core.print.amount') ?></th>
            </tr>
          </thead>
          <tbody>
            <?php for ($i = 0; $i < count($templateVars['product']); $i++): ?>
            <tr>
              <td style="text-align: start;padding-left: 10px;padding-top: 20px;width: 50%;">
                <p class="bold"><?= t( $templateVars['product'][$i]['name'] ) ?></p>
                <p class="bold p-block-5"><?= t('core.print.'. $templateVars['product'][$i]['billingPeriodIntervalAdv']) ?></p>
                <ul class="product-feature">
                  <?php foreach ($templateVars['product'][$i]['productFeatures'] as $feature): ?>
                  <li>
                    <div><?= t('ui_sub_feature_'.$feature['key'] , ['user_count' =>  $templateVars['product'][$i]['userCount']  , 'project_count' =>  $templateVars['product'][$i]['creditCount']  ]) ?></div>
                  </li>
                  <?php endforeach; ?>
                </ul>
              </td>
              <td style="vertical-align: top;text-align: center;padding-top: 20px;">
                <strong><?= @money($templateVars['product'][$i]['unitPrice'], $templateVars['currencyCode']) ?></strong>
              </td>
              <td style="vertical-align: top;text-align: center;padding-top: 20px;">
                <strong><?= $templateVars['product'][$i]['quantity'] ?></strong>
              </td>
              <td style="vertical-align: top;text-align: center;padding-top: 20px;">
                <strong><?= @money($templateVars['product'][$i]['price'], $templateVars['currencyCode']) ?></strong>
              </td>
            </tr>
            <?php endfor; ?>
          </tbody>
        </table>

        <?php if (!empty($templateVars['payment']['discount']) && !empty($templateVars['payment']['discountValue'])): ?>
        <div class="reduction">
          <span><span><?= t('core.print.discount') ?></span> <?= $templateVars['payment']['discount'] ?>%</span>
          <span>-<?= @money($templateVars['payment']['discountValue'], $templateVars['currencyCode']) ?></span>
        </div>
        <?php endif; ?>

        <?php if (!empty($templateVars['payment']['accounting']['amountToRefund'])): ?>
        <div class="reduction">
          <span><span><?= t('core.print.reimbursment') ?></span></span>
          <span>-<?= @money($templateVars['payment']['accounting']['amountToRefund'], $templateVars['currencyCode']) ?></span>
        </div>
        <?php endif; ?>
      </div>

      <div class="p-payment-info">
        <div class="p-history">
          <p><strong><?= $settings["companyData"]['companyName'] ?></strong><br>
            <?= $settings["companyData"]['street'] ?><br>
            <?= $settings["companyData"]['suite'] ?><br>
            <?= $settings["companyData"]['city'] ?><br>
            <?= $settings["companyData"]['country'] ?><br>
            <?= $settings["companyData"]['email'] ?></p>
        </div>
        <div class="p-detail">
            <?php if (!empty($templateVars['payment']['discount']) && !empty($templateVars['payment']['discountValue'])): ?>
                <div class="p-detail-content bold">
                    <span><span><?= t('core.print.discount') ?></span> <?= $templateVars['payment']['discount'] ?>%</span>
                    <span>-<?= @money($templateVars['payment']['discountValue'], $templateVars['currencyCode']) ?></span>
                </div>
            <?php endif; ?>
            <?php if (!empty($templateVars['payment']['accounting']['amountToRefund'])): ?>
                <div class="p-detail-content bold">
                    <span><span><?= t('core.print.reimbursment') ?></span></span>
                    <span>-<?= @money($templateVars['payment']['accounting']['amountToRefund'], $templateVars['currencyCode']) ?></span>
                </div>
            <?php endif; ?>
            <?php if (
                (
                    isset($templateVars['payment']['accounting']['total']) &&
                    isset($templateVars['payment']['accounting']['amountPaid']) &&
                    $templateVars['payment']['accounting']['total'] == $templateVars['payment']['accounting']['amountPaid']
                )
                ): ?>
                <div class="p-detail-content bold">
                    <span><?= t('core.print.'.(($templateVars['payment']['accounting']['total']) < 0 ? 'total_refunded' : 'total_paid'))  ?></span>
                    <strong><?= @money(abs($templateVars['payment']['accounting']['total']), $templateVars['currencyCode']) ?></strong>
                </div>
            <?php // Partial Payment/Refund ?>
            <?php else: ?>
                <div class="p-detail-content bold">
                    <span><?= t('core.print.'.(($templateVars['payment']['accounting']['total']) < 0 ? 'total_to_refund' : 'total_to_pay'))  ?></span>
                    <strong><?= @money(abs($templateVars['payment']['accounting']['total'] ), $templateVars['currencyCode']) ?></strong>
                </div>

                <?php if(
                        isset($templateVars['payment']['accounting']['amountAlreadyPaid']) &&
                        abs($templateVars['payment']['accounting']['amountAlreadyPaid']) > 0
                    ): ?>
                    <div class="p-detail-content bold">
                        <span><?= t('core.print.'.(($templateVars['payment']['accounting']['total']) < 0 ? 'already_refunded' : 'amount_already_paid'))  ?></span>
                        <strong><?= @money(abs($templateVars['payment']['accounting']['amountAlreadyPaid']), $templateVars['currencyCode']) ?></strong>
                    </div>
                <?php endif; ?>
                <div class="p-detail-content bold">
                    <span><?= t('core.print.'.(($templateVars['payment']['accounting']['amountPaid']) < 0 ? 'total_refunded' : 'total_paid'))  ?></span>
                    <strong><?= @money(abs($templateVars['payment']['accounting']['amountPaid'] ?? 0), $templateVars['currencyCode']) ?></strong>
                </div>
                <?php if(
                    isset($templateVars['payment']['accounting']['remainingToPay']) &&
                    abs($templateVars['payment']['accounting']['remainingToPay']) > 0
                    ): ?>
                    <div class="p-detail-content bold">
                        <span><?= t('core.print.'.(($templateVars['payment']['accounting']['total']) < 0 ? 'remaining_to_refund' : 'remaining_to_pay'))  ?></span>
                        <strong><?= @money(abs($templateVars['payment']['accounting']['remainingToPay']), $templateVars['currencyCode']) ?></strong>
                    </div>
                <?php endif; ?>

            <?php endif; ?>



        </div>
      </div>
    </div>
  </div>
  <div class="preview-footer">
    <p><?= $settings['appName'] ?></p>
  </div>
</div>



