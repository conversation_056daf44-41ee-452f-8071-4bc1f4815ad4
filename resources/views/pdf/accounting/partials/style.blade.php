
<style>
  .text-start {
    text-align: start;
    /* font-size: 12px; */
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .text-end {
    text-align: end;
    /* font-size: 13px; */
    word-break: break-all;
    padding-top: 5px;
    padding-bottom: 5px;
    max-width: 172px !important;
  }

  .info {
    display: flex;
    align-items: stretch;
    gap: 10px;
    justify-content: space-between;
    height: max-content;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .customer-info {
    width: 300px;
    border: 2px solid <?= $settings['colorTheme']['primary'] ?>;
    padding: 5px 8px;
  }

  .customer-contact {
    line-height: 1.25;
  }

  .recu-info {
    flex-grow: 1;
    border: 2px solid <?= $settings['colorTheme']['primary'] ?>;
    padding: 5px 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .recu-info table {
    width: 100%;
    /* Ensures table takes the full width */
    border-collapse: collapse;
    /* Removes gaps between borders */
  }

  .recu-info tr {
    border-bottom: 2px solid <?= $settings['colorTheme']['primary'] ?>;
    font-weight: bold;
  }

  .recu-info tr:last-child {
    border-bottom: none;
  }

  .recu-info td {
    padding: 5px;
  }

  .recu-info tr td:first-child {
    white-space: nowrap;
    width: 130px;
  }

  .recu-info div {
    /* font-size: 18px; */
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .preview-footer {
    background-color: #dcdff1;
    color: #8d8989;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // margin-top: 10px;
    padding-block: 10px;
  }

  .preview-footer h3 {
    margin: 0;
  }

  .content {
    // margin-bottom: 40px;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: 90px;
  }

  .header h1 {
    color: #8d8989;
  }

  .logo-container {
    background-color: <?= $settings['colorTheme']['primary'] ?>;
  }

  .header .header-desc {
    margin-left: 8px;
    /* font-size: 17px; */
  }

  .preview-body {
    background: white;
    color: rgba(0, 0, 0, 0.85);
    /* font-size: 14px; */
    font-variant: tabular-nums;
    line-height: 1.15;
  }

  * {
    box-sizing: border-box;
  }

  * {
    box-sizing: border-box;
  }

  .preview-body {
    flex-grow: 1;
    color: rgba(0, 0, 0, 0.85);
    /* font-size: 14px; */
    font-variant: tabular-nums;
    line-height: 1.5715;
    background-color: #fff;
    font-feature-settings: "tnum", "tnum";
  }


  *,
  :before,
  :after {
    box-sizing: border-box;
  }

  h2 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
  }

  nz-table {
    display: block;
  }

  .ant-table-wrapper {
    clear: both;
    max-width: 100%;
  }

  .ant-table-wrapper:before {
    display: table;
    content: "";
  }

  .ant-table-wrapper:after {
    display: table;
    clear: both;
    content: "";
  }

  nz-spin {
    display: block;
  }

  .ant-spin-nested-loading {
    position: relative;
  }

  .ant-spin-container {
    position: relative;
    transition: opacity 0.3s;
  }

  .ant-spin-container:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    display: none \9;
    width: 100%;
    height: 100%;
    background: #fff;
    opacity: 0;
    transition: all 0.3s;
    content: "";
    pointer-events: none;
  }

  hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
  }

  .ant-table {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: rgba(0, 0, 0, 0.85);
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum", "tnum";
    position: relative;
    /* font-size: 14px; */
    background: #fff;
    border-radius: 2px;
  }

  nz-table-inner-default {
    display: block;
  }

  .ant-table-container {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
  }

  .ant-table .ant-table-container::before,
  .ant-table .ant-table-container::after {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    width: 30px;
    transition: box-shadow 0.3s;
    content: "";
    pointer-events: none;
  }

  .ant-table .ant-table-container::before {
    left: 0;
  }

  .ant-table .ant-table-container::after {
    right: 0;
  }

  b {
    font-weight: bolder;
  }

  h3 {
    margin-top: 0;
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
  }

  table {
    border-collapse: collapse;
  }

  .copp-info {
    padding-top: 10px;
    border: 2px solid <?= $settings['colorTheme']['primary'] ?>;
    padding: 5px 8px;
  }

  .p-payment-info {
    width: 100%;
    display: flex;
    height: fit-content;
    margin-top: 15px;
  }

  .p-history {
    width: 50%;
    padding-top: 5px;
    margin-right: 2px;
    line-height: 1.25;
  }

  .p-detail {
    min-height: 100px;
    margin-left: 7px;
    flex-grow: 1;
  }

  .p-detail-content {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    background-color: <?= $settings['colorTheme']['primary'] ?>;
    color:  <?= $settings['colorTheme']['accent'] ?>;
    padding-left: 10px;
    padding-right: 40px;
    font-weight: bold;
  }
  .p-detail-content:first-child {
    margin-top: 0;
  }

  /* .large {
    font-size: large;
  } */

  .custom-table {
    width: 100%;
  }

  .logo-title {
    color: #f2f2f2;
    /* background-color: #0065a2; */
    display: flex;
    align-items: center;
    font-size: 60px;
    font-weight: 400;
    width: max-content;
  }

  .title {
    color: <?= $settings['colorTheme']['docTypeColor'] ?> !important;
    font-size: 40px;
    font-weight: 600;
    display: flex;
    margin-right: 20px;
  }
  .invoice-number {
    font-size: 20px;
    font-weight: bold;
    text-transform: uppercase;
  }

  .logo-title a {
    text-decoration: none !important;
    color: #fff;
  }

  .header {
    /* background-color: #0065a2; */
    background-image: url(<?= $settings['backgroundImageUrl'] ?>);
    background-size: cover;
    background-repeat: no-repeat;
  }

  .table-header {
    background-color: <?= $settings['colorTheme']['primary'] ?>;
    color: <?= $settings['colorTheme']['accent'] ?>;
    text-align: start;
  }

  .reduction {
    margin: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .reduction span {
    color: <?= $settings['colorTheme']['accent'] ?>;
    font-weight: bold;
  }

  p {
    margin: 0;
  }


  .uppercase {
    text-transform: uppercase;
  }

  .bold {
    font-weight: bold;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .p-block-5 {
    padding-block: 5px;
  }

  .product-feature {
    margin: 0;
    padding-left: 20px;
    line-height: 1.5;
  }

  .d-block {
    display: block;
  }

  .all-body {
    display: flex;
    flex-direction: column;
    width: 200mm;
    /* A4 width */
    height: 260mm;
    /* A4 height */
    /* max-height: 993px !important;
  overflow: hidden; */
  }


</style>
