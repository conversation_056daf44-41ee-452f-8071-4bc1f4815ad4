@extends('pdf.content-layout')

@section('style')
    <style>
        .image-container {
            min-height: 70vh;
            width: 100%;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }
        .image-container img {
            width: 100%;
            max-width: 350px;
        }
        .under-construction-title {
            font-weight: 700;
            text-transform: uppercase;
            color: #2b6cb0;
            font-size: 2.5rem;
            margin-top: 40px;
        }
    </style>
@endsection

@section('content')
<div class="main-container">
    <div class="main-content">
        <div class="full-column">
            <div class="header">
                <div class="header-contents">
                    {{-- <div class="mask-credits"></div> --}}
                    <img alt="Image info" src="https://cdn.solar-control.energy/images/pdf/info.png" width="80px" />
                    <div class="sub-titles">
                        <h1>
                            <span>{{ t('core.under_construction', [], $lang ?? 'en') }}</span>
                        </h1>
                    </div>
                </div>
                <div class="image-container">
                    <div style="margin-right: 150px">
                        <img
                            src="https://cdn.solar-control.energy/images/static/under-construction.png"
                            alt="Under Construction">
                        <div class="under-construction-title">
                            {{ t('core.under_construction', [], $lang ?? 'en') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
