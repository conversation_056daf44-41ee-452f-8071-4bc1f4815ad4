<section class="page-container page-idx-{{ $pageIdx ?? '' }}">
    <header class="absolute h-fit top-0 w-full left-0">
        <div class="header-content">
            <img src="https://cdn.solar-control.energy/images/pdf/header-pdf.png" alt="Image PVGIS" class="top-img">
            <div class="header-text">
                <h2>
                    {{ t('core.simulation.pdf.header', [], $lang ?? 'en') }}
                </h2>
            </div>
        </div>
    </header>


    <section class="bg-white" style="margin-top: 40px;">
        <div class="wrapper">
            <div class="left-section">
                {{-- <div class="content">
                    <h3 data-ts="core.simulation.pdf.self.consumption.header">AUTOCONSOMMATION </h3>
                    <h3 data-ts="core.simulation.pdf_autonomy" >+ AUTONOMIE </h3>
                    <h3><span style=" font-family:  var(--font-condensed) !important;"  data-ts="core.simulation.pdf.power" >PUISSANCE</span> 5 kWh</h3>
                    <h3><span style=" font-family:  var(--font-condensed) !important;"  data-ts="core.simulation.pdf.financing" >FINANCEMENT</span> <span class="uppercase"><span data-ts="core.extranet.file_info.">1</span></span></h3>
                </div> --}}
            </div>
            <div class="right-section">
                <div class="letter-container">
                    <div class="letter"> <span>{{ t('core.simulation.pdf.client.name', [], $lang ?? 'en') }}</span>
                    </div>
                    <div class="text uppercase ">{{ $client['name'] ?? '-' }}</div>
                </div>
                <div class="letter-container ">
                    <div class="letter">
                        <span>{{ t('core.simulation.pdf.project.location', [], $lang ?? 'en') }}</span>
                    </div>
                    <div class="text uppercase">{{ $projectInfo['location'] ?? '-' }}</div>
                </div>
                <div class="letter-container">
                    <div class="letter">
                        <span>{{ t('core.simulation.pdf.solar.file', [], $lang ?? 'en') }}</span>
                    </div>
                    <div class="text uppercase">{{ $projectInfo['projectName'] ?? '-' }}</div>
                </div>
                <div class="letter-container letter-container-last">
                    <div class="letter">
                        <span>{{ t('core.simulation.pdf.simulation', [], $lang ?? 'en') }}</span>
                    </div>
                    <div class="text uppercase">{{ $projectInfo['simulationName'] ?? '-' }}</div>
                </div>


            </div>
        </div>

        <div class="wrapper bottom-section">
            <div class="letter"></div>
            <div class="right-section">
                <div class="letter-container">
                    <div class="letter">
                        <span> {{ t('core.simulation.pdf.client.contact', [], $lang ?? 'en') }} </span>
                    </div>
                    <div class="text">{{ $client['contact'] ?? '-' }}</div>
                </div>
                <div class="letter-container">
                    <div class="letter">
                        <span> {{ t('core.simulation.pdf.client.email', [], $lang ?? 'en') }} </span>
                    </div>
                    <div class="text">{{ $client['email'] ?? '-' }}</div>
                </div>
                <div class="letter-container">
                    <div class="letter">
                        <span> {{ t('core.simulation.pdf.client.phone', [], $lang ?? 'en') }} </span>
                    </div>
                    <div class="text">{{ $client['phoneNumber'] ?? '-' }}</div>
                </div>
            </div>
        </div>


    </section>

    <footer class="absolute bottom-0 w-full left-0">
        <div class="flex w-full justify-between items-center">
            <span class="user-logo" style="margin-right: 20px;">
                @if (isset($urlImage))
                    <img src="{{ $urlImage }}" alt="Logo" class="footer-logo" />
                @endif
            </span>
        </div>
    </footer>
</section>
