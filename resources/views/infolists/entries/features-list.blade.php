@php
  // The features are passed from the ViewEntry's data.
  // The data is located at $getRecord()->plan->product->features
  $features = $getRecord()->plan->product->features ?? [];
@endphp

<div {{ $attributes }}>
    <ul class="flex flex-col items-start gap-3">
      @foreach ($features as $f)
        <li class="inline-flex gap-2 text-sm">
          <div class="flex-shrink-0">
              @if($f['pivot']['enabled'])
                  <svg class="w-5 h-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
              @else
                  <svg class="w-5 h-5 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
              @endif
          </div>
          <span>{{ $f['name'] }}</span>
        </li>
      @endforeach
    </ul>
</div>