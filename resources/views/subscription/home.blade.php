<x-layouts.app>
    <div class="mx-4 mt-5"
         x-data="{ isPro: false, isYearly: false }"
         x-init="$watch('isPro', val => { if (val) isYearly = false })">

        <h2 class="text-4xl font-extrabold dark:text-white text-primary_ui-50 mb-6 leading-tight tracking-tight">
            {{ $title ?? 'Check and control the solar production of your photovoltaic system' }}
        </h2>

        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 mb-8 space-y-4 sm:space-y-0 flex items-center justify-between mt-4 mb-8">
            <label class="inline-flex items-center cursor-pointer">
                <span class="font-bold me-3 text-sm text-black-900 dark:text-black-300">{{ t('ui_sub_none_pro') }}</span>
                <input type="checkbox" class="sr-only peer" x-model="isPro">
                <div class="relative w-14 h-8 bg-yellow_custom-50 rounded-full dark:bg-gray-700
                            peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300
                            dark:peer-focus:ring-blue-800 peer-checked:after:translate-x-full
                            rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white
                            after:content-[''] after:absolute after:top-[3.5px] after:start-[3px]
                            after:bg-white after:border-gray-300 dark:border-gray-600
                            after:border after:rounded-full after:h-6 after:w-6 after:transition-all">
                    <!-- <div class="absolute top-[1.7px] start-[1.2px] w-7 h-7 rounded-full border-2 border-black
                                transition-all transform peer-checked:translate-x-full"></div> -->
                </div>
                <span class="font-bold ms-3 text-sm text-black-900 dark:text-black-300">{{ t('ui_sub_pro') }}</span>

            </label>


            <!-- <div x-show="isPro" class="transition-opacity duration-300"> -->
            <div class="transition-opacity duration-300">
                <label class="inline-flex items-center cursor-pointer">
                    <span class="font-bold me-3 text-sm text-black-900 dark:text-black-300">{{ t('ui_sub_monthly') }}</span>
                    <input type="checkbox" class="sr-only peer" x-model="isYearly">
                    <div class="relative w-14 h-8 bg-primary_ui-50 rounded-full dark:bg-gray-700
                            peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300
                            dark:peer-focus:ring-blue-800 peer-checked:after:translate-x-full
                            rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white
                            after:content-[''] after:absolute after:top-[3.5px] after:start-[3px]
                            after:bg-white after:border-gray-300 dark:border-gray-600
                            after:border after:rounded-full after:h-6 after:w-6 after:transition-alll">
                    </div>
                    <span class="font-bold ms-3 text-sm text-black-900 dark:text-black-300">{{ t('ui_sub_yearly') }}</span>
                </label>
            </div>
        </div>

        <div class="text-start mb-4">
            <p class="dark:text-white text-primary_ui-50 font-bold">
                {{ t('ui_sub_description_service') }}
            </p>
        </div>

        <div x-show="isPro" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-stretch">
            @foreach ($productsProfessional as $product)
                <template x-if="isYearly && '{{ $product['type'] }}' === 'Yearly'">
                    <div class="product-card h-full">
                        <x-subscription.plan-card :product="$product" />
                    </div>
                </template>
                <template x-if="!isYearly && '{{ $product['type'] }}' === 'Monthly'">
                    <div class="product-card h-full">
                        <x-subscription.plan-card :product="$product" />
                    </div>
                </template>
            @endforeach
        </div>

        <div x-show="!isPro" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-stretch">
            @foreach ($productsParticular as $product)
                <template x-if="isYearly && '{{ $product['type'] }}' === 'Yearly'">
                    <div class="product-card h-full">
                        <x-subscription.plan-card :product="$product" />
                    </div>
                </template>
                <template x-if="!isYearly && '{{ $product['type'] }}' === 'Monthly'">
                    <div class="product-card h-full">
                        <x-subscription.plan-card :product="$product" />
                    </div>
                </template>
            @endforeach
        </div>

        <div class="text-start mt-5">
            <p class="text-lg text-primary_ui-50 font-bold">
                * {{ t('ui_sub_no_limit') }}
            </p>
        </div>
    </div>
</x-layouts.app>
