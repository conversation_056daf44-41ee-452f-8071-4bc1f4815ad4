<x-filament::page>
    <div class="border-[1px] border-primary_ui_high-900 p-5" x-data="mySettings($wire.users)">
        <div class="bg-gray-100 p-5 rounded-md text-lg">
            <form @submit.prevent="submitForm" wire:submit.prevent="submit">

                {{ $this->form }}

                <!-- Users Section -->
                <div x-show="users.length" class="mt-6">
                    <div class="mb-1 font-semibold" data-ts="core.my_setting.users">{{ t('core.my_setting.users') }}</div>
                    <template x-for="(user, idx) in users" :key="idx">
                        <div class="flex flex-col gap-1 mb-4">
                            <div class="flex gap-4 items-start">
                                <!-- Email -->
                                <div class="flex flex-col flex-1">
                                    <input type="email" :disabled="!!user.id" :class="{
                                        'border-red-500 focus:border-red-500 hover:outline-red-500/30 focus:outline-red-500/30': user.email_error || user.email_exist_error,
                                        'border-primary_ui_high-900 focus:border-primary_ui_high-900': !user.email_error && !user.email_exist_error
                                    }" class="form-input rounded px-2 py-1 disabled:cursor-not-allowed"
                                        placeholder="Email" x-model="user.email" />
                                    <small x-show="user.email_error"
                                        class="text-red-500 text-xs" data-ts="core.user.email_error">{{ t('core.user.email_error') }}</small>
                                    <small x-show="user.email_exist_error"
                                        class="text-red-500 text-xs" data-ts="core.user.email_exist_error">{{ t('core.user.email_exist_error') }}</small>
                                </div>

                                <!-- Password -->
                                <div class="flex flex-col flex-1 relative">
                                    <div class="flex flex-col flex-1 relative">
                                        <input :type="user.showPassword ? 'text' : 'password'" :disabled="!!user.id" :class="{
                                            'border-red-500 focus:border-red-500 hover:outline-red-500/30 focus:outline-red-500/30': user.password_error,
                                            'border-primary_ui_high-900 focus:border-primary_ui_high-900': !user.password_error
                                        }" class="form-input rounded px-2 py-1 pe-10 disabled:cursor-not-allowed"
                                            placeholder="{{ t('core.user.password_placeholder') }}"
                                            x-model="user.password" />

                                        <!-- Eye icon -->
                                        <button type="button" :disabled="!!user.id"
                                            class="absolute right-[35px] top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                                            @click="user.showPassword = !user.showPassword">
                                            <template x-if="user.showPassword">
                                                <x-heroicon-o-eye-slash class="w-5 h-5" />
                                            </template>
                                            <template x-if="!user.showPassword">
                                                <x-heroicon-o-eye class="w-5 h-5" />
                                            </template>
                                        </button>

                                        <!-- Generate Password -->
                                        <button type="button" :disabled="!!user.id"
                                            class="absolute right-[5px] top-1/2 -translate-y-1/2 bg-gray-200 rounded p-1 text-xs"
                                            @click="user.password = Math.random().toString(36).slice(-6)">
                                            <x-heroicon-o-key class="w-5 h-5" />
                                        </button>

                                    </div>
                                    <small x-show="user.password_error"
                                        class="text-red-500 text-xs" data-ts="core.user.password_error">{{ t('core.user.password_error') }}</small>
                                </div>

                                <!-- Reset password button -->
                                <button class="h-[34px] w-[34px]" :disabled="!user.id" type="button"
                                    @click="resetPassword(user.id ?? 0)" title="{{ t('core.user.reset_password') }}">
                                    <span
                                        class="inline-flex items-center justify-center border bg-gray-200 rounded p-1">
                                        <x-heroicon-o-arrow-path class="font-bold w-6 h-6 text-black" />
                                    </span>
                                </button>

                                <!-- Toggle active/deactivate button -->
                                <button type="button" :disabled="user.loading ? true : false" class="h-[34px] w-[34px]"
                                    @click="!user.id ? saveUser(idx) : toggleActive(user.id, user.active, idx)"
                                    :title="!user.id ? '{{ t('core.user.save') }}' : user.active ? '{{ t('core.user.deactivate') }}' : '{{ t('core.user.activate') }}'">
                                    <span x-show="user.id && !user.active"
                                        class="inline-flex items-center justify-center bg-green-600 rounded p-1 h-100">
                                        <x-heroicon-o-link class="font-bold w-6 h-6 text-white" />
                                    </span>
                                    <span x-show="user.id && user.active"
                                        class="inline-flex items-center justify-center bg-red-600 rounded p-1 h-100">
                                        <x-heroicon-o-link-slash class="font-bold w-6 h-6 text-white" />
                                    </span>
                                    <span x-show="!user.id"
                                        class="inline-flex items-center justify-center hover:bg-primary_ui-50 bg-primary_ui_high-50 rounded p-1 h-100">
                                        <img class="!max-w-[none] w-6" src="{{URL::asset('/images/save.png')}}"
                                            alt="save-icon" width="24px">
                                    </span>
                                </button>

                                <!-- Remove user button -->
                                <button type="button" :disabled="!user.id" @click="askRemove(user.id ?? 0, idx)"
                                    class="bg-red-500 hover:bg-red-600 h-[34px] w-[34px] flex items-center justify-center rounded text-white"
                                    title="{{ t('core.user.remove_user') }}">
                                    <x-heroicon-o-trash class="w-6 h-6" />
                                </button>
                            </div>
                        </div>
                    </template>

                    {{-- <button type="button" @click="addUser"
                        class="mt-2 bg-blue-100 border border-blue-400 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 text-sm">
                        + {{ t('core.user.add_user') }}
                    </button> --}}
                </div>
                <!-- Hidden input to wire users data back to backend -->
                {{-- <input type="hidden" wire:model.defer="users_json" x-ref="usersInput"
                    :value="JSON.stringify(users)" /> --}}

                <div class="text-end mt-6">
                    <x-filament::button type="submit" class="align-right" data-ts="core.save">
                        {{ t('core.save') }}
                    </x-filament::button>
                </div>
            </form>
        </div>

        <div x-show="confirmOpen" x-transition.opacity class="fixed inset-0 z-50 flex items-center justify-center"
            aria-modal="true" role="dialog">
            <!-- Backdrop -->
            <div class="absolute inset-0 bg-black/50" @click="closeConfirm()"></div>

            <!-- Panel -->
            <div x-show="confirmOpen" x-transition.scale class="relative z-10 w-full max-w-md bg-white shadow-xl">
                <div class="flex items-start gap-3 p-6">
                    <x-heroicon-s-exclamation-triangle class="w-10 h-10 text-red-500 shrink-0" />
                    <div class="text-start">
                        <h3 class="text-lg font-semibold">{{ t('core.delete.sub_user.title') }}</h3>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ t('core.delete.sub_user.text_content') }}
                        </p>
                    </div>
                </div>

                <div class="mt-6 flex items-center justify-end">
                    <button type="button" class="px-4 py-2 h-[42px] border hover:bg-gray-50" @click="closeConfirm()"
                        x-ref="confirmCancel" :disabled="confirming">
                        {{ t('core.cancel') }}
                    </button>
                    <button type="button"
                        class="px-4 py-2 bg-red-600 h-[42px] text-white hover:bg-red-700 disabled:opacity-60"
                        @click="confirmRemove()" :disabled="confirming">
                        <span x-show="!confirming">{{ t('core.action.delete') }}</span>
                        <span x-show="confirming">{{ t('core.action.deleting') }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    @vite('resources/js/setting/my-setting.js')

</x-filament::page>
