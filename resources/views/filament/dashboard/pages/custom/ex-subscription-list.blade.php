<x-filament::page>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach ($this->subscriptions as $subscription)
            <div class="border rounded-lg p-4 shadow-sm">
                <h2 class="text-lg font-semibold">{{ $subscription->plan->name }}</h2>
                <p class="text-sm text-gray-500">
                    {{ money($subscription->price, $subscription->currency->code) }} /
                    {{ $subscription->interval_count > 1 ? $subscription->interval_count . ' ' . Str::plural($subscription->interval->name) : $subscription->interval->name }}
                </p>
                <p class="mt-1">
                    Status: <span class="text-xs {{ $subscription->status === 'active' ? 'text-green-500' : 'text-red-500' }}">{{ $subscription->status }}</span>
                </p>
                <div class="mt-4">
                    <a href="{{ route('filament.dashboard.resources.subscriptions.view', $subscription) }}" class="text-primary-600 text-sm underline">See details</a>
                </div>
            </div>
        @endforeach
    </div>
</x-filament::page>
