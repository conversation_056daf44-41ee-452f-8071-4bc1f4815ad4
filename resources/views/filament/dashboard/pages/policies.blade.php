<x-filament::page x-data="{ viewing: '' }">
    <div class="border-[1px] border-primary_ui_high-900 rounded">
        <template x-if="viewing === ''">
                <div class=" bg-gray-100 rounded space-y-5 p-5 m-5">
                    <div class="flex flex-col items-start justify-between ">
                        <span class="text-lg text-black font-semibold mb-2">{{ t("core.auth.terms_and_conditions") }}</span>
                        <x-filament::button color="primary" @click="viewing = 'terms'">{{ t("core.view") }}</x-filament::button>
                    </div>
                    <div class="flex flex-col items-start justify-between">
                        <span class="text-lg text-black font-semibold mb-2">{{ t("core.privacy_policy_title") }}</span>
                        <x-filament::button color="primary" @click="viewing = 'privacy'">{{ t("core.view") }}</x-filament::button>
                    </div>
                </div>
        </template>

        <template x-if="viewing === 'terms'">
            <div class="max-w-3xl mx-auto p-5">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl text-black font-bold">{{ t("core.auth.terms_and_conditions") }}</h2>
                    <button @click="viewing = ''" class="text-gray-500 hover:text-gray-700">
                        <x-heroicon-o-x-mark class="w-5 h-5" />
                    </button>
                </div>
                <div class="bg-white rounded py-6 max-h-[calc(100vh-310px)] overflow-y-auto text-black">
                    @include('components.policies.terms-of-service')
                </div>
            </div>
        </template>

        <template x-if="viewing === 'privacy'">
            <div class="max-w-3xl mx-auto p-5">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl text-black font-bold">{{ t("core.privacy_policy_title") }}</h2>
                    <button @click="viewing = ''" class="text-gray-500 hover:text-gray-700">
                        <x-heroicon-o-x-mark class="w-5 h-5" />
                    </button>
                </div>
                <div class="bg-white rounded py-6 max-h-[calc(100vh-310px)] overflow-y-auto text-black">
                    @include('components.policies.privacy-policy')
                </div>
            </div>
        </template>
    </div>
</x-filament::page>
