@php($uid = 'media_' . \Illuminate\Support\Str::uuid())
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

{{-- The simple div your library needs --}}
<div id="{{ $uid }}" class="m-0 p-0 media-manager-container" wire:ignore></div>

{{-- Make sure Vite loads the small init file that imports the NPM package --}}
@vite('resources/js/filament/media-manager.js')

<script>
    document.addEventListener("livewire:navigated", () => {
        console.trace('Check this livewire.navigated')
        initMediaManager();
    });

    function initMediaManager() {
        const el = document.getElementById(@json($uid));
        if (!el) return;
        el.innerHTML = '';

        const interval = setInterval(() => {
            if (typeof window.bootMediaManager === "function") {
                clearInterval(interval);

                el.innerHTML = '';

                window.bootMediaManager(el, {
                    btnId: "mediaToggle",
                    btnText: "Media manager",
                    API_URL: "{{ config('app.media_manager_api') }}",
                    uiText: {
                        home: "Home",
                        deleteConfirmationText:
                            "Are you sur ? <br> all instance that use this file will be un reachable. <br>",
                        headerTitle: "Media manager",
                    },
                });
            }
        }, 500);
    }
</script>
