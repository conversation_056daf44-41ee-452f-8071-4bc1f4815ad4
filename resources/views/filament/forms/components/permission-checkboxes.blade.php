<div class="space-y-4">
    <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
        Permissions
    </div>
    
    <div class="grid grid-cols-4 gap-6">
        @foreach($permissions as $category => $categoryPermissions)
            <div class="space-y-3">
                {{-- Category Header --}}
                <div class="text-sm font-semibold text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700 pb-1">
                    {{ $category }}
                </div>
                
                {{-- Permissions in this category --}}
                <div class="space-y-2">
                    @foreach($categoryPermissions as $permission)
                        <label class="flex items-start space-x-2 text-sm cursor-pointer">
                            <input 
                                type="checkbox" 
                                name="permissions[]" 
                                value="{{ $permission->id }}"
                                wire:model="{{ $getStatePath }}"   
                                @if(in_array($permission->id, $selectedPermissions)) checked @endif
                                class="mt-0.5 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            />

                            <span class="text-gray-700 dark:text-gray-300 leading-4 mt-[2px]">
                                {{ ucwords(str_replace(['-', '_'], ' ', $permission->name)) }}
                            </span>
                        </label>
                    @endforeach
                </div>
            </div>
        @endforeach
    </div>
    
    {{-- Helper text --}}
    <div class="text-xs text-gray-500 dark:text-gray-400">
       {{ t('core.permission.text_helper') }} 
    </div>
</div>