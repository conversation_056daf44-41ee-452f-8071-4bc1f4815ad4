<div x-data x-show="$store.global.isLoading" x-transition.opacity style="display: none;"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center">

    <div class="loader"></div>

    <style>
        .loader {
            width: 48px;
            height: 48px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            /* Faint ring */
            border-top-color: white;
            border-bottom-color: white;
            border-left-color: white;
            /* Visible leading arc */
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            /* Smooth rotation */
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>

</div>



<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('global', {
            isLoading: false,
            showLoading() {
                this.isLoading = true;
            },
            hideLoading() {
                this.isLoading = false;
            }
        });
    });

    if (window.Livewire) {
        Livewire.hook('message.sent', () => Alpine.store('global').showLoading());
        Livewire.hook('message.processed', () => Alpine.store('global').hideLoading());
    }
</script>
