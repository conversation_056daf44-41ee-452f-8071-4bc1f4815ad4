<div class="w-full">
    <div class="p-6 pb-0">
        <h1 class="text-xl text-primary_ui_high-50 font-bold tracking-tight">{{ t('core_payment_method_title') }}</h1>
        <form id="payment-form" class="mt-2 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('core_ui_my_sub_credit_card') }}</label>
                    <div id="card-number-element" class="p-3 border rounded-md" wire:ignore></div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1"> {{ t('core_ui_my_sub_expired_date') }} </label>
                    <div id="card-expiry-element" class="p-3 border rounded-md" wire:ignore></div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1"> {{ t('core_ui_my_sub_code_security') }} </label>
                    <div id="card-cvc-element" class="p-3 border rounded-md" wire:ignore></div>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ t('core_ui_my_sub_country') }}</label>
                <select id="billing-country" class="p-3 border rounded-md w-full text-black">
                @foreach($countryList as $country)
                        <option value="{{ $country['code_alpha_2'] }}">{{ $country['name'] }}</option>
                    @endforeach
                </select>
            </div>
            <div id="card-errors" class="text-sm text-red-600 mt-2"></div>
            <p class="text-sm text-gray-500 mt-2">
                {{ t('core_ui_add_card_desc', ['appName' => $appName]) }}
            </p>
        </form>
    </div>

    
    <div class="mt-4 flex justify-end">
        <button
            class="px-6 py-3 bg-primary_ui-50 text-white focus:outline-none"
            id="submit-card"
            x-ref="submitCard"
            x-data="{ loading: false }"
            x-on:click="loading = true"
            x-bind:disabled="loading"
            x-bind:class="loading ? 'opacity-50' : ''"
            style="border-radius: 0;"
        >
            <span x-show="!loading">{{ t('core_ui_add_card_save') }}</span>
            <span x-show="loading">{{ t('core_ui_add_card_saving') }}...</span>
        </button>
    </div>


</div>

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
window.translations = {
    add_error_card: @json(t('core_ui_add_error_card')),
    core_ui_add_error_card_not_valid: @json(t('core_ui_add_error_card_not_valid')),
    add_error_sec: @json(t('core_ui_add_error_sec')),
    add_error_date: @json(t('core_ui_add_error_date')),
    add_error_date_empty: @json(t('core_ui_add_error_date_empty')),
    add_error_default: @json(t('core_ui_add_error_default'))
};

document.addEventListener('livewire:initialized', function () {
    // Stripe setup
    const stripe = Stripe(@json(config('services.stripe.publishable_key')));
    const elements = stripe.elements();
    const cardNumber = elements.create('cardNumber');
    const cardExpiry = elements.create('cardExpiry');
    const cardCvc = elements.create('cardCvc');
    cardNumber.mount('#card-number-element');
    cardExpiry.mount('#card-expiry-element');
    cardCvc.mount('#card-cvc-element');

    const submitBtn = document.getElementById('submit-card');
    const cardErrors = document.getElementById('card-errors');
    const billingCountry = document.getElementById('billing-country');

    Livewire.on('close-modal', ({ id }) => {
        if (id === 'payment-method-form-modal') {
            Alpine.$data(submitBtn).loading = false;
            cardNumber.clear();
            cardExpiry.clear();
            cardCvc.clear();
            cardErrors.textContent = '';
        }
    });

    async function handleSubmit() {
        let clientSecret = @this.get('clientSecret');
        let country = billingCountry.value;

        const { setupIntent, error } = await stripe.confirmCardSetup(
            clientSecret,
            {
                payment_method: {
                    card: cardNumber,
                    billing_details: {
                        address: { country: country }
                    }
                }
            }
        );

        if (error) {
            let keyTransError = '';
            switch (error.message) {
                case 'Votre numéro de carte est incomplet.':
                    keyTransError = window.translations.add_error_card;
                    break;
                case "Votre numéro de carte n'est pas valide.":
                    keyTransError = window.translations.core_ui_add_error_card_not_valid;
                    break;
                case 'Le code de sécurité de votre carte est incomplet.':
                    keyTransError = window.translations.add_error_sec;
                    break;
                case "La date d'expiration de votre carte est incomplète.":
                    keyTransError = window.translations.add_error_date_empty;
                    break;
                case "L'année d'expiration de votre carte est dans le passé.":
                    keyTransError = window.translations.add_error_date;
                    break;
                case "L'année d'expiration de votre carte est dans le passé.":
                    keyTransError = window.translations.core_ui_add_error_date_empty;
                    break;
                default:
                    keyTransError = window.translations.add_error_default;
            }
            // cardErrors.textContent = error.message;
            cardErrors.textContent = keyTransError;
            Alpine.$data(submitBtn).loading = false;
        } else {
            await @this.call('handlePaymentMethodAdded', setupIntent.payment_method);
            // after finish
            // we redirect : window.location = '/subscriptions'
        }
    }

    submitBtn?.addEventListener('click', handleSubmit);
});
</script>
@endpush