<div>
  <x-filament-actions::modals />

  @if ($subscription)
    <x-filament::section :id="$subscription->uuid">
      <x-slot name="heading">
        My current subscription
      </x-slot>

      {{ $this->subscriptionInfolist->record($subscription) }}

    </x-filament::section>
    @if ($subscriptionNext)
      <x-filament::section :id="$subscriptionNext->uuid" class="mt-5">
        <x-slot name="heading">
          My next subscription
        </x-slot>

        {{ $this->subscriptionInfolistNext->record($subscriptionNext) }}

      </x-filament::section>
    @endif

    <div class="pt-12">
      <h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl text-center mb-6">
          {{ __('Change Your Plan') }}
      </h2>

      <x-filament.plans.all
        :disableBuyBtn="$disableBuyBtn"
        :is-grouped="true"
        preselected-interval="year"
        current-subscription-uuid="{{ $subscription->uuid }}"
      />
    </div>
  @else
    {{-- KEY CHANGE: This block runs if no current subscription was found. --}}
    <x-filament::section>
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ __('No Active Subscription Found') }}
        </h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {{ __("You don't have an active subscription yet.") }}
        </p>
        {{-- Optional: Add a link to your pricing page --}}
        {{--
        <div class="mt-6">
          <x-filament::button :href="route('pricing')">
            {{ __('View Plans') }}
          </x-filament::button>
        </div>
        --}}
      </div>
    </x-filament::section>
  @endif

  {{-- KEY CHANGE: Replace the old invoice section with the Filament table --}}
  <x-filament::section class="mt-8">
    <x-slot name="heading"> {{ __('My Invoices & Transactions') }} </x-slot>

    {{-- This line will render the entire table you defined above --}}
    {{ $this->table }}
  </x-filament::section>
</div>
