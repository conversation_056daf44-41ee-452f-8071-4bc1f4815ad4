<div
    class="bg-gray-100 p-5 space-y-4"
>
    <h3 class="text-lg font-semibold text-red-600">
        {{ t('core.delete_account') }}
    </h3>

    <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ t('core.delete_account_text') }}
    </p>

    <x-filament::button
        color="danger"
        wire:click="openConfirmationModal"
        icon="heroicon-o-trash"
        class="danger-btn"
    >
        {{ t('core.delete_account') }}
    </x-filament::button>

    <x-filament::modal
        id="confirm-delete-account-modal"
        icon="heroicon-o-exclamation-triangle"
        icon-color="danger"
        :wire-close="false"
        :close-by-clicking-away="true"
        width="lg"
    >
        <x-slot name="heading">
            {{ t('core.delete_account_you_sure') }}
        </x-slot>

        <x-slot name="description">
            <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ t('core.delete_account_you_sure_text') }}
            </p>
        </x-slot>

        <x-slot name="footerActions">
            <div class="w-full flex justify-end space-x-2">
                <x-filament::button
                    color="gray"
                    wire:click="closeConfirmationModal"
                    >
                    {{ t('core.auth.cancel') }}
                </x-filament::button>
                <x-filament::button color="danger" wire:click="deleteAccount">
                    {{ t('core.delete_account_yes_btn') }}
                </x-filament::button>
            </div>
        </x-slot>
    </x-filament::modal>
</div>
