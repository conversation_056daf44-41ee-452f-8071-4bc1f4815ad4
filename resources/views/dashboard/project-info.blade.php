<x-layouts.dashboard :styles="$styles">
    <x-slot name="content">
        <div class="project-info"
            x-data="createProject({{ isset($project) ? json_encode($project) : 'null' }}, {{ json_encode($subscription) }})"
            x-init="
                init();
                $watch('locationType', async (value) => {
                    if (value === 'gps-geolocated') {
                        await geolocateUser();
                    }
                });
            " x-on:map-update.window="
                latitude  = $event.detail.lat;
                longitude = $event.detail.lng;
                await updateAddressFromCoords()
            ">
            {{-- ───────── Page Title --}}
            <h1 class="text-2xl font-bold text-primary_ui-50 mb-2">
                {{ isset($project) ? t('core.project.folder_overview') : t('core.project.creation') }}
            </h1>

            {{-- ───────── Map (placeholder) --}}
            <div class="w-full">
                <x-map.map-picker :distance-km="0.1" :editable="$isInfo === 'false'"
                    mapClass="h-[450px]"></x-map.map-picker>
            </div>

            {{-- Section heading --}}
            <h2 class="text-primary_ui-50 text-2xl font-bold mb-4 mt-3">
                {{ t('core.geolocation') }}
            </h2>

            {{-- “Show Geolocation Info” small note --}}
            <p class="text-md text-gray-700 underline -mt-3 mb-4 cursor-pointer w-max"
                @click="showGeolocationInformation = !showGeolocationInformation">
                {{ t('core.show_geolocation_info') }}
            </p>

            {{-- CONTENT GRID --}}
            <form x-cloak x-show="showGeolocationInformation" method="POST" action="#"
                @submit.prevent="if (validateForm(true) && canCreate()) { showConfirmationScreen = true }"
                class="border-2 border-primary_ui-50 rounded-sm bg-white p-5 space-y-6 relative mb-8 pb-[50px]">
                @if(!$subscription)
                    <p id="error"
                        class="text-start bg-yellow-100 text-black border !mb-[10px] px-4 py-2 rounded-md mx-auto font-light flex items-center gap-2 justify-start">
                        <x-heroicon-s-exclamation-triangle class="w-6 h-6 text-yellow-500" />
                        {!! t('core.project.no_subscription', ['url' => '/subscriptions']) !!}
                    </p>
                @elseif($subscription && $subscription['balanceMonth'] <= 0)
                    <p id="error"
                        class="text-start bg-yellow-100 text-black border !mb-[10px] px-4 py-2 rounded-md mx-auto font-light flex items-center gap-2 justify-start">
                        <x-heroicon-s-exclamation-triangle class="w-6 h-6 text-yellow-500" />
                        {!! t('core.project.no_more_credit', ['additional_credit_url' => '/subscriptions']) !!}
                    </p>
                @endif

                <div class="flex gap-6">
                    <div class="flex-1 flex gap-6">
                        <fieldset class="location-type">
                            <legend class="font-semibold mb-2 text-lg text-primary_ui-50">
                                {{ t('core.production_location') }}
                            </legend>
                            <div class="space-y-2">
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}"
                                    value="address">{{ t('core.location.address') }}</x-forms.custom-radio>
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}"
                                    value="gps-point">{{ t('core.location.gps_point') }}</x-forms.custom-radio>
                                <x-forms.custom-radio size="md" model="locationType" disabled="{{ $isInfo }}"
                                    value="gps-geolocated">{{ t('core.location.gps_geolocated') }}</x-forms.custom-radio>
                            </div>
                        </fieldset>
                        <div class="flex-1 gap-10 flex text-black">
                            <table x-show="locationType === 'address' || locationType === 'gps-geolocated'" x-cloak
                                class="location-coordinate mt-[28px] h-max">
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.country') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.country') }}"
                                            x-model="address.country" @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui" :disabled="fieldsDisabled" />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.city') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.city') }}"
                                            x-model="address.city" @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui" :disabled="fieldsDisabled" />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.street') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.street') }}"
                                            x-model="address.street" @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui" :disabled="fieldsDisabled" />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.number') }}</label></td>
                                    <td>
                                        <input type="text" placeholder="{{ t('core.form.number') }}"
                                            x-model="address.number" @input.debounce.750ms="geocodeAddress()"
                                            class="input-ui" :disabled="fieldsDisabled" />
                                    </td>
                                </tr>
                            </table>
                            <table x-show="locationType === 'gps-point'" x-cloak
                                class="location-coordinate mt-[28px] h-max">
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.latitude') }}</label></td>
                                    <td>
                                        <input type="number" step="any" placeholder="{{ t('core.form.latitude') }}"
                                            x-model.number="latitude"
                                            :disabled="fieldsDisabled || locationType === 'gps-geolocated'"
                                            class="input-ui" />
                                    </td>
                                </tr>
                                <tr>
                                    <td><label class="form-label">{{ t('core.form.longitude') }}</label></td>
                                    <td>
                                        <input type="number" step="any" placeholder="{{ t('core.form.longitude') }}"
                                            x-model.number="longitude"
                                            :disabled="fieldsDisabled || locationType === 'gps-geolocated'"
                                            class="input-ui" />
                                    </td>
                                </tr>
                            </table>
                            <div class="folder-information">
                                <div class="mb-2">
                                    <label
                                        class="form-label font-bold text-md text-primary_ui-50">{{ t('core.customer.name') }}</label>
                                    <input type="text" class="input-ui"
                                        :class="validation.project_info ? 'border-red-500 outline-none' : ''"
                                        x-model="project_info" @input="validateField('project_info')"
                                        :disabled="fieldsDisabled">
                                    <template x-if="validation.project_info">
                                        <div class="text-red-500 text-md font-semibold text-[15px] mt-[-3px] mb-1"
                                            x-text="validation.project_info"></div>
                                    </template>
                                </div>
                                <div>
                                    <label
                                        class="form-label font-bold text-md text-primary_ui-50">{{ t('core.project.name') }}</label>
                                    <input type="text" class="input-ui"
                                        :class="validation.project_name ? 'border-red-500 outline-none' : ''"
                                        x-model="project_name" @input.debounce.300ms="validateField('project_name')"
                                        :disabled="fieldsDisabled">
                                    <template x-if="validation.project_name">
                                        <div class="text-red-500 text-md font-semibold text-[15px] mt-[-3px] mb-1"
                                            x-text="validation.project_name"></div>
                                    </template>
                                </div>
                                @if (isset($project))
                                    <div class="mt-1">
                                        <label
                                            class="inline-block form-label font-bold text-md text-primary_ui-50">{{ t('core.date') }}:</label>
                                        <span>{{ \Carbon\Carbon::parse($project['created_at'])->subDay()->format('d/m/Y') }}</span>
                                    </div>
                                @endif
                                <div class="flex gap-2 mt-2 profile-choice">
                                    <x-forms.custom-radio size="md" model="profile" value="residential"
                                        disabled="{{ $isInfo }}">{{ t('core.profile.residential') }}</x-forms.custom-radio>
                                    <x-forms.custom-radio size="md" model="profile" value="commercial"
                                        disabled="{{ $isInfo }}">{{ t('core.profile.commercial') }}</x-forms.custom-radio>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if ($subscription)
                        <div class="subscription-information text-md leading-5 space-y-0.5 italic text-gray-500">
                            <p><span>{{ t('core.subscription.credits_year') }} :</span> {{ $subscription['creditsYear'] }}
                            </p>
                            <p><span>{{ t('core.subscription.credits_month') }} :</span> {{ $subscription['creditsMonth'] }}
                            </p>
                            <p><span>{{ t('core.subscription.created_month') }} :</span>
                                {{ $subscription['projectsMonth'] }}</p>
                            <p><span>{{ t('core.subscription.balance_month') }} :</span> {{ $subscription['balanceMonth'] }}
                            </p>
                        </div>
                    @endif
                </div>

                {{-- Save button --}}
                <div class="absolute right-0 bottom-0 flex justify-end w-full">
                    <button type="submit"
                        :disabled="!subscription_id || isLoading || fieldsDisabled || {{ $subscription && $subscription['balanceMonth'] <= 0 ? 'true' : 'false' }}"
                        class="text-white text-lg disabled:cursor-not-allowed uppercase inline-block btn bg-primary_ui-50 hover:bg-primary_ui-50/90 hover:border-primary_ui-50/90 border-primary_ui-50 rounded-none min-w-[200px] font-semibold">
                        <span x-show="!isLoading">{{ t('core.project.save') }}</span>
                        <span x-show="isLoading">{{ t('core.project.saving') }}...</span>
                    </button>
                </div>

            </form>

            {{-- Project info mode: show at the bottom --}}
            @if (isset($project))
                <div class="mt-8">
                    <h2 class="text-2xl font-bold mb-2 text-primary_ui-50">{{ t('core.project.info') }}</h2>
                    <div
                        class="border-2 border-primary_ui-50 rounded-sm bg-gray-100 p-6 flex justify-center items-center min-h-[200px] mb-6">
                        <div class="font-semibold uppercase text-primary_ui-50 text-2xl">{{ t('core.under_construction') }}
                        </div>
                    </div>
                    <div class="flex gap-4 justify-end mb-10">
                        <a href="/dashboard"
                            class="btn rounded-md w-60 text-lg uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90">
                            {{ t('core.dashboard.title') }}
                        </a>
                        <a href="/project/{{$project['id']}}/simulation"
                            class="btn rounded-md w-60 text-lg uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90">
                            {{ t('core.simulation.create') }}
                        </a>
                        {{-- <button type="button"
                            class="btn rounded-md w-52 uppercase bg-primary_ui-50 text-white font-bold py-2 px-6 hover:bg-primary_ui-50/90">
                            {{ t('core.simulation.create') }}
                        </button> --}}
                    </div>
                </div>
            @endif


            <div x-show="showConfirmationScreen" x-cloak x-transition
                class="fixed inset-0 z-50 flex items-start justify-center bg-white">
                <div
                    class="min-h-[340px] mt-[100px] text-center bg-white shadow-xl px-8 py-10 max-w-[800px] w-full relative">
                    <h2 class="font-bold">
                        {{ t('core.project.registration') }} <span class="uppercase text-primary_ui-50"
                            x-text="profile"></span>
                    </h2>
                    <h3 class="text-primary_ui-50 mb-6 font-semibold" x-text="'Project ' + project_name.toUpperCase()">
                    </h3>

                    @if ($subscription)
                        <div class="text-gray-500 space-y-2 text-center">
                            <p>{{ t('core.subscription.credits_year') }}: {{ $subscription['creditsYear'] }}</p>
                            <p>{{ t('core.subscription.credits_month') }}: {{ $subscription['creditsMonth'] }}</p>
                            <p>{{ t('core.subscription.created_month') }}: {{ $subscription['projectsMonth'] }}</p>
                            <p>{{ t('core.subscription.balance_month') }}: {{ $subscription['balanceMonth'] }}</p>
                        </div>
                    @endif

                    <!-- Error/Success feedback -->
                    <template x-if="error">
                        <div class="text-red-600 mt-4 font-semibold text-center" x-text="error"></div>
                    </template>
                    <template x-if="success">
                        <div class="text-green-600 mt-4 font-semibold text-center" x-text="success"></div>
                    </template>

                    <div class="mt-6 flex bottom-0 right-0 gap-5 absolute">
                        <button @click="showConfirmationScreen = false; error = ''; success = ''"
                            class="text-black underline font-semibold">
                            {{ t('core.cancel') }}
                        </button>
                        <button @click="await saveProject()" :disabled="!subscription_id || isLoading || fieldsDisabled"
                            class="bg-primary_ui-50 text-white uppercase px-4 min-w-[200px] py-2 font-bold shadow hover:bg-primary_ui-50/90">
                            <span x-show="!isLoading">{{ t('core.project.save') }}</span>
                            <span x-show="isLoading">{{ t('core.project.saving') }}...</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        @vite('resources/js/project/creation.js')
    </x-slot>
</x-layouts.dashboard>
