<x-layouts.dashboard :styles="$styles" size='full'>
    <x-slot name="content">
        <div class="flex justify-center gap-2">
            <div class="w-[220px]">
            </div>
            <div class="flex-1 w-full max-w-[1110px] flex flex-col gap-8">
                {{-- <div class="w-[220px]">
                </div> --}}
                <div class="">
                    <div class="info text-black my-4">
                        <div>
                            <span class="font-bold">{{ t('core.project.title') }}</span>: {{ $project->project_name ?? '' }}  <a href="{{ route('dashboard.project-info', ['id' => $project->id]) }}" class="text-primary_ui-50 underline">{{ t('core.project.view_info') }}</a>
                        </div>
                        <div>
                            @if(isset($simulation))
                                <span class="font-bold">{{ t('core.simulation.title') }}</span>: {{ $simulation['item_name'] ?? '' }}
                                <span class="italic">(<span class="font-bold">{{ t('core.last_updated_at') }}</span> : {{ $simulation['updated_at'] ?? '' }})</span>
                            @else
                                <span class="font-bold">{{ t('core.simulation.title') }}</span>: -
                            @endif
                        </div>
                    </div>
                    <div class="flex border-2 border-primary_ui-50 min-h-[80vh] mb-2 w-full flex-col justify-center items-center">
                        <img data-src="{{ URL::asset('/images/under-construction.png') }}"
                            src="{{ URL::asset('/images/under-construction.png') }}" class="w-64 md:w-80 mx-auto lazyLoad">
                        <div class="font-semibold uppercase text-primary_ui-50 text-4xl">
                            {{ t('core.under_construction') }}
                        </div>
                    </div>
                </div>
                <!-- Loading Spinner -->
            </div>
            <div class="flex flex-col gap-4 w-[220px] pt-20">
                <button id="save-btn"
                    class="px-6 py-2 bg-primary_ui-50 text-white rounded hover:bg-primary_ui-60 transition">
                    {{ t('core.simulation.save') }}
                </button>
                <button id="view-pdf-btn"
                    class="flex items-center gap-2 px-6 py-2 justify-center bg-primary_ui-50 text-white rounded hover:bg-primary_ui-60 transition">
                    <svg id="view-pdf-loading" class="hidden animate-spin h-6 w-6" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                            stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                    <span>
                        {{ t('core.simulation.view_pdf') }}
                    </span>
                </button>
            </div>
        </div>
        <!-- Save Simulation Modal -->
        <div id="save-simulation-modal" class="fixed inset-0 z-50 text-[17px] flex items-start pt-[200px] justify-center text-black bg-black bg-opacity-30 hidden">
            <div class="bg-white rounded-lg shadow-lg max-w-md w-full p-6 flex gap-4 min-w-[460px]">
                <div>
                    <img data-src="{{ URL::asset('/images/picto-icloud.png') }}"
                            src="{{ URL::asset('/images/picto-icloud.png') }}" class="w-full min-w-[61px] mx-auto lazyLoad">
                </div>
                <div>
                    <div class="font-bold text-lg text-primary_ui-50 mb-2">{{ t('core.simulation.saving') }}</div>

                    <div class="mb-2">{{ t('core.project.title') }} “<span id="modal-project-name"></span>”</div>

                    @if (isset($simulation))
                        <div class="mb-2" id="modal-current-sim-name"></div>
                        <button id="modal-save-btn" class="w-full py-2 mb-4 bg-primary_ui-50 text-white rounded hover:bg-primary_ui-60 font-semibold">{{ t('core.sauvegarder') }}</button>
                    @endif

                    <div class="mb-2">{{ t('core.save_as') }}</div>
                    <input id="simulation-name-input" type="text" class="w-full border rounded p-2" placeholder="{{ t('core.simulation.name') }}" />
                    <div id="modal-save-error" class="text-red-600 mt-2 text-sm hidden"></div>

                    <button id="modal-save-simulation-btn" class="w-full py-2 mt-4 bg-primary_ui-50 text-white rounded hover:bg-primary_ui-60 font-semibold">{{ t('core.simulation.save') }}</button>
                    <div id="modal-save-success" class="text-green-600 mt-2 text-sm hidden"></div>
                </div>
            </div>
        </div>

    </x-slot>
    <x-slot name="scripts">
        <script>
            const simulationId = @json($simulation['id']??null);
            const projectId = @json($project->id??null);
        </script>
        <script>
            const viewBtn = document.getElementById('view-pdf-btn');
            const loadingDiv = document.getElementById('view-pdf-loading');
            viewBtn.addEventListener('click', async function() {
                // Show loading, hide button
                loadingDiv.classList.remove('hidden');
                viewBtn.disabled = true;
                const data = {
                    projectId,
                    simulationId,
                    lang: @json(app()->getLocale()),
                };

                try {
                    const response = await fetch('/api/project-items/generate-pdf', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/pdf',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        },
                        body: JSON.stringify(data)
                    });
                    if (!response.ok) throw new Error('Failed to generate PDF');
                    const blob = await response.blob();
                    // Store PDF blob in sessionStorage as base64 for the editor page
                    const reader = new FileReader();
                    reader.onloadend = function() {
                        sessionStorage.setItem('simulationPdf', reader.result);
                        window.open(`/project/${data.projectId}/simulation${simulationId ? `/${simulationId}` : ''}/pdf`, '_blank');
                    };
                    reader.readAsDataURL(blob);
                } catch (err) {
                    alert('Error generating PDF: ' + err.message);
                } finally {
                    loadingDiv.classList.add('hidden');
                    viewBtn.disabled = false;
                }
            });
        </script>
        <script>
            // Elements
            const saveBtn = document.getElementById('save-btn');
            const modal = document.getElementById('save-simulation-modal');
            const closeModal = () => modal.classList.add('hidden');
            const openModal = () => modal.classList.remove('hidden');
            const modalProjectName = document.getElementById('modal-project-name');
            const modalSimName = document.getElementById('modal-current-sim-name');
            const simulationNameInput = document.getElementById('simulation-name-input');
            const saveSimulationBtn = document.getElementById('modal-save-simulation-btn');
            const saveBtnInModal = document.getElementById('modal-save-btn');
            const modalError = document.getElementById('modal-save-error');
            const modalSuccess = document.getElementById('modal-save-success');
            const errorsTranslated = {
                'core.error.simulation_unique': "{{ t('core.error.simulation_unique') }}",
            }

            // Populate modal with project and simulation info
            function showSaveSimulationModal(projectName, simName) {
                if (modalSimName) modalSimName.textContent = simName ? simName : 'Simulation';
                modalProjectName.textContent = projectName ?? '';
                simulationNameInput.value = simName ?? '';
                modalError.classList.add('hidden');
                modalSuccess.classList.add('hidden');
                openModal();
            }

            // Open modal on save click
            saveBtn.addEventListener('click', function() {
                showSaveSimulationModal(
                    @json($project->project_name ?? ''),
                    @json($simulation['item_name'] ?? '')
                );
            });

            // "Sauvegarder"
            saveBtnInModal?.addEventListener('click', function() {
                closeModal();
            });

            // Actual save: call API to store ProjectItem
            saveSimulationBtn.addEventListener('click', async function() {
                const simulationName = simulationNameInput.value.trim();
                if (!simulationName) {
                    modalError.textContent = "{{ t('core.error.simulation_name_required') }}";
                    modalError.classList.remove('hidden');
                    return;
                }
                saveSimulationBtn.disabled = true;
                modalError.classList.add('hidden');
                modalSuccess.classList.add('hidden');

                const payload = {
                    project_id: projectId,
                    item_name: simulationName,
                    item_type: "simulation",
                    // Optional: You can include item_info if you want to store additional simulation data
                    // item_info: {},
                    created_by: @json(auth()->id() ?? null),
                    last_user_to_interact: @json(auth()->id() ?? null),
                };

                try {
                    const response = await fetch('/api/project-items', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify(payload),
                    });
                    const data = await response.json();
                    if (!response.ok) {
                        const errorMsg = data.message || (data.errors ? Object.values(data.errors).flat().join(", ") : "Erreur inconnue");
                        modalError.textContent = errorsTranslated[errorMsg] || errorMsg;
                        modalError.classList.remove('hidden');
                        saveSimulationBtn.disabled = false;
                        return;
                    }
                    modalSuccess.textContent = "{{ t('core.simulation.save_success') }}";
                    modalSuccess.classList.remove('hidden');
                    setTimeout(() => {
                        closeModal();
                        window.location.href = `/project/${payload.project_id}/simulation/${data?.data?.id}`;
                    }, 1200);
                } catch (e) {
                    console.error(e);
                    modalError.textContent = "{{ t('core.simulation.save_error') }}";
                    modalError.classList.remove('hidden');
                } finally {
                    saveSimulationBtn.disabled = false;
                }
            });

            // Optional: Close modal on outside click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) closeModal();
            });
        </script>
    </x-slot>
</x-layouts.dashboard>
