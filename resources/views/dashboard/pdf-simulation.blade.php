<!DOCTYPE html>
<html 
    lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ in_array(app()->getLocale(), config('app.rtl_languages')) ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @vite(['resources/sass/components/pdf_editor.scss'])

</head>
<body>
    <x-pdf.pdf />

    <script src="{{ config('services.cdn.url') }}/assets/libs/pdf.js/2.12.313/pdf.min.js"></script>
    <script src="{{ config('services.cdn.url') }}/assets/libs/pdf-lib/1.17.1/pdf-lib.min.js"></script>
    <script src="{{ config('services.cdn.url') }}/assets/libs/sortable/1.14.0/sortable.min.js"></script>
    <script src="/js/pdf/pdf-editor-clean.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load the PDF from sessionStorage
            const pdfDataUrl = sessionStorage.getItem('simulationPdf');
            // const pdfDataUrl = "http://dashboard.localhost:5500/pdf/test_pdf.pdf";
            if (!pdfDataUrl) {
                alert('No PDF found. Please generate the PDF again.');
                window.history.back();
                return;
            }
            // Convert base64 data URL to Blob
            fetch(pdfDataUrl)
                .then(res => res.blob())
                .then(blob => {
                    const editor = new PdfEditor('editor-1', pdfDataUrl);
                    editor.loadPdf(blob);
                });
        });
    </script>
</body>
</html>
