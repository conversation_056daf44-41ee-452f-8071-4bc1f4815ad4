@props(['title' => '', 'size' => 'md'])
@php
    $sizeClass = match ($size) {
        'sm' => 'max-w-sm',
        'md' => 'max-w-md',
        'lg' => 'max-w-lg',
        'xl' => 'max-w-xl',
        '2xl' => 'max-w-2xl',
        default => 'max-w-md',
    };
@endphp

<div
    x-data="{ show: @entangle($attributes->wire('model')) }"
    x-show="show"
    x-transition.opacity.duration.300ms
    @keydown.escape.window="show = false"
    @click.self="show = false"
    class="fixed inset-0 flex items-start justify-center bg-black/50 z-[9999] overflow-y-auto pt-10 "
    x-cloak
>
    <div
        @click.stop
        x-transition.scale.duration.300ms
        class="bg-white rounded-xl shadow-xl w-full {{ $sizeClass }} p-6 relative"
    >
        <div class="flex items-center gap-2 mb-4">
            <span class="cursor-pointer pt-[2px] bg-[#ED7A2E] text-white rounded-full font-bold text-[20px] text-center w-[40px] h-[40px] inline-flex justify-center items-center min-w-[40px] min-40-[29px]">
                ?
            </span>
            <h2 class="text-xl font-bold ms-3">{{ $title }}</h2>
        </div>

        {{ $slot }}
    </div>
</div>
