@props([
    'model',
    'value',
    'size' => 'md',
    'class' => '',
    'disabled' => 'false',
    'dataTs' => '',
])

@php
    $isDisabled = isset($disabled) && $disabled === 'true';
    $sizes = [
        'lg' => [
            'labelClass' => 'ms-10 rtl:ms-0 rtl:me-10 text-3xl font-semibold',
            'custRadioClass' => 'h-[40px] w-[40px] border-[2px]',
            'custRadioInnerClass' => 'after:h-7 after:w-7',
        ],
        'md' => [
            'labelClass' => 'ms-2 rtl:ms-0 rtl:me-2  text-lg',
            'custRadioClass' => 'h-[20px] w-[20px] border-[1px]',
            'custRadioInnerClass' => 'after:h-3 after:w-3',
        ],
    ];

    $labelClass = $sizes[$size]['labelClass'] ?? '';
    $custRadioClass = $sizes[$size]['custRadioClass'] ?? '';
    $custRadioInnerClass = $sizes[$size]['custRadioInnerClass'] ?? '';
@endphp

<label class="relative ps-4 flex items-center cursor-pointer {{ $class }}">
    <input
        type="radio"
        @if ($isDisabled)
            disabled
        @endif
        x-model="{{ $model }}"
        value="{{ $value }}"
        class="sr-only peer" />

    {{-- Label text --}}
    <span class="text-black input-label {{ $labelClass }}" data-ts="{{ $dataTs }}">
        {{ $slot }}
    </span>

    {{-- Custom radio appearance using ::before and ::after --}}
    <span class="pointer-events-none absolute left-0 top-1/2 -translate-y-1/2 rounded-full
                 before:content-[''] before:absolute before:inset-0 before:rounded-full before:border before:border-primary_ui-50
                 after:content-[''] after:absolute after:rounded-full after:bg-primary_ui-50
                 after:transition-transform after:duration-200 after:scale-0 after:top-1/2 after:-translate-y-1/2 after:left-1/2 after:-translate-x-1/2
                 peer-checked:after:scale-100
                 {{ $custRadioClass }} {{ $custRadioInnerClass }}">
    </span>
</label>
