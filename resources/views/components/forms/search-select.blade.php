@props([
    'name',
    'options' => [],
    'placeholder' => 'Rechercher...',
    'value' => null,
    'required' => false,
    'disabled' => false,
    'label' => null,
    'labelClass' => 'block text-lg py-1',
    'class' => '',
    'dataTs' => '',
    'onChange' => null, // New prop for change callback
])

<div class="{{ $class }}">
    @if($label)
        <label class="{{ $labelClass }}">
            <span data-ts="{{ $dataTs }}">{{ $label }}</span>&nbsp;
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <div
        x-data="(() => {
            const initial = @js($value);
            const options = @js($options);
            const onChange = @js($onChange);

            return {
                open: false,
                query: '',
                highlighted: 0,
                all: options,
                selected: initial ? options.find(o => o.value == initial) || null : null,

                get filtered() {
                    if (!this.query) return this.all;
                    const q = this.query.toLowerCase();
                    return this.all.filter(o =>
                        o.label.toLowerCase().includes(q) ||
                        String(o.value).toLowerCase().includes(q)
                    );
                },

                init() {
                    if (initial) {
                        const opt = this.all.find(o => o.value == initial) || null;
                        this.selected = opt;
                        this.query = opt ? opt.label : '';
                    }

                    // Watch for changes to selected value
                    this.$watch('selected', (newSelected, oldSelected) => {
                        // Call your custom function here
                        this.onSelectionChange(newSelected, oldSelected);

                        // Dispatch custom event
                        this.$dispatch('select-changed', {
                            selected: newSelected,
                            previous: oldSelected
                        });
                    });
                },

                onSelectionChange(newSelected, oldSelected) {
                    if (onChange && typeof window[onChange] === 'function' && newSelected?.value != oldSelected?.value) {
                        window[onChange](newSelected?.value);
                    }
                },

                toggle() { this.open ? this.close() : this.openAndFocus(); },
                close() {
                    this.open = false;
                    if (this.selected) {
                        this.query = this.selected.label;
                    } else {
                        this.highlighted = 0;
                    }
                },
                openAndFocus() {
                    this.open = true;
                    this.query = '';
                    this.highlighted = 0;
                    this.$nextTick(() => this.$refs.input.focus());
                },

                move(step) {
                    if (!this.open) this.openAndFocus();
                    const max = this.filtered.length - 1;
                    this.highlighted = Math.max(0, Math.min(max, this.highlighted + step));
                    this.ensureVisible();
                },

                ensureVisible() {
                    this.$nextTick(() => {
                        const list = this.$el.querySelector('[x-show] + div, [x-show].absolute');
                        const item = list?.querySelectorAll('[x-on\\:mouseenter]')[this.highlighted];
                        if (list && item) {
                            const liTop = item.offsetTop, liBottom = liTop + item.offsetHeight;
                            const vTop = list.scrollTop, vBottom = vTop + list.clientHeight;
                            if (liTop < vTop) list.scrollTop = liTop;
                            else if (liBottom > vBottom) list.scrollTop = liBottom - list.clientHeight;
                        }
                    });
                },

                select(opt) {
                    this.selected = opt;
                    this.query = opt.label;
                    this.close();
                },

                selectHighlighted() {
                    const opt = this.filtered[this.highlighted];
                    if (opt) this.select(opt);
                },
            }
        })()"
        x-on:click.away="close()"
        x-on:select-changed="console.log('Custom event received:', $event.detail)"
        class="relative"
    >
        {{-- Search / Display field --}}
        <div class="flex items-center border rounded-md h-10 px-3 bg-white cursor-text"
             :class="open ? 'ring-2 ring-primary_ui-50 border-primary_ui-50' : 'border-primary_ui-50'">
            <input
                x-ref="input"
                x-model="query"
                x-on:focus="openAndFocus()"
                x-on:keydown.arrow-down.prevent="move(1)"
                x-on:keydown.arrow-up.prevent="move(-1)"
                x-on:keydown.enter.prevent="selectHighlighted()"
                x-on:keydown.escape.prevent="close()"
                type="text"
                class="w-full outline-none bg-transparent"
                :placeholder="selected ? selected.label : '{{ $placeholder }}'"
                :disabled="@js($disabled)"
            />
            <button type="button" class="ms-2" x-on:click="toggle()">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none"
                     viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 9l-7 7-7-7"/>
                </svg>
            </button>
        </div>

        {{-- Dropdown --}}
        <div x-show="open" x-transition
             class="absolute z-20 mt-1 w-full max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-lg">
            <template x-if="filtered.length === 0">
                <div class="px-3 py-2 text-sm text-gray-500">Aucun résultat</div>
            </template>

            <template x-for="(opt, idx) in filtered" :key="opt.value">
                <div
                    class="px-3 py-2 text-sm cursor-pointer"
                    :class="{
                        'bg-gray-100': idx === highlighted,
                        'font-medium': selected && selected.value === opt.value
                    }"
                    x-on:mouseenter="highlighted = idx"
                    x-on:mousedown.prevent="select(opt)"
                    x-text="opt.label"
                ></div>
            </template>
        </div>

        {{-- Hidden input --}}
        <input type="hidden" name="{{ $name }}" :value="selected ? selected.value : ''" />
    </div>
</div>
