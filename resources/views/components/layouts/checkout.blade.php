<!doctype html>
<html 
    lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    class="scroll-smooth h-full"
    dir="{{ in_array(app()->getLocale(), config('app.rtl_languages')) ? 'rtl' : 'ltr' }}"
>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @include('components.layouts.partials.head')
    @include('components.layouts.partials.env')
    @vite(['resources/css/fonts.css', 'resources/css/app.css', 'resources/js/app.js', 'resources/js/translation.js'])
</head>
<body class="text-primary-900 flex flex-col min-h-screen"  x-data>
    <div id="checkout" class="flex flex-col flex-grow">
        
        <x-layouts.partials.dashboard.custom-header class="flex-shrink-0" />

        <div class="relative">
            {{ $slot }}
        </div> 

        @include('components.layouts.partials.tail')
    </div>
    <x-impersonate::banner/>

    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js" defer></script>
</body>
</html>
