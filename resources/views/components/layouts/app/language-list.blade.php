@php
    use App\Services\TranslationApiService;
    use App\Services\LanguageService;

    $apiService = app(TranslationApiService::class);
    $apiLangService = app(LanguageService::class);
    $languages = $apiService->getAllLanguages(['limit' => 200]);

    $host = request()->getHost();
    $isAdminSubdomain =
        str_starts_with($host, env('SUBDOMAIN_ADMIN') . '.') ||
        str_starts_with($host, env('SUBDOMAIN_DASHBOARD') . '.');

    $actualPath = urldecode(request()->getPathInfo());
    $pathSegments = explode('/', trim($actualPath, '/'));
    $isHomepage = count($pathSegments) === 1 && !$isAdminSubdomain;

    $pathTranslated = [];
    if (!$isHomepage && !$isAdminSubdomain) {
        $basePath = $actualPath;
        if (strpos($actualPath, '/en') !== 0) {
            $pathTranslatedTemp = $apiService->getTranslateUrlForLang($actualPath, 'en');
            if (isset($pathTranslatedTemp[0]['path'])) {
                $basePath = $pathTranslatedTemp[0]['path'];
            }
        }
        if ($basePath) {
            $pathTranslated = $apiService->getTranslateUrlForLang($basePath);
        }
    }
@endphp

@props([
    'updateSessionLanguage' => false,
])

<div
    x-data="{ search: '', showAll: false, isLoading: false }"
    x-cloak
    class="w-full max-w-4xl space-y-2 border border-gray-300/50 bg-white px-2 py-2"
>
    <!-- Search Box -->
    <div>
        <input
            type="text"
            placeholder="{{ t('core.search_language') }}"
            class="input input-bordered rounded-none w-full dark:bg-gray-800 dark:text-white"
            x-model="search"
        />
    </div>

    <!-- Language List -->
    <ul
        tabindex="0"
        class="menu menu-lg max-h-[410px] overflow-auto w-[250px] md:w-[450px] lg:w-[650px] ul-lang z-[122] p-2 ps-3 bg-white grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2"
    >
        @php $maxVisible = 35; @endphp
        @foreach ($languages['data'] as $index => $language)
            @php
                $isoCode = $language['languageISO2'];
                $finalUrl = '';

                if ($isAdminSubdomain) {
                    $finalUrl = '/lang/' . $isoCode;
                } elseif ($isHomepage) {
                    $finalUrl = '/' . $isoCode;
                } else {
                    $finalUrl = getTranslatedUrlForIso($pathTranslated, $isoCode);
                    if (is_null($finalUrl)) {
                        $finalUrl = '/' . $isoCode;
                    }
                }
            @endphp

            <template
                x-if="search === '' ? ({{ $index }} < {{ $maxVisible }} || showAll) : '{{ strtolower($language['languageFullNative']) }}'.includes(search.toLowerCase())"
            >
                @if ($updateSessionLanguage)
                    {{-- Session-update mode: call /lang/{iso2} then reload --}}
                    <x-nav.item
                        href="#"
                        @click.prevent="
                            if (isLoading) return;
                            isLoading = true;
                            Alpine?.store('global')?.showLoading()
                            fetch(`/lang/{{ $isoCode }}`)
                                .then(() => window.location.reload())
                                .catch(() => window.location.reload())
                                .finally(() => {
                                    isLoading = false;
                                    Alpine?.store('global')?.hideLoading();
                                });
                        "
                        linkClass="w-full h-full"
                    >
                        <span class="flex flex-nowrap items-center min-h-[25px]">
                            <span class="me-2 w-[1.1em] flag-icon flag-icon-{{ strtolower($language['flagCode']) }}"></span>
                            <span class="text-xs uppercase text-black">
                                {{ $language['languageFullNative'] }}
                            </span>
                        </span>
                    </x-nav.item>
                @else
                    {{-- Default behavior: navigate to computed translated URL --}}
                    <x-nav.item :href="$finalUrl" linkClass="w-full h-full">
                        <span class="flex flex-nowrap items-center min-h-[25px]">
                            <span class="me-2 w-[1.1em] flag-icon flag-icon-{{ strtolower($language['flagCode']) }}"></span>
                            <span class="text-xs uppercase text-black">
                                {{ $language['languageFullNative'] }}
                            </span>
                        </span>
                    </x-nav.item>
                @endif
            </template>

            @if ($index === $maxVisible)
                <!-- More Languages Button -->
                <template x-if="!showAll && search === ''">
                    <button
                        type="button"
                        @click.stop="showAll = true; $event.stopPropagation(); $event.preventDefault();"
                    >
                        ... {{ t('core.more_languages') }}
                    </button>
                </template>
            @endif
        @endforeach
    </ul>
</div>
