@php
    $user = auth()->user();
    $companyLogoUrl = $user->getCompanyLogoUrl();
    $avatarUrl = $user->getFilamentAvatarUrl();
@endphp

@if ($companyLogoUrl)
    <div class="w-full h-20 relative overflow-hidden z-[99]">
        <img
            src="{{ $companyLogoUrl }}"
            alt="Company Cover"
            class="w-full h-full object-cover object-center transition duration-300 ease-in-out"
            loading="lazy"
            decoding="async"
        />
    </div>
@else
    <div class="w-full h-20 relative bg-primary_ui_high-50 z-[99]"></div>
@endif


<div class="relative mt-0 md:-mt-14 flex flex-col items-center bg-custom-menu-left">
    <div class="w-28 h-28 rounded-full overflow-hidden border-4 border-white shadow-md z-[100]">
        <img
            src="{{ $avatarUrl ?: asset('images/icon/profile.png') }}"
            alt="User Avatar"
            class="object-cover w-full h-full"
        />
    </div>

    <div class="mt-2 w-full text-center py-2">
        <p class="text-base font-bold uppercase text-black">{{ $user->name }} {{ $user->public_name }}</p>
        <p class="text-xs text-gray-500" data-ts="core.register_since">
            {{t("core.register_since")}} {{ $user->created_at->diffForHumans(null, true) }}
        </p>
    </div>
</div>


<ul class="space-y-1 py-3 px-2 text-sm font-medium bg-custom-menu-left">
    <li class="fi-custom-menu-container">
        <a href="/my-settings" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('my-settings') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/weather-blue.png') }}" width="32" />
            <span data-ts="core.my_settings.title">{{ t("core.my_settings.title") }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container">
        <a href="/my-profile" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('my-profile') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/user-circle-blue.png') }}" width="32" />
            <span data-ts="core.my_account.title">{{ t("core.my_account.title") }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container">
        <a href="/notifications" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('notifications') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/bell-blue.png') }}" width="32" />
            <span data-ts="core.notifications">{{ t('core.notifications') }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container">
        <a href="/payment-methods" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('payment-methods') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/weather-blue.png') }}" width="32" />
            <span data-ts="core.payment_method">{{ t("core.payment_method") }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container">
        <a href="/policies" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('policies') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/reset-circle-blue.png') }}" width="32" />
            <span data-ts="core.policies">{{ t("core.policies") }}</span>
        </a>
    </li>
    {{-- <li class="fi-custom-menu-container">
        <a href="/orders" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('orders') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/reset-circle-blue.png') }}" width="32" />
            <span>Orders</span>
        </a>
    </li> --}}
    <li class="fi-custom-menu-container">
        <a href="/subscriptions" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('subscriptions') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/reset-circle-blue.png') }}" width="32" />
            <span data-ts="core.my_sub">{{ t("core.my_sub") }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container">
        <a href="/transactions" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1
        {{ request()->is('transactions') ? 'text-primary_ui_high-50' : '' }}">
            <img src="{{ asset('images/menu/reset-circle-blue.png') }}" width="32" />
            <span data-ts="core.payement_title">{{ t("core.payement_title") }}</span>
        </a>
    </li>
    <li class="fi-custom-menu-container fi-custom-menu-container-end">
        <form method="POST" action="{{ route('cross-logout') }}" class="w-full">
            @csrf
            <button type="submit" class="text-black flex items-center justify-start text-center gap-x-3 w-full uppercase text-xs font-semibold p-1">
                <img src="{{ asset('images/menu/x.png') }}" class="x-icon-image" />
                <span>Logout</span>
            </button>
        </form>
    </li>
</ul>
