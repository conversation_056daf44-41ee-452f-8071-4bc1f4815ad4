<li class="pb-1 relative"
    @mouseenter="hoveredProject = {{ $project->id }}"
    @mouseleave="hoveredProject = null"
>
    <a class="text-black flex font-medium justify-between hover:font-bold" href="/project-info/{{ $project->id }}">
        <span>{{ $project->project_name }}</span>
        <svg xmlns="http://www.w3.org/2000/svg" class="w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
        </svg>
    </a>
    <div x-show="hoveredProject === {{ $project->id }}" class="absolute left-full top-0 ms-1 w-64 bg-gray-200 shadow-lg rounded p-4 z-50" style="display: none;" x-transition>
        <a href="/project-info/{{ $project->id }}" class="flex items-baseline mb-2 text-primary_ui-50">
            <img src="{{ asset('images/dashboard/graph-icone.png') }}" width="20" class="me-1" />
            <span>
                {{ t('core.project.view_info') }}
            </span>
        </a>
        @if($project->items->count())
            <div class="mb-2 text-primary_ui-50 font-bold">{{ t('core.simulation.my_latest') }}</div>
            <ul class="mb-2">
                @foreach($project->items->take(2) as $item)
                    <li>
                        <a href="/project/{{ $project->id }}/simulation/{{ $item->id }}" class="hover:font-bold text-black">{{ $item->item_name ?? 'Simulation #' . $item->id }}</a>
                    </li>
                @endforeach
            </ul>
            <a href="/dashboard?id={{ $project->id }}" class="text-primary_ui-50 hover:font-bold">{{ t("core.simulation.all_my") }}</a>
        @else
            <a href="/project/{{ $project->id }}/simulation" class="flex items-center mt-2 text-primary_ui-50 hover:font-bold">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
                <span>{{ t('core.simulation.create_new') }}</span>
            </a>
        @endif
    </div>
</li>
