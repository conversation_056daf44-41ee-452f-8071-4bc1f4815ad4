@props(['product'])

@php
    $mainFeatures = array_slice($product['features'], 0, 2);
    $extraFeatures = array_slice($product['features'], 2);
@endphp

<div  x-data="{ showModal: false }" class="{{ $product['recommande'] ? 'bg-primary_ui-50 text-white' : 'bg-white text-primary_ui-50' }} rounded shadow-2xl p-6 pt-2">
    <div class="flex-grow flex flex-col justify-start">
        <div class="text-center mb-6">
            <div class="h-16 flex items-start justify-center px-3 py-3">
                <h3 class="text-2xl font-bold {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} uppercase leading-tight tracking-tight">
                {{ $product['name'] == 'Gratuit' ? t('ui_product_free') : $product['name'] }}
                </h3>
            </div>
            <div class="h-16 flex items-center justify-center mt-2">
                @if($product['slug'])
                    <a href="/checkout/plan/{{ $product['slug'] }}" class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                            {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                    </a>
                @else
                    <a href="/register"  class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                            {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                    </a>
                @endif
            </div>
            <div class="h-16 flex items-center justify-center">
                <p class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold text-center leading-normal tracking-normal">
                @if($product['type'] == 'Monthly')
                    {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-monthly') }}
                @else
                    {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-yearly') }}
                @endif
                </p>
            </div>
        </div>

        <div class="space-y-3 mb-6">
            <div
                class="h-15 text-center text-sm font-bold
                {{ $product['recommande'] ? 'text-white border-white' : 'text-primary_ui-50 border-primary_ui-50' }}
                border-t pt-2 border-b pb-2"
            >
                @if ($product['dossier'] > 1)
                    {{ $product['dossier'] }} {{ t('ui_sub_credits_per_project') }} {{ $product['type'] == 'Monthly' ? t('ui_sub_project_monthly') : t('ui_sub_project_yearly') }} <br>
                    <span class="text-xs">
                        @if(isset($product['price'], $product['dossier']) && is_numeric($product['price']) && is_numeric($product['dossier']) && $product['dossier'] != 0)
                            {{ t('ui_sub_either') }} {{ number_format($product['price'] / $product['dossier'], 2) }} € {{ t('ui_sub_each_project') }}
                        @else
                            <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                        @endif
                    </span>
                @else
                    {{ t('ui_sub_one_credit') }}<br>
                    <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                @endif
            </div>
            <div class="flex items-start space-x-2">
                <div class="w-5 h-5 flex-shrink-0">
                    <img  style="width: 17px;height: 25px !important;height: 20px !important;}" src="/images/icon/check.svg" />
                </div>
                <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                    {{ $product['user_count'] }} {{ $product['user_count'] === 1 ? t('ui_sub_user') : t('ui_sub_users') }}
                </span>
            </div>
            @foreach ($mainFeatures as $feature)
                <div class="flex items-start space-x-2">
                    <div class="w-5 h-5 flex-shrink-0">
                        <img  style="width: 17px;margin-top: -2px;height: 25px !important;height: 20px !important;}" src="/images/icon/<?= $feature['enabled'] ? 'check.svg' : 'x-cross.png' ?>" />
                    </div>
                    <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                        {{ t('ui_sub_feature_'.$feature['key']) }}
                    </span>
                </div>
            @endforeach
            @foreach ($extraFeatures as $feature)
                <div class="hidden md:flex items-start space-x-2">
                    <div class="w-5 h-5 flex-shrink-0">
                        <img  style="width: 17px;margin-top: -2px;height: 25px !important;height: 20px !important;}" src="/images/icon/<?= $feature['enabled'] ? 'check.svg' : 'x-cross.png' ?>" />
                    </div>
                    <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                        {{ t('ui_sub_feature_'.$feature['key']) }}
                    </span>
                </div>
            @endforeach
        </div>
    </div>
    @if($product['slug'])
        <a href="/checkout/plan/{{ $product['slug'] }}"
            class="w-full {{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
            {{  t('ui_sub_subscribe') }}
        </a>
    @else
        <a  href="/register"
            class="w-full {{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
            {{  t('ui_sub_register') }}
        </a>
    @endif


    {{-- View More Button (only if there are extra features) --}}
    @if(count($extraFeatures) > 0)
        <button
            @click="showModal = true"
            class="{{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} flex md:hidden text-xs mt-5 underline items-center justify-center w-full"
        >
            {{ t('core.view_more') }}
            <svg class="w-4 h-4 ms-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
        </button>
    @endif

    {{-- Modal --}}
    <div
        x-show="showModal"
        x-transition
        class="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-50"
        style="display: none;"
    >
        <div
            class="{{ $product['recommande'] ? 'bg-primary_ui-50 text-white' : 'bg-white text-primary_ui-50' }} rounded-lg shadow-lg max-w-sm w-full p-6 relative"
            @click.away="showModal = false"
        >
            {{-- Close Button --}}
            <button
                @click="showModal = false"
                class="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
            >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>


            <div class="flex-grow flex flex-col justify-start">
                <div class="text-center mb-6">
                    <div class="h-16 flex items-start justify-center px-3 py-3">
                        <h3 class="text-2xl font-bold {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} uppercase leading-tight tracking-tight">
                        {{ $product['name'] == 'Gratuit' ? t('ui_product_free') : $product['name'] }}
                        </h3>
                    </div>
                    <div class="h-16 flex items-center justify-center mt-2">
                        @if($product['slug'])
                            <a href="/checkout/plan/{{ $product['slug'] }}" class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                                    {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                            </a>
                        @else
                            <a href="/register"  class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'text-yellow_custom-50 bg-primary_ui-50' }} px-4 py-2 rounded-md text-2xl font-bold">
                                    {{ number_format($product['price'], 0) }}€ / {{ $product['type'] == 'Monthly' ? t('ui_sub_monthly_2') : t('ui_sub_yearly_2') }}
                            </a>
                        @endif
                    </div>
                    <div class="h-16 flex items-center justify-center">
                        <p class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold text-center leading-normal tracking-normal">
                        @if($product['type'] == 'Monthly')
                            {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-monthly') }}
                        @else
                            {{ $product['name'] == 'Gratuit' ? t('ui_product_description_free') : t('ui_sub_desc_product_'.$product['description_translation'].'-yearly') }}
                        @endif
                        </p>
                    </div>
                </div>

                <div class="space-y-3 mb-6">
                    <div
                        class="h-15 text-center text-sm font-bold
                        {{ $product['recommande'] ? 'text-white border-white' : 'text-primary_ui-50 border-primary_ui-50' }}
                        border-t pt-2 border-b pb-2"
                    >
                        @if ($product['dossier'] > 1)
                            {{ $product['dossier'] }} {{ t('ui_sub_credits_per_project') }} {{ $product['type'] == 'Monthly' ? t('ui_sub_project_monthly') : t('ui_sub_project_yearly') }} <br>
                            <span class="text-xs">
                                @if(isset($product['price'], $product['dossier']) && is_numeric($product['price']) && is_numeric($product['dossier']) && $product['dossier'] != 0)
                                    {{ t('ui_sub_either') }} {{ number_format($product['price'] / $product['dossier'], 2) }} € {{ t('ui_sub_each_project') }}
                                @else
                                    <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                                @endif
                            </span>
                        @else
                            {{ t('ui_sub_one_credit') }}<br>
                            <span class=" {{ $product['recommande'] ? 'text-primary_ui-50' : 'text-white' }}">-</span>
                        @endif
                    </div>
                    <div class="flex items-start space-x-2">
                        <div class="w-5 h-5 flex-shrink-0">
                            <img  style="width: 17px;height: 25px !important;height: 20px !important;}" src="/images/icon/check.svg" />
                        </div>
                        <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                            {{ $product['user_count'] }} {{ $product['user_count'] === 1 ? t('ui_sub_user') : t('ui_sub_users') }}
                        </span>
                    </div>
                    @foreach ($product['features'] as $feature)
                        <div class="flex items-start space-x-2">
                            <div class="w-5 h-5 flex-shrink-0">
                                <img  style="width: 17px;margin-top: -2px;height: 25px !important;height: 20px !important;}" src="/images/icon/<?= $feature['enabled'] ? 'check.svg' : 'x-cross.png' ?>" />
                            </div>
                            <span class="text-sm {{ $product['recommande'] ? 'text-white' : 'text-primary_ui-50' }} font-bold">
                                {{ t('ui_sub_feature_'.$feature['key']) }}
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
            @if($product['slug'])
                <a href="/checkout/plan/{{ $product['slug'] }}"
                    class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
                    {{  t('ui_sub_subscribe') }}
                </a>
            @else
                <a  href="/register"
                    class="{{ $product['recommande'] ? 'bg-yellow_custom-50 text-primary_ui-50' : 'bg-primary_ui-50 text-white' }} py-3 px-4 rounded-lg font-semibold transition-colors text-center block">
                    {{  t('ui_sub_register') }}
                </a>
            @endif

            {{-- Close Button --}}
            <button
                @click="showModal = false"
                class="{{ $product['recommande'] ? 'bg-gray-200/10 text-white' : 'bg-gray-200 text-black' }} mt-4 w-full py-2 px-3 text-xs rounded-lg font-semibold transition-colors text-center block"
            >
                View all subscription
            </button>

        </div>
    </div>
</div>
