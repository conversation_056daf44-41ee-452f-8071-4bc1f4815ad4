<div class="modal-backdrop" id="modal-backdrop-pdf">
    <div id='pdf-modal' class="modal modal-box-shadow">
        <div id="loading"></div>
        <div class="pdf-editor">
            <div id="editor-1">
                <div style="width: 100%;">
                    <div class="modal-info">
                        <div class="modal-title">
                            <div class="left-title">
                                <h4 id="title">{{ t("core.pdf_editor_component") }}</h4>
                                <input type="text" id="name" name="name" required minlength="4" size="50"
                                    value="PDF EDITOR COMPONENT" style="display: none; width: 100%; padding: 5px;" />
                                <span id="edit-icon" class="zoom-button" title="Modifier">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-pencil">
                                        <path
                                            d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z" />
                                        <path d="m15 5 4 4" />
                                    </svg>
                                </span>
                                <span style="display: none;" id="close-icon" class="zoom-button" title="Sauvegarder">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-save">
                                        <path
                                            d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z" />
                                        <path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7" />
                                        <path d="M7 3v4a1 1 0 0 0 1 1h7" />
                                    </svg>
                                </span>
                            </div>
                            <div class="zoom-container">
                                <h4 class="organise">{{ t('core.organis_download') }}</h4>
                                <div id="edit-mode" title="Organise" class="edit-mode"><i class="bi bi-stack"></i></div>
                                <div class="zoom-left">
                                    <span id="zoom-in" class="zoom-button" title="Zoom +">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-zoom-in">
                                            <circle cx="11" cy="11" r="8" />
                                            <line x1="21" x2="16.65" y1="21" y2="16.65" />
                                            <line x1="11" x2="11" y1="8" y2="14" />
                                            <line x1="8" x2="14" y1="11" y2="11" />
                                        </svg>
                                    </span>
                                    <span id="zoom-out" class="zoom-button" title="Zoom -">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-zoom-out">
                                            <circle cx="11" cy="11" r="8" />
                                            <line x1="21" x2="16.65" y1="21" y2="16.65" />
                                            <line x1="8" x2="14" y1="11" y2="11" />
                                        </svg>
                                    </span>
                                    <span id="zoom-reset" class="zoom-button" title="Réinitialiser">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-rotate-ccw">
                                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                                            <path d="M3 3v5h5" />
                                        </svg>
                                    </span>
                                    <span id="download" class="zoom-button" title="Download">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round"
                                            class="lucide lucide-download">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                            <polyline points="7 10 12 15 17 10" />
                                            <line x1="12" x2="12" y1="15" y2="3" />
                                        </svg>
                                    </span>
                                </div>

                                <span onclick="closePdfEditorModal('editor-1')" title="Fermer" class="zoom-button">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="lucide lucide-x">
                                        <path d="M18 6 6 18" />
                                        <path d="m6 6 12 12" />
                                    </svg></span>
                            </div>
                        </div>
                        <div class="preview-container">
                            <div class="thumbnail-wrapper">
                                <div class="thumbnail-container">
                                </div>
                            </div>

                            <div class="page-container">
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="loading-modal" id="pdf-loading-modal">
    <div>
        <div class="loader"></div>
        <div class="message-container">
            <div class="message"></div>
            <span class="loading-dots" style="display: none;">
                <span>.</span>
                <span>.</span>
                <span>.</span>
            </span>
        </div>
    </div>
</div>
