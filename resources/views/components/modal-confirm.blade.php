@props([
    'show' => 'showConfirmModal',
    'onConfirm' => 'handleConfirm()',
    'message' => null
])

<div
    x-show="{{ $show }}"
    x-transition.opacity.duration.300ms
    @keydown.escape.window="{{ $show }} = false"
    @click.self="{{ $show }} = false"
    class="fixed inset-0 flex items-center justify-center bg-black/50 z-[9999] overflow-y-auto pt-20"
    x-cloak
>
    <div
        @click.stop
        x-transition.scale.duration.300ms
        class="bg-white rounded-lg shadow-md w-full max-w-2xl p-8"
    >
        <p class="text-center text-lg leading-6 text-gray-900">
            @if($message)
                {{ $message }}
            @else
                {{ $slot }}
            @endif
        </p>
        <div class="mt-6 flex justify-center gap-4">
            <button
                @click="{{ $show }} = false"
                class="px-6 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium"
            >
                {{ t('ui_no') }}
            </button>
            <button
                @click="{{ $onConfirm }}"
                class="px-6 py-2 rounded bg-primary_ui-50 hover:bg-primary_ui-100 text-white font-medium"
            >
                {{ t('ui_yes') }}
            </button>
        </div>
    </div>
</div>