<div class="flex flex-col md:flex-row gap-6 md:gap-10 relative">
    <div class="w-full md:w-1/2">
        <div class="space-y-3">
            <x-forms.custom-radio size="lg" model="locationType" value="address">{!! t('location.address') !!}</x-forms.custom-radio>
            <x-forms.custom-radio size="lg" model="locationType" value="gps-geolocated">{!! t('location.gps_geolocated') !!}</x-forms.custom-radio>
            <x-forms.custom-radio size="lg" model="locationType" value="gps-point">{!! t('location.gps_point') !!}</x-forms.custom-radio>
        </div>

        <div x-show="locationType === 'address' || locationType === 'gps-geolocated'" x-transition x-cloak class="mt-6 space-y-3">
            <input type="text" placeholder="{!! t('location.placeholders.country') !!}" x-model="address.country" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="{!! t('location.placeholders.city') !!}" x-model="address.city" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="{!! t('location.placeholders.street') !!}" x-model="address.street" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="text" placeholder="{!! t('location.placeholders.number') !!}" x-model="address.number" @input.debounce.750ms="geocodeAddress()"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
        </div>

        <div x-show="locationType === 'gps-point'" x-transition x-cloak class="mt-6 space-y-3">
            <input type="number" step="any" placeholder="{!! t('location.placeholders.latitude') !!}" x-model.number="latitude" :disabled="locationType === 'gps-geolocated'"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
            <input type="number" step="any" placeholder="{!! t('location.placeholders.longitude') !!}" x-model.number="longitude" :disabled="locationType === 'gps-geolocated'"
                class="w-full text-2xl md:text-3xl rounded-md border-2 placeholder-black/75 border-black/75 px-4 py-2 md:px-5 md:py-2 focus:border-primary_ui-50 focus:outline-none" />
        </div>
    </div>

    <div 
        class="w-full md:w-1/2 h-96 md:h-auto"
        x-transition
        x-cloak
        x-on:map-update.window="
            latitude = $event.detail.lat;
            longitude = $event.detail.lng;
            updateAddressFromCoords(); 
        "
    >
        <x-map.map-picker :distance-km="0.1"></x-map.map-picker>
    </div>

    <div
        x-show="isLoading"
        x-transition
        x-cloak
        class="absolute inset-0 z-10 flex items-center justify-center rounded-md bg-white/75"
    >
        <span class="h-12 w-12 animate-spin rounded-full border-4 border-x-primary_ui-50"></span>
    </div>
</div>