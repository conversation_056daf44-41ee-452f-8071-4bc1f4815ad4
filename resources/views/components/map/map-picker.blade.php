@props([
    'distanceKm' => 5,
    'mapClass' => 'h-[25.5rem]',
    'editable' => true,
])

<div
  x-data="{
    map: null,
    marker: null,
    isCompassLarge: false,
    editable: {{ isset($editable) && ($editable === 'false' || !$editable) ? 'false' : 'true' }},

    initMap() {
      this.$watch('latitude',  newLat => this.updateMapPosition({ lat:newLat, lng:this.longitude }));
      this.$watch('longitude', newLng => this.updateMapPosition({ lat:this.latitude, lng:newLng }));

      this.$nextTick(() => {
        loadGoogleMaps().then(async (google) => {
          this.map = new google.maps.Map(this.$refs.mapContainer, {
            center:    { lat:this.latitude, lng:this.longitude },
            mapTypeId: 'satellite',
            tilt:      0,
            disableDefaultUI: true,
            zoomControl:      true,
            zoomControlOptions: {
              position: google.maps.ControlPosition.LEFT_BOTTOM
            },
            mapTypeControl:         true,
            streetViewControl:      true,
            streetViewControlOptions: {
              position: google.maps.ControlPosition.RIGHT_BOTTOM
            },
            fullscreenControl: true,
            rotateControl:     false,
          });
  
          this.map.addListener('zoom_changed',    () => this.map.setTilt(0));
          this.map.addListener('maptypeid_changed', () => this.map.setTilt(0));
  
          this.marker = new google.maps.Marker({
            position: { lat:this.latitude, lng:this.longitude },
            map:      this.map,
            draggable: this.editable
          });
  
          this.marker.addListener('dragend', ev => {
            const newLat = ev.latLng.lat(),
                  newLng = ev.latLng.lng();
            this.latitude  = newLat;
            this.longitude = newLng;
            this.$dispatch('map-update', { lat:newLat, lng:newLng });
          });
  
          this.setZoomForDistance({{ $distanceKm }});
  
          if (this.editable) {
              this.map.addListener('click', (e) => {
                  const lat = e.latLng.lat();
                  const lng = e.latLng.lng();
  
                  this.marker.setPosition({ lat, lng });
  
                  this.$dispatch('map-update', { lat, lng });
              });
          }
        })

      });
    },

    toggleCompassOverlay() {
        this.isCompassLarge = !this.isCompassLarge;
    },

    setZoomForDistance(distance) {
        if (!this.map) return;

        const center = new google.maps.LatLng(this.latitude, this.longitude);
        const bounds = new google.maps.LatLngBounds();

        const radiusInMeters = (distance / 2) * 1000;

        const nePoint = google.maps.geometry.spherical.computeOffset(center, radiusInMeters, 45);
        const swPoint = google.maps.geometry.spherical.computeOffset(center, radiusInMeters, 225);

        bounds.extend(nePoint);
        bounds.extend(swPoint);

        this.map.fitBounds(bounds);
    },

    updateMapPosition({ lat, lng }) {
      if (!this.map || !this.editable) return;
      const pos = { lat, lng };
      this.map.panTo(pos);
      this.marker.setPosition(pos);
      this.setZoomForDistance({{ $distanceKm }});
    }
  }"
  x-init="initMap()"
  class="relative"
>
  <div x-ref="customCompassContainer" x-cloak class="absolute z-50 right-16 bottom-16">
    <img
      @click="toggleCompassOverlay()"
      :src="isCompassLarge
          ? '{{ asset('images/maps/arrows-minimize.png') }}'
          : (latitude > 0 ? '{{ asset('images/maps/petit_rose_vents_n.png') }}' : '{{ asset('images/maps/petit_rose_vents_s.png') }}')"
      alt="Compass"
      class="cursor-pointer h-auto"
      :style="isCompassLarge ? 'width: 50px' : 'width: 150px;'"
    />
  </div>

  <div
    x-show="isCompassLarge"
    x-transition
    x-cloak
    class="pointer-events-none absolute inset-0 z-50 flex items-center justify-center"
  >
    <img
      :src="latitude > 0
          ? '{{ asset('images/maps/grand_rose_vents_n.png') }}'
          : '{{ asset('images/maps/grand_rose_vents_s.png') }}'"
      alt="Large Compass Rose"
      class="h-auto w-3/4 opacity-75"
    />
  </div>

  <div x-ref="mapContainer" class="w-full {{ $mapClass }}"></div>

  <input type="hidden" name="latitude" :value="latitude" />
  <input type="hidden" name="longitude" :value="longitude" />
</div>
