const { PDFDocument, PageSizes, rgb, PDFName, PDFArray, PDFDict, PDFHexString, StandardFonts, PDFString, PDFNumber } = PDFLib;
let pdfFileName = "";
let isConnected = true;

let blurExeptPageOne = false;
function updateBlurredState(){
  if((typeof fromWhere !== "undefined" && fromWhere != 'pvgis_5_3') || (typeof isHomePage !== "undefined" && isHomePage )|| (typeof isPGVIS24 !== "undefined" && isPGVIS24)) {
    blurExeptPageOne = !isConnected
    if(isConnected && typeof hasActiveSubscription !== "undefined" && !hasActiveSubscription){
      const c = document.querySelector('input[name="mounting_position"]:checked')?.value
      if(c){
        // blurExeptPageOne = !['free_standing_new','roof_added_new','imposition_sec_1'].includes(c)
        blurExeptPageOne = ![].includes(c)
      }else if(simData?.dataInfoPhotovoltaics){
        blurExeptPageOne = +simData?.dataInfoPhotovoltaics?.numberOfSections >= 1;
      }
    }
  }
}

function showPdfEditorModal(editorId) {
  editorId = editorId.includes("#") ? editorId : "#" + editorId;
  const editorModal = document.querySelector(`${editorId}`);

  editorModal.style.opacity = 1;
  editorModal.style.visibility = "visible";
}

function closePdfEditorModal() {
    const confirmation = confirm("Are you sure to close the active tab!");
    console.log(confirmation);
    if (confirmation) {
        window.history.back()
    }
}

async function openTab(event, tabId) {
  // Remove 'active' class from all tab links and contents
  const tabLinks = document.querySelectorAll(".tab-link");
  const tabContents = document.querySelectorAll(".tab-content");

  tabLinks.forEach((link) => link.classList.remove("active"));
  tabContents.forEach((content) => content.classList.remove("active"));

  // Add 'active' class to the clicked tab link and corresponding content
  event.currentTarget.classList.add("active");

  const editor = document.getElementById(tabId);
  editor.classList.add("active");

  const pageContainer = editor.querySelector(".page-container");

  if (!pageContainer.querySelector(".zoomer")) {
    url2 = "http://localhost:8001/pdf/base.pdf";
    const editor2 = new PdfEditor("editor-2", url2);
    await editor2.loadPdf();
    showPdfEditorModal("editor-2");
  }
}

let currentFileName = "";
let isLoadingPdf = false;
window.simulationName =  "simulation";


let currentEditor = null;

// launch on generation finished
async function downloadSimulationPdfFile(fileName, clientSocketID) {
  //update to url for getting file
  const pdfUrl = `${BP_BASE_URL}/get/${fileName}?idSocket=${clientSocketID}`;
  let btnD = document.getElementById("downloadSimulationPdf");

  try {
    const response = await fetch(pdfUrl);

    if (!response.ok) {
      throw new Error("Failed to download PDF");
    }

      fileName = getPdfNameWithFileName(
          projectInfo?.projectName,
          simId,
          simulationItemName || simulationName || ""
        );

    pdfFileName = fileName.replace(/ /g, "_");

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    currentFileName = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
      return reader;
    });

    url1 = currentFileName;
    const editor1 = new PdfEditor("editor-1", url1);
    await editor1.loadPdf();
    currentEditor = editor1;

    showPdfEditorModal("editor-1");

    const btn_ = document.getElementById("pdf-viewer-tab1");

    isLoadingPdf = false;
    // btnD.querySelector(".fa-spinner").style.display = "none";
    // btnD.classList.remove("disabled");
    // btnD.removeAttribute("disabled");

    const modalBackdropPdf = document.querySelector("#modal-backdrop-pdf");

    modalBackdropPdf.style.opacity = 1;
    modalBackdropPdf.style.visibility = "visible";
    modalBackdropPdf.style.display = "block";
    document.body.style.overflow = "hidden";

  } catch (error) {
    isLoadingPdf = false;
    if (btnD) {
      btnD.querySelector(".fa-spinner").style.display = "none";
      btnD.classList.remove("disabled");
      btnD.removeAttribute("disabled");
    }

    console.error("Error downloading PDF:", error);
  }
}

async function downloadPdfFile(fileName, clientSocketID) {
  //update to url for getting file
  const pdfUrl = `${env.BP_BASE_URL}/get/${fileName}?idSocket=${clientSocketID}`;
  const simulationSelect = document.getElementById("simulation");
  const simulationName = simulationSelect.options[simulationSelect.selectedIndex].text;
  const simulationId = folder?.name ? simulationSelect.value : null;
  let btnD = document.getElementById("downloadPdf");

  try {
    const response = await fetch(pdfUrl);
    if (!response.ok) {
      throw new Error("Failed to download PDF");
    }

    fileName = getPdfNameWithFileName(folder?.name, simulationId, simulationName);
    pdfFileName = fileName.replace(/ /g, "_");
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    currentFileName = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
      return reader;
    });

    url1 = currentFileName;
    const editor1 = new PdfEditor("editor-1", url1);
    await editor1.loadPdf();
    currentEditor = editor1;

    showPdfEditorModal("editor-1");

    const btn_ = document.getElementById("pdf-viewer-tab1");

    isLoadingPdf = false;
    // btnD.querySelector(".fa-spinner").style.display = "none";
    // btnD.classList.remove("disabled");
    // btnD.removeAttribute("disabled");

    const modalBackdropPdf = document.querySelector("#modal-backdrop-pdf");

    modalBackdropPdf.style.opacity = 1;
    modalBackdropPdf.style.visibility = "visible";
    modalBackdropPdf.style.display = "block";
    document.body.style.overflow = "hidden";

    updateBlurredState()
    if(typeof homeDataPvgis24 != "undefined" && isConnected && !blurExeptPageOne){
      editor1.createAndDownloadPdf()
      localStorage.removeItem("home_data");
    }
  } catch (error) {
    isLoadingPdf = false;

    btnD.querySelector(".fa-spinner").style.display = "none";
    btnD.classList.remove("disabled");
    btnD.removeAttribute("disabled");

    console.error("Error downloading PDF:", error);
  }
}

async function downloadPdfFileBlurred(fileName, clientSocketID) {
  const pdfUrl = `${env.BP_BASE_URL}/get/${fileName}?idSocket=${clientSocketID}`;
  const simulationSelect = document.getElementById("simulation");
  const simulationName = simulationSelect.options[simulationSelect.selectedIndex].text;
  const simulationId = folder?.name ? simulationSelect.value : null;
  let btnD = document.getElementById("downloadPdf");

  try {
    const response = await fetch(pdfUrl);
    if (!response.ok) {
      throw new Error("Failed to download PDF");
    }

    fileName = getPdfNameWithFileName(folder?.name, simulationId, simulationName);
    pdfFileName = fileName.replace(/ /g, "_");
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    currentFileName = await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
      return reader;
    });

    url1 = currentFileName;
    const editor1 = new PdfEditor("editor-1", url1);
    await editor1.loadPdf();

    isLoadingPdf = false;

    updateBlurredState()
    await editor1.directCreateAndDownloadPdf()

    if(typeof homeDataPvgis24 != "undefined" && !blurExeptPageOne){
      localStorage.removeItem("home_data");
    }
  } catch (error) {
    isLoadingPdf = false;

    btnD.querySelector(".fa-spinner").style.display = "none";
    btnD.classList.remove("disabled");
    btnD.removeAttribute("disabled");

    console.error("Error downloading PDF:", error);
  }
}

function showModalPDFToLoad() {
  const modal = document.querySelector("#choose-pdf-to-load-modal");

  modal.style.opacity = "1";
  modal.style.zIndex = "3";
  document.body.style.overflow = "hidden";
  generateForm();
}

function closeModalPDFToLoad() {
  const modal = document.querySelector("#choose-pdf-to-load-modal");

  modal.style.opacity = "0";
  modal.style.zIndex = "-9";
  document.body.style.overflow = "";
  clearForm();
}

function submitFormData() {
  const modal = document.querySelector("#choose-pdf-to-load-modal");

  const formData = {};

  const batteryNames = [
    "Batteries 24 kWh",
    "Batteries 20 kWh",
    "Batteries 16 kWh",
  ];
  // Loop through the array of battery names
  batteryNames.forEach((batteryName) => {
    // Convert battery name to a suitable key (e.g., "Batteries 24 kWh" => "batterie24")
    const batteryKey = batteryName
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, "")
      .replace("batteries", "batterie");

    // Initialize the battery object with empty payment options
    formData[batteryKey] = {
      cash: false,
      leasing: false,
      loan: false,
      loan2: false,
    };
  });
  const checkboxes = modal.querySelectorAll('input[type="checkbox"]');

  // Update formData based on checked checkboxes
  checkboxes.forEach((checkbox) => {
    // Extract the battery key and payment method from the checkbox id
    const [batteryKey, paymentMethod] = checkbox.id.split("-");

    // Update the formData with checkbox value (checked or not)
    if (paymentMethod && formData[batteryKey]) {
      formData[batteryKey][paymentMethod] = checkbox.checked;
    }
  });
  return formData;
}
class PdfEditor {
  url = "";
  editorId = "";
  selectedPage = null;
  selectedPdfPage = null;
  focusedElementId = null;
  insertIndex = 1;
  isAddingImage = false;
  replaceIndex = null;
  isReplacingPage = false;
  insertPlace = "bottom";
  activeThumbnailIndex = 1;
  isScrollingPage = false;
  defaultWidth = 65;
  pdfPages = [];
  lastOrderPage = [];
  isRendering = false;
  blobPdfFinal;
  pageTitles = [];

  fileToDownload = null
  // element html
  pageContainer;
  thumbnailContainer;
  thumbnailWrapper;
  zoomInAction;
  zoomOutAction;
  zoomResetAction;
  downloadAction;
  pdfCanvasContainer;
  pdfThumbnailsContainer;
  // element html

  fileInput = null;
  fileInputChangeHandler = null;
  clickOutsideHandler = null;

  selectedPages = null; // Initially, all pages are selected
  deselectedPages = null;
  title = null;
  saveAction = null;
  editAction = null;
  sortableInstance = null;

  constructor(editorId, url) {
    this.editorId = editorId.includes("#") ? editorId : "#" + editorId;
    this.url = url;
    this.handleDownloadClick = this.handleDownloadClick.bind(this);
    // get element
    this.pageContainer = document.querySelector(
      `${this.editorId} .page-container`
    );
    this.thumbnailContainer = document.querySelector(
      `${this.editorId} .thumbnail-container`
    );
    this.thumbnailWrapper = document.querySelector(
      `${this.editorId} .thumbnail-wrapper`
    );
    this.zoomInAction = document.querySelector(`${this.editorId} #zoom-in`);
    this.zoomOutAction = document.querySelector(`${this.editorId} #zoom-out`);
    this.zoomResetAction = document.querySelector(
      `${this.editorId} #zoom-reset`
    );

    this.saveAction = document.querySelector(`${this.editorId} #close-icon`);
    this.editAction = document.querySelector(`${this.editorId} #edit-icon`);
    this.downloadAction = !(typeof inSimulator == "undefined" || !inSimulator) || (typeof fromWhere != "undefined" && fromWhere == 'pvgis_5_3') ? null : document.querySelector(`${this.editorId} #download`);
    this.title = `${pdfFileName
      .toLocaleUpperCase()
      .replace(/ /g, "_")}-${this.formatDate()}`;

    this.initializeEventListeners();

    document.getElementById("title").innerHTML = this.title;
    document.getElementById("name").value = this.title;
  }
  formatDate() {
    var d = new Date(),
        day = "" + d.getDate(),
        month = "" + (d.getMonth() + 1),
        year = d.getFullYear(),
        hours = "" + d.getHours(),
        minutes = "" + d.getMinutes();


    if (day.length < 2) day = "0" + day;
    if (month.length < 2) month = "0" + month;
    if (hours.length < 2) hours = "0" + hours;
    if (minutes.length < 2) minutes = "0" + minutes;

    return day + month + year + "-" + hours + "_" + minutes;
  }



  toggleEdit() {
    const title = document.getElementById("title");
    const input = document.getElementById("name");
    const editIcon = document.getElementById("edit-icon");
    const closeIcon = document.getElementById("close-icon");

    if (title.style.display === "none") {
      title.style.display = "block"; // Show title
      input.style.display = "none"; // Hide input
      editIcon.style.display = "block"; // Show edit icon
      closeIcon.style.display = "none"; // Hide close icon

      // Replace spaces with underscores
      this.title = document.getElementById("name").value.replace(/ /g, "_");
      title.innerHTML = this.title;
    } else {
      title.style.display = "none"; // Hide title
      input.style.display = "block"; // Show input
      closeIcon.style.display = "block"; // Show close icon
      editIcon.style.display = "none"; // Hide edit icon

      // Set input value as the current title (with spaces replaced back for editing, if needed)
      this.title = document.getElementById("name").value.replace(/_/g, " ");
      input.focus(); // Focus on the input
    }
  }

  initializeEventListeners() {
    const fileInput = document.querySelector(`.file-input`);
    const modal = document.querySelector(`.modal`);

    this.fileInputChangeHandler = this.handleFileInputChange.bind(this);
    this.clickOutsideHandler = this.handleClickOutside.bind(this);

    if (fileInput) {
      fileInput.addEventListener("change", this.fileInputChangeHandler);
    } else {
      console.warn("File input element not found");
    }

    if (modal) {
      modal.addEventListener("click", this.clickOutsideHandler);
    } else {
      console.warn("Modal element not found");
    }
  }

  createPageContainer(_pdfPages) {
    const zoomerDiv = document.createElement("div");
    zoomerDiv.classList.add("zoomer");

    _pdfPages.forEach((page) => {
      const pageDiv = document.createElement("div");
      pageDiv.classList.add("page");

      const canvas = document.createElement("canvas");
      canvas.id = `page_${page}`;
      canvas.setAttribute('data-page-number',page)
      canvas.classList.add("pdf-canvas");

      pageDiv.appendChild(canvas);

      zoomerDiv.appendChild(pageDiv);
    });
    this.pageContainer.appendChild(zoomerDiv);

    const fileInputContainer = createFileInputContainer();

    this.pageContainer.appendChild(fileInputContainer);
  }


  registerBtn = null;
  async handleDownloadClick() {
    const downloadBlurred = document.getElementById('logToPdfModal-download-button');
    const downloadBlurredSpinner = downloadBlurred?.querySelector('.spinnerCont');
    downloadBlurred.disabled = true;
    if (downloadBlurredSpinner) downloadBlurredSpinner.classList.remove('hidden');
    await this.createAndDownloadPdf(false);

    downloadBlurred.removeEventListener("click", this.handleDownloadClick);
    downloadBlurred.disabled = false;
    if (downloadBlurredSpinner) downloadBlurredSpinner.classList.add('hidden');
  }
  async createAndDownloadPdf(needConfirmation = true) {
    console.trace('Download')
    const downloadBlurred = document.getElementById('logToPdfModal-download-button');
    const downloadBlurredSpinner = downloadBlurred?.querySelector('.spinnerCont');

    updateBlurredState()

    if (blurExeptPageOne && downloadBlurred && needConfirmation && typeof showLogForPdfModal === "function") {
      let headerTitle = 'pvgis.need_log_to_get_full_pdf'
      const log = document.getElementById('logToPdfModal-log')
      const subscribe = document.getElementById('logToPdfModal-subscribe')
      const downloadBlurredContainer = document.getElementById('logToPdfModal-download-blurred');
      if(downloadBlurredContainer) downloadBlurredContainer.classList.remove('hidden');
      if (isConnected) {
        headerTitle = 'pvgis.need_subscribe_to_get_full_pdf'
        log.classList.add('hidden')
        subscribe.classList.remove('hidden')
      }else{
        log.classList.remove('hidden')
        subscribe.classList.add('hidden')
      }
      downloadBlurred.addEventListener('click',this.handleDownloadClick);
      showLogForPdfModal({},headerTitle);
      return;
    }

    const newPdfDoc = await PDFDocument.create();
    const existingPdfBytes = await this.blobPdfFinal.save();
    const existingPdfDoc = await PDFDocument.load(existingPdfBytes);
    const selectedPages = this.selectedPages;

    if (selectedPages) {
        const reorderedArray = Array.from(selectedPages).sort((a, b) => {
            return this.pdfPages.indexOf(a) - this.pdfPages.indexOf(b);
        });

        for (const pageNumber of reorderedArray) {
          const [copiedPage] = await newPdfDoc.copyPages(existingPdfDoc, [pageNumber - 1]);

          const aspectRatio = copiedPage.getWidth() / copiedPage.getHeight();
          let newWidth, newHeight;

          const [A4Width, A4Height] = PageSizes.A4;

          if (aspectRatio > 1) {
            // Landscape
            newWidth = A4Height;
            newHeight = A4Width;
          } else {
            // Portrait
            newWidth = A4Width;
            newHeight = A4Height;
          }

          copiedPage.setSize(newWidth, newHeight);
          const { width, height } = copiedPage.getSize();

          if (blurExeptPageOne && pageNumber > 1) {
            if (pageNumber == 2) {
              newPdfDoc.addPage(copiedPage);
              continue;
            }
            //Blured pages
            const blurredImage = await this.renderPdfPageAsBlurredImage(existingPdfBytes, pageNumber - 1, width, height);
            const newPage = newPdfDoc.addPage([width, height]);
            const image = await newPdfDoc.embedPng(blurredImage);

            newPage.drawImage(image, {
                x: 0,
                y: 0,
                width,
                height
            });
            if(this.registerBtn){
              const registerBtnPage = await newPdfDoc.embedPage(this.registerBtn);
              const xPos = (newPage.getWidth() / 2) - (this.registerBtn.getWidth() / 2)
              const yPos =  (newPage.getHeight() / 2) - (this.registerBtn.getHeight() / 2)
              const widthR = this.registerBtn.getWidth();
              const heightR = this.registerBtn.getHeight();
              newPage.drawPage(registerBtnPage, {
                  x: xPos,
                  y: yPos,
                  width: widthR,
                  height: heightR
              });

              // Define annotation properties
              const linkAnnotation = newPdfDoc.context.obj({
                Type: 'Annot',
                Subtype: 'Link',
                Rect: [xPos, yPos+this.registerBtn.getHeight(), widthR, yPos],
                Border: [0, 0, 0],
                C: [0, 0, 0],
                A: newPdfDoc.context.obj({
                  Type: 'Action',
                  S: 'URI',
                  URI: PDFString.of(`${env.UI_BASE_URL}/${getCurrentLanguage()=='en'?'':getCurrentLanguage() + '/'}${isConnected && !hasActiveSubscription ?'subscription':'register'}`),
                }),
              });

              // Attach annotation to the page
              const pageRef = newPdfDoc.context.register(linkAnnotation);
              const annotations = newPage.node.lookup(PDFName.of('Annots'), PDFArray) || newPdfDoc.context.obj([]);
              annotations.push(pageRef);
              newPage.node.set(PDFName.of('Annots'), annotations);
            }

          } else {
              newPdfDoc.addPage(copiedPage);
          }
        }

        const newPdfBytes = await newPdfDoc.save();
        const blob = new Blob([newPdfBytes], { type: "application/pdf" });
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = `${this.title}.pdf`;
        downloadLink.click();

        URL.revokeObjectURL(downloadLink.href);
    }

    if (typeof hideLogForPdfModal != "undefined") {
      hideLogForPdfModal()
    }
  }

  async directCreateAndDownloadPdf() {

    const newPdfDoc = await PDFDocument.create();
    const existingPdfBytes = await this.blobPdfFinal.save();
    const existingPdfDoc = await PDFDocument.load(existingPdfBytes);
    const selectedPages = this.selectedPages;

    if (selectedPages) {
        const reorderedArray = Array.from(selectedPages).sort((a, b) => {
            return this.pdfPages.indexOf(a) - this.pdfPages.indexOf(b);
        });

        for (const pageNumber of reorderedArray) {
          const [copiedPage] = await newPdfDoc.copyPages(existingPdfDoc, [pageNumber - 1]);

          const aspectRatio = copiedPage.getWidth() / copiedPage.getHeight();
          let newWidth, newHeight;

          const [A4Width, A4Height] = PageSizes.A4;

          if (aspectRatio > 1) {
            // Landscape
            newWidth = A4Height;
            newHeight = A4Width;
          } else {
            // Portrait
            newWidth = A4Width;
            newHeight = A4Height;
          }

          copiedPage.setSize(newWidth, newHeight);
          const { width, height } = copiedPage.getSize();

          if (blurExeptPageOne && pageNumber > 1) {
            if (pageNumber == 2) {
              newPdfDoc.addPage(copiedPage);
              continue;
            }
            //Blured pages
            const blurredImage = await this.renderPdfPageAsBlurredImage(existingPdfBytes, pageNumber - 1, width, height);
            const newPage = newPdfDoc.addPage([width, height]);
            const image = await newPdfDoc.embedPng(blurredImage);

            newPage.drawImage(image, {
                x: 0,
                y: 0,
                width,
                height
            });
            if(this.registerBtn){
              const registerBtnPage = await newPdfDoc.embedPage(this.registerBtn);
              const xPos = (newPage.getWidth() / 2) - (this.registerBtn.getWidth() / 2)
              const yPos =  (newPage.getHeight() / 2) - (this.registerBtn.getHeight() / 2)
              const widthR = this.registerBtn.getWidth();
              const heightR = this.registerBtn.getHeight();
              newPage.drawPage(registerBtnPage, {
                  x: xPos,
                  y: yPos,
                  width: widthR,
                  height: heightR
              });

              // Define annotation properties
              const linkAnnotation = newPdfDoc.context.obj({
                Type: 'Annot',
                Subtype: 'Link',
                Rect: [xPos, yPos+this.registerBtn.getHeight(), widthR, yPos],
                Border: [0, 0, 0],
                C: [0, 0, 0],
                A: newPdfDoc.context.obj({
                  Type: 'Action',
                  S: 'URI',
                  URI: PDFString.of(`${env.UI_BASE_URL}/${getCurrentLanguage()=='en'?'':getCurrentLanguage() + '/'}${isConnected && !hasActiveSubscription ?'subscription':'register'}`),
                }),
              });

              // Attach annotation to the page
              const pageRef = newPdfDoc.context.register(linkAnnotation);
              const annotations = newPage.node.lookup(PDFName.of('Annots'), PDFArray) || newPdfDoc.context.obj([]);
              annotations.push(pageRef);
              newPage.node.set(PDFName.of('Annots'), annotations);
            }

          } else {
              newPdfDoc.addPage(copiedPage);
          }
        }

        const newPdfBytes = await newPdfDoc.save();
        const blob = new Blob([newPdfBytes], { type: "application/pdf" });
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = `${this.title}.pdf`;
        downloadLink.click();

        URL.revokeObjectURL(downloadLink.href);
    }

  }

  // async renderPdfPageAsBlurredImage(pdfBytes, pageIndex, width, height) {
  //   const loadingTask = pdfjsLib.getDocument({ data: pdfBytes });
  //   const pdf = await loadingTask.promise;
  //   const page = await pdf.getPage(pageIndex + 1);

  //   const scale = 2;
  //   const viewport = page.getViewport({ scale });

  //   const canvas = document.createElement("canvas");
  //   const ctx = canvas.getContext("2d");
  //   canvas.width = viewport.width;
  //   canvas.height = viewport.height;

  //   const renderContext = { canvasContext: ctx, viewport };
  //   await page.render(renderContext).promise;

  //   // Apply blur effect directly on canvas
  //   const tempCanvas = document.createElement("canvas");
  //   const tempCtx = tempCanvas.getContext("2d");
  //   tempCanvas.width = canvas.width;
  //   tempCanvas.height = canvas.height;
  //   tempCtx.filter = "blur(10px)";
  //   tempCtx.drawImage(canvas, 0, 0);

  //   return tempCanvas.toDataURL("image/png");
  // }

  async renderPdfPageAsBlurredImage(pdfBytes, pageIndex, width, height) {
    const loadingTask = pdfjsLib.getDocument({ data: pdfBytes });
    const pdf = await loadingTask.promise;
    const page = await pdf.getPage(pageIndex + 1);

    const scale = 2;
    const viewport = page.getViewport({ scale });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    canvas.width = viewport.width;
    canvas.height = viewport.height;

    const renderContext = {
        canvasContext: ctx,
        viewport: viewport
    };
    await page.render(renderContext).promise;

    const blurRadius = 10;

    if (typeof StackBlur !== "undefined" && typeof StackBlur.canvasRGBA === "function") {
        // ✅ Use StackBlur if available
        StackBlur.canvasRGBA(canvas, 0, 0, canvas.width, canvas.height, blurRadius);
    } else {
        // ❗Fallback: use native filter if supported (may not work on Safari)
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");
        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        tempCtx.filter = `blur(${blurRadius}px)`;
        tempCtx.drawImage(canvas, 0, 0);
        return tempCanvas.toDataURL("image/png");
    }

    console.log('Renedered the bluredimgage', canvas.width, canvas.height, width, height,canvas.toDataURL("image/png"));

    return canvas.toDataURL("image/png");
  }


  async addLinkToPage(newPdfDoc, copiedPage, url) {
    // Get page dimensions
    const { width, height } = copiedPage.getSize();
    const linkX = 100;  // X coordinate
    const linkY = height - 150;  // Y coordinate (PDF origin is bottom-left)
    const linkWidth = 200;
    const linkHeight = 50;

    // Draw a visible rectangle around the clickable area
    copiedPage.drawRectangle({
        x: linkX,
        y: linkY,
        width: linkWidth,
        height: linkHeight,
        borderColor: rgb(0, 0, 1), // Blue border
        borderWidth: 2,
        opacity: 0.5, // Slightly transparent
    });

    // Embed text inside the clickable area
    const font = await newPdfDoc.embedFont(StandardFonts.HelveticaBold);
    copiedPage.drawText("Click Here", {
        x: linkX + 20,
        y: linkY + 15,
        size: 14,
        font,
        color: rgb(0, 0, 1), // Blue color
    });

    // Create the link annotation (the clickable part)
    const linkAnnotation = {
        Type: 'Annot',
        Subtype: 'Link',
        Rect: [linkX, linkY, linkX + linkWidth, linkY + linkHeight],
        Border: [0, 0, 0], // No border for the annotation itself
        A: {
            Type: 'Action',
            S: 'URI',
            URI: url,
        },
    };

    // Get existing annotations (or initialize to empty array)
    const annotations = copiedPage.node.get('Annots') || [];

    // Add the new link annotation
    annotations.push(linkAnnotation);

    // Set the annotations back on the page
    copiedPage.node.set('Annots', annotations);
}

  createThumbnailContainer(_pdfPages) {
    const pageLength = _pdfPages.length - 1;

    // Create a fixed container for the Select All checkbox
    const fixedContainer = document.createElement("div");
    fixedContainer.classList.add("fixed-select-all-container");

    // Add Select All checkbox
    const selectAllContainer = document.createElement("div");
    selectAllContainer.classList.add("select-all-container");

    const selectAllCheckbox = document.createElement("input");
    selectAllCheckbox.type = "checkbox";
    selectAllCheckbox.id = "select-all-checkbox";
    selectAllCheckbox.classList.add("select-all-checkbox");
    selectAllCheckbox.checked = true; // Default to checked
    selectAllCheckbox.addEventListener("change", (e) => {
      this.onSelectAllChange(e.target.checked);
    });

    const selectAllLabel = document.createElement("label");
    selectAllLabel.htmlFor = "select-all-checkbox";
    selectAllLabel.textContent = "Select All";

    selectAllContainer.appendChild(selectAllCheckbox);
    selectAllContainer.appendChild(selectAllLabel);

    fixedContainer.appendChild(selectAllContainer);

    // Add the fixed container to the thumbnail container
    this.thumbnailWrapper.prepend(fixedContainer);

    // Create a spacer to prevent content from being hidden behind the fixed container
    const spacer = document.createElement("div");
    spacer.classList.add("select-all-spacer");
    this.thumbnailWrapper.prepend(spacer);

    _pdfPages.forEach((page, i) => {
      const isLastPage = i === pageLength;
      const thumbnailDiv = document.createElement("div");
      thumbnailDiv.classList.add("thumbnail");
      thumbnailDiv.dataset.page = page;
      thumbnailDiv.dataset.originalPage = page;
      thumbnailDiv.classList.toggle(
        "active",
        this.activeThumbnailIndex === page
      );
      if (!(isMobileDevice() || window.innerWidth <= 768)) {
        thumbnailDiv.draggable = true;
      }
      thumbnailDiv.addEventListener("click", () => this.scrollPageTo(page));

      // Create checkbox for page selection
      const checkboxContainer = document.createElement("div");
      checkboxContainer.classList.add("checkbox-container");

      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.id = `page-checkbox-${page}`;
      checkbox.classList.add("page-checkbox");
      checkbox.checked = true; // Default to checked
      checkbox.addEventListener("change", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.onPageSelectionChange(
          parseInt(e.target.id.split("-")[2]),
          e.target.checked
        );
      });

      const checkboxLabel = document.createElement("label");
      checkboxLabel.htmlFor = `page-checkbox-${page}`;
      checkboxLabel.textContent = "Include";

      checkboxContainer.appendChild(checkbox);
      // checkboxContainer.appendChild(checkboxLabel);

      // thumbnailDiv.appendChild(checkboxContainer);

      const miniPageContainer = document.createElement("div");
      miniPageContainer.classList.add("mini-page-container");

      // Create miniPage div with a canvas for rendering the mini-page
      const miniPageDiv = document.createElement("div");
      miniPageDiv.classList.add("miniPage");
      const canvas = document.createElement("canvas");
      canvas.id = `miniPage_${page}`;
      canvas.classList.add("thumbnail-canvas");
      canvas.width = "120";
      canvas.width = "180";

      miniPageDiv.appendChild(canvas);
      miniPageContainer.appendChild(miniPageDiv);

      // Create the thumbnail-action div with delete and replace icons
      const thumbnailActionDiv = document.createElement("div");
      thumbnailActionDiv.classList.add("thumbnail-action");

      // // Delete Icon
      // const deleteIcon = document.createElement("span");
      // deleteIcon.title = "Delete this page.";
      // deleteIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>`; // Replace with actual icon
      // deleteIcon.style.color = "#eb2f96";
      // deleteIcon.addEventListener("click", (e) => {
      //   e.stopPropagation(); // Prevent triggering thumbnail click event
      //   this.deletePage(page);
      // });
      // thumbnailActionDiv.appendChild(deleteIcon);

      // Move up Icon
      const moveIconUp = document.createElement("span");
      moveIconUp.title = "deplacer vers le haut";
      moveIconUp.className = "zoom-button";
      moveIconUp.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-up"><path d="M8 6L12 2L16 6"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
      moveIconUp.style.color = "#eb2f96";
      moveIconUp.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.movePageUp(page);
      });
      thumbnailActionDiv.appendChild(moveIconUp);

      // move down Icon
      const moveIconDown = document.createElement("span");
      moveIconDown.title = "deplacer vers le bas";
      moveIconDown.className = "zoom-button";
      moveIconDown.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-down"><path d="M8 18L12 22L16 18"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
      moveIconDown.style.color = "#eb2f96";
      moveIconDown.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.movePageDown(page);
      });
      thumbnailActionDiv.appendChild(moveIconDown);

      //
      // Replace Icon
      const replaceIcon = document.createElement("span");
      replaceIcon.title = "Replace this page.";
      replaceIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right-left"><path d="m16 3 4 4-4 4"/><path d="M20 7H4"/><path d="m8 21-4-4 4-4"/><path d="M4 17h16"/></svg>`; // Replace with actual icon
      replaceIcon.style.color = "#3182c8";
      replaceIcon.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.onAddImage(page, page + 1, true);
      });

      thumbnailActionDiv.appendChild(replaceIcon);

      // miniPageContainer.appendChild(thumbnailActionDiv);

      miniPageContainer.prepend(checkboxContainer);
      // Append the action div to the thumbnail
      thumbnailDiv.appendChild(miniPageContainer);

      // Add the "add-page" div with a click event for adding pages
      thumbnailDiv.appendChild(this.createAddPageDiv(page));
      this.createAction(thumbnailDiv, page);

      // Append the thumbnail to the container
      this.thumbnailContainer.appendChild(thumbnailDiv);

      const titleDiv = document.createElement("div");
      titleDiv.classList.add("thumbnail-title");
      titleDiv.textContent = (
        this.pageTitles[i] || `Page ${page}`
      ).toUpperCase();
      thumbnailDiv.prepend(titleDiv);
    });

    const miniPages = document.querySelectorAll(".miniPage");
    if (!(isMobileDevice() || window.innerWidth <= 768)) {
      miniPages.forEach((miniPage) => {
        miniPage.addEventListener("mousedown", () => {
          miniPage.classList.add("dragging");
        });

        miniPage.addEventListener("mouseup", () => {
          miniPage.classList.remove("dragging");
        });

        miniPage.addEventListener("mouseleave", () => {
          miniPage.classList.remove("dragging");
        });
      });
    }

    // Initialize Sortable.js to enable sorting
    if (!(isMobileDevice() || window.innerWidth <= 768)) {
      this.sortableInstance = new Sortable(this.thumbnailContainer, {
        animation: 150,
        onEnd: async (evt) => {
          await this.reorder(evt);
        },
      });
    }
  }

  // Helper function to trigger sortable events and handle async callbacks
  async triggerSortableEventAndWait(sortableInstance, eventName, payload = {}) {
    // Create base event data that mimics SortableJS event structure
    const baseEventData = {
      to: sortableInstance.el,
      from: sortableInstance.el,
      item: null,
      clone: null,
      oldIndex: 0,
      newIndex: 1,
      oldDraggableIndex: 0,
      newDraggableIndex: 1,
      pullMode: undefined,
      ...payload,
    };

    // Create the event
    const event = {
      ...baseEventData,
      target: sortableInstance.el,
      preventDefault: () => {},
      stopPropagation: () => {},
      cancelBubble: false,
      willSort: true,
      draggedRect: null,
      relatedRect: null,
      type: eventName,
    };

    // Get the event handler
    const handler = sortableInstance.options[`on${eventName}`];

    // If there's a handler, call it and wait if it's async
    if (handler) {
      try {
        const result = await handler(event);
        return { event, result };
      } catch (error) {
        throw new Error(`Error in ${eventName} handler: ${error.message}`);
      }
    }

    return event;
  }

  createAddPageDiv(page) {
    const addPageDiv = document.createElement("div");
    addPageDiv.title = "Add a new page.";
    addPageDiv.classList.add("add-page");
    addPageDiv.innerHTML =
      '<svg class="icon-plus" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-plus"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>';
    addPageDiv.addEventListener("click", (e) => {
      if (!isConnected && typeof showLogForPdfModal === "function" && pvgisParamsForPdf) {
        showLogForPdfModal(pvgisParamsForPdf, "pvgis.need_log_to_use");
        return;
      }
      e.stopPropagation(); // Prevent triggering thumbnail click event
      console.log(`Add page clicked for page ${page}`);
      this.onAddImage(page, page + 1);
    });

    return addPageDiv;
  }

  async loadPdf(blobFile = null) {
    this.fileToDownload = null
    if (blobFile) {
      this.url = await blobFile.arrayBuffer();
      this.blobPdfFinal = await PDFDocument.load(this.url);
    } else {
      const arrayBuffer = await fetch(this.url).then((response) =>
        response.arrayBuffer()
      );
      this.blobPdfFinal = await PDFDocument.load(arrayBuffer);
    }

    this.registerBtn = await this.blobPdfFinal.getPage(0);
    // await this.blobPdfFinal.removePage(0)

    const loadingTask = pdfjsLib.getDocument(this.url);
    const pdf = await loadingTask.promise;

    this.pdfPages = Array.from({ length: pdf.numPages }, (_, i) => i + 1);
    if(blurExeptPageOne){
      this.pdfPages.shift();
    }
    this.selectedPages = new Set(this.pdfPages);
    this.deselectedPages = new Set([]);

    // Extract page titles
    this.pageTitles = await this.extractPageTitles(pdf);

    this.updateLastOrder();
    // create pdf and thumbnail canvas
    this.createPageContainer(this.pdfPages);
    this.createThumbnailContainer(this.pdfPages);
    this.pdfCanvasContainer = document.querySelectorAll(
      `${this.editorId} .pdf-canvas`
    );
    this.pdfThumbnailsContainer = document.querySelectorAll(
      `${this.editorId} .thumbnail-canvas`
    );

    this.zoomInAction.addEventListener("click", () => {
      this.zoomIn();
    });
    this.zoomOutAction.addEventListener("click", () => {
      this.zoomOut();
    });
    this.zoomResetAction.addEventListener("click", () => {
      this.zoomReset();
    });

    if(this.downloadAction){
      this.downloadAction.addEventListener("click", () => {
        this.createAndDownloadPdf();
      });
    }

    this.saveAction.addEventListener("click", () => {
      this.toggleEdit();
    });

    this.editAction.addEventListener("click", () => {
      this.toggleEdit();
    });

    // Initial rendering of pages
    this.pdfCanvasContainer.forEach(async (canvas, index) => {
      const page_number = +canvas.getAttribute('data-page-number')
      const page = await pdf.getPage(page_number);
      await this.renderPage(page, canvas, 2.3);
      const miniPage = this.pdfThumbnailsContainer[index];
      if (miniPage) {
        await this.renderPage(page, miniPage, 1);
      }
    });
  }

  cleanup() {
    // Clear references
    this.downloadAction = null;
    this.saveAction = null;
    this.editAction = null;
    this.zoomInAction = null;
    this.zoomOutAction = null;
    this.zoomResetAction = null;
    this.pdfPages = [];
    this.selectedPages = null;
    this.deselectedPages = null;
  }

  async extractPageTitles(pdf) {
    const titles = [];
    for (let i = 1; i <= pdf.numPages; i++) {
      if (blurExeptPageOne && i == 1) {
        continue;
      }
      const page = await pdf.getPage(i);
      const content = await page.getTextContent();
      const pageTitle = this.findPageTitle(content.items);
      titles.push(pageTitle || `Page ${i}`);
    }
    return titles;
  }

  findPageTitle(textItems) {
    // This is a simple implementation. You might need to adjust it based on your PDF structure.
    for (const item of textItems) {
      if (!Number(item.str) && item.str && item.str.trim().length > 0) {
        return item.str.trim();
      }
    }
    return null;
  }

  // Not use
  // async renderMainPage(_blob){
  //   const loadingTask = pdfjsLib.getDocument(_blob);
  //   const pdf = await loadingTask.promise;

  //   this.pdfCanvasContainer.forEach(async (canvas, index) => {
  //     const page = await pdf.getPage(index + 1);
  //     await this.renderPage(page, canvas, 2.3);
  //   });
  // }

  // Render PDF page onto a canvas
  async renderPage(page, canvas, scale) {
    const viewport = page.getViewport({ scale });
    const context = canvas.getContext("2d");

    canvas.height = viewport.height;
    canvas.width = viewport.width;
    canvas.className =
      viewport.height > viewport.width ? "portrait" : "landscape";

    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    // Render the PDF page onto the canvas
    await page.render(renderContext).promise;
  }

  updateLastOrder() {
    // save order
    this.lastOrderPage = [...this.pdfPages];
  }

  // Sample Handlers
  currentAddIndex = null;
  currentAddPosition = null;

  createFileInput() {
    console.log("Creating file input");
    if (this.fileInput) {
      this.fileInput.value = "";
      this.fileInput.removeEventListener("change", this.fileInputChangeHandler);
      this.fileInput.remove();
      console.log("File input already exists, returning existing one");
    }

    const uploadDiv = document.createElement("div");
    uploadDiv.className = "file-input-container";

    const label = document.createElement("label");
    label.setAttribute("for", "file-upload");
    label.className = "file-label";
    label.textContent = "Choose a file";

    this.fileInput = document.createElement("input");
    this.fileInput.setAttribute("type", "file");
    this.fileInput.id = "file-upload";
    this.fileInput.className = "file-input";
    this.fileInput.setAttribute("accept", "image/*");

    const fileNameSpan = document.createElement("span");
    fileNameSpan.id = "file-name";
    fileNameSpan.className = "file-name";
    fileNameSpan.textContent = "No file chosen";

    uploadDiv.appendChild(label);
    uploadDiv.appendChild(this.fileInput);
    uploadDiv.appendChild(fileNameSpan);

    this.pageContainer.appendChild(uploadDiv);
    console.log("File input created and added to page container");

    return this.fileInput;
  }

  onAddImage(index, position, replace = false) {

    if (!isConnected && typeof showLogForPdfModal === "function" && pvgisParamsForPdf) {
      showLogForPdfModal(pvgisParamsForPdf, "pvgis.need_log_to_use");
      return;
    }
    // Create a temporary file input
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";
    fileInput.style.display = "none";
    document.body.appendChild(fileInput);

    // Store current index and position
    this.currentAddIndex = index;
    this.currentAddPosition = position;

    // Handle file selection
    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file && file.type.startsWith("image/")) {
        console.log("Image file selected:", file.name);

        try {
          // Create a promise to handle the file reading
          const imageDataUrl = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsDataURL(file);
          });

          // Create and load the image
          const img = new Image();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imageDataUrl;
          });

          // Render the image
          await this.renderImageInMiniPage(
            img,
            this.currentAddIndex + 1,
            imageDataUrl
          );
          await this.renderImageInMainPage(img, this.currentAddIndex + 1);

          // update the pdf to add the image
          await this.addImageToPdf(img, this.currentAddIndex + 1);

          if (replace) {
            this.deletePage(this.currentAddIndex);
          }
        } catch (error) {
          console.error("Error processing image:", error);
          // Handle error - maybe show a toast or alert to user
        }
      } else {
        console.log("Invalid file type selected");
        // Optionally show error message to user
      }

      // Clean up - remove the temporary file input
      document.body.removeChild(fileInput);
    };

    // Handle cancel/close
    fileInput.oncancel = () => {
      document.body.removeChild(fileInput);
    };

    // Trigger the file chooser dialog
    fileInput.click();
  }

  // Update thumbnail after image upload
  updateThumbnail(page, imageDataUrl) {
    const canvas = document.querySelector(`#miniPage_${page}`);
    if (!canvas) {
      console.error(`Canvas not found for page ${page}`);
      return;
    }

    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Calculate aspect ratio to fit image properly
      const aspectRatio = img.width / img.height;
      let drawWidth, drawHeight;

      if (aspectRatio > canvas.width / canvas.height) {
        // Image is wider than canvas
        drawWidth = canvas.width;
        drawHeight = canvas.width / aspectRatio;
      } else {
        // Image is taller than canvas
        drawHeight = canvas.height;
        drawWidth = canvas.height * aspectRatio;
      }

      // Center the image
      const x = (canvas.width - drawWidth) / 2;
      const y = (canvas.height - drawHeight) / 2;

      // Draw the image
      ctx.drawImage(img, x, y, drawWidth, drawHeight);
    };

    img.src = imageDataUrl;
  }

  handleFileInputChange(event) {
    const file = event.target.files[0];
    const uploadContainer = document.querySelector(
      `${this.editorId} .file-input-container`
    );
    const zoomerDiv = document.querySelector(`${this.editorId} .zoomer`);
    const fileInput = document.querySelector(`${this.editorId} .file-input`);

    if (file && file.type.startsWith("image/")) {
      console.log("Image file selected:", file.name);
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = async () => {
          // Render the image in the mini page
          await this.renderImageInMiniPage(img, this.currentAddIndex);
          // Hide the upload container and show the zoomerDiv
          uploadContainer.classList.remove("file-input-container-visible");
          zoomerDiv.style.display = "block";
          // Reset file input and remove event listeners
          this.resetFileInput();
          uploadContainer.remove();
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    } else {
      console.log("Invalid image, input reset");
      fileInput.value = "";
    }
  }

  async addImageToPdf(img) {
    try {
      // Create a canvas to convert the image to PNG
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');

      // Draw the image onto the canvas
      ctx.drawImage(img, 0, 0);

      // Convert canvas to PNG blob
      const pngBlob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/png');
      });

      // Convert blob to array buffer
      const pngArrayBuffer = await pngBlob.arrayBuffer();

      // Now embed the PNG image into the PDF
      const embeddedImage = await this.blobPdfFinal.embedPng(pngArrayBuffer);

      // Calculate dimensions while maintaining aspect ratio
      const { width, height } = embeddedImage.scale(1);
      const page = this.blobPdfFinal.addPage([width, height]);


      const [A4Width, A4Height] = PageSizes.A4;


      page.drawImage(embeddedImage, {
        x: 0,
        y: 0,
        width: page.getHeight() > page.getWidth() ? A4Width : A4Height,
        height: page.getHeight() < page.getWidth() ? A4Width : A4Height,
      });

      const pageIndex = this.currentAddPosition - 1;
      this.blobPdfFinal.insertPage(pageIndex, page);

      // Update pdfPages array
      this.pdfPages.splice(pageIndex, 0, this.pdfPages.length + 1);
      this.pdfPages = this.pdfPages.map((_, index) => index + 1);

      // Update selectedPages to include the new page
      this.selectedPages.add(this.pdfPages[pageIndex]);

      console.log(`Image inserted into PDF at index ${pageIndex}`);
    } catch (error) {
      console.error("Error inserting image into PDF:", error);
      throw error; // Re-throw the error to handle it in the calling function
    }
  }

  // async addPdfToPdf(arrayBuffer) {
  //   try {
  //     const pdfDoc = await PDFDocument.load(arrayBuffer);
  //     const pageIndex = this.currentAddPosition - 1;

  //     for (let i = 0; i < pdfDoc.getPageCount(); i++) {
  //       const [copiedPage] = await this.blobPdfFinal.copyPages(pdfDoc, [i]);
  //       this.blobPdfFinal.insertPage(pageIndex + i, copiedPage);
  //     }

  //     // Update pdfPages array
  //     const newPages = Array(pdfDoc.getPageCount()).fill(0);
  //     this.pdfPages.splice(pageIndex, 0, ...newPages);
  //     this.pdfPages = this.pdfPages.map((_, index) => index + 1);

  //     this.selectedPages.add(this.pdfPages[pageIndex]); // Add the new page to selectedPages

  //     // Update thumbnails and main view
  //     await this.updateThumbnailsAndMainView();
  //     console.log(`PDF inserted into PDF at index ${pageIndex}`);
  //   } catch (error) {
  //     console.error("Error inserting PDF into PDF:", error);
  //   }
  // }

  // async updateThumbnailsAndMainView() {
  //   // Clear existing thumbnails and main view
  //   this.thumbnailContainer.innerHTML = "";
  //   this.pageContainer.querySelector(".zoomer").remove();

  //   // Recreate thumbnails and main view
  //   this.createThumbnailContainer(this.pdfPages);
  //   // this.createPageContainer(this.pdfPages);

  //   // Re-render all pages
  //   const pdf = await pdfjsLib.getDocument(await this.blobPdfFinal.save())
  //     .promise;
  //   this.pdfCanvasContainer = document.querySelectorAll(
  //     `${this.editorId} .pdf-canvas`
  //   );
  //   this.pdfThumbnailsContainer = document.querySelectorAll(
  //     `${this.editorId} .thumbnail-canvas`
  //   );

  //   this.pdfCanvasContainer.forEach(async (canvas, index) => {
  //     const page = await pdf.getPage(index + 1);
  //     await this.renderPage(page, canvas, 2.3);
  //     const miniPage = this.pdfThumbnailsContainer[index];
  //     if (miniPage) {
  //       await this.renderPage(page, miniPage, 1);
  //     }
  //   });
  // }

  updatePageTitles() {
    const thumbnails = document.querySelectorAll(`${this.editorId} .thumbnail`);
    thumbnails.forEach((thumbnail, index) => {
      const titleDiv = thumbnail.querySelector(".thumbnail-title");
      if (titleDiv) {
        titleDiv.textContent = (
          this.pageTitles[index] || `Page ${index + 1}`
        ).toUpperCase();
      }
    });
  }

  resetFileInput() {
    if (this.fileInput) {
      this.fileInput.value = "";
      this.fileInput.removeEventListener("change", this.fileInputChangeHandler);
    }
    document.removeEventListener("click", this.clickOutsideHandler);
  }

  handleClickOutside(e) {
    const uploadContainer = document.querySelector(
      `${this.editorId} .file-input-container`
    );
    const zoomerDiv = document.querySelector(`${this.editorId} .zoomer`);

    if (uploadContainer && !uploadContainer.contains(e.target)) {
      console.log("Clicked outside the component");

      // Reset the file input when hiding the container
      if (this.fileInput) {
        this.fileInput.value = ""; // Clears the file input value
        console.log("File input reset");
      }

      // Restore visibility of zoomerDiv
      if (zoomerDiv) {
        zoomerDiv.style.display = "block";
      }

      // Hide the upload container
      uploadContainer.classList.remove("file-input-container-visible");
      // uploadContainer.classList.remove("file-input-container-visible");

      // Remove event listeners
      // if (this.fileInputChangeHandler) {
      //   this.fileInput.removeEventListener("change", this.fileInputChangeHandler);
      // }
      if (this.clickOutsideHandler) {
        document.removeEventListener("click", this.clickOutsideHandler);
      }

      // uploadContainer.remove()
    }
  }

  async deletePage(pageNumber) {
    // Construct the canvas ID for the target page
    const canvasId = `page_${pageNumber}`;

    // Remove the page from pdfPages array
    const pageIndex = this.pdfPages.indexOf(pageNumber);
    if (pageIndex === -1) {
      console.error("Page non trouvée dans pdfPages");
      return;
    }
    this.pdfPages.splice(pageIndex, 1);

    // Find and remove the canvas by its ID
    const canvasToRemove = document.getElementById(canvasId);
    if (canvasToRemove) canvasToRemove.remove();

    // Remove the corresponding thumbnail if it exists
    const thumbnailToRemove = this.thumbnailContainer.querySelector(
      `#miniPage_${pageNumber}`
    );
    if (thumbnailToRemove) thumbnailToRemove.closest(".thumbnail").remove();

    // Update the selectedPages set and the order of pages
    this.selectedPages.delete(pageNumber);
    this.updateLastOrder();

    // Re-index the remaining thumbnails
    this.reindexThumbnails();
  }

  // Auxiliary function to re-index the thumbnails
  reindexThumbnails() {
    const thumbnails = this.thumbnailContainer.querySelectorAll(".thumbnail");
    thumbnails.forEach((thumbnail, index) => {
      const newPageNumber = this.pdfPages[index];
      const miniPageCanvas = thumbnail.querySelector(".thumbnail-canvas");
      if (miniPageCanvas) {
        miniPageCanvas.id = `miniPage_${newPageNumber}`;
      }
      thumbnail.dataset.page = newPageNumber; // Update data-page as well
    });
  }

  onReplace(index) {
    const fileInput = this.createFileInput();

    console.log(`Add image at index: ${index},  `);
    const uploadContainer = document.querySelector(
      `${this.editorId} .file-input-container`
    );
    const zoomerDiv = document.querySelector(`${this.editorId} .zoomer`);

    // Create file input if it doesn't exist

    this.initializeEventListeners();

    // Show the upload container
    uploadContainer.classList.add("file-input-container-visible");
    zoomerDiv.style.display = "none"; // Hide zoomerDiv when file input is shown

    // Remove existing event listeners
    if (this.fileInputChangeHandler) {
      fileInput.removeEventListener("change", this.fileInputChangeHandler);
    }
    if (this.clickOutsideHandler) {
      document.removeEventListener("click", this.clickOutsideHandler);
    }

    // Add new event listeners
    this.fileInputChangeHandler = this.handleFileInputChange.bind(this);
    this.clickOutsideHandler = this.handleClickOutside.bind(this);

    fileInput.addEventListener("change", this.fileInputChangeHandler);
    document.addEventListener("click", this.clickOutsideHandler);

    // Store current index and position
    this.currentAddIndex = index;
  }

  focusOnCanvas(pageDetails) {
    console.log(`Focus on page:`, pageDetails);
  }

  setActiveThumbnail(page) {
    const thumbnails = document.querySelectorAll(`${this.editorId} .thumbnail`);
    if (thumbnails.length > 0 && page) {
      this.activeThumbnailIndex = page;
      thumbnails.forEach((thumbnail) => {
        const canvas = thumbnail.querySelector("canvas");
        if (canvas && canvas.id === `miniPage_${page}`) {
          thumbnail.classList.add("active");
        } else {
          thumbnail.classList.remove("active");
        }
      });
    }
  }

  scrollPageTo(pageId) {
    const element = document.querySelector(`${this.editorId} #page_${pageId}`);

    if (element) {
      this.isScrollingPage = true;
      this.setActiveThumbnail(pageId);

      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "center",
      });

      setTimeout(() => {
        // Reset flag after scroll
        this.isScrollingPage = false;
      }, 2000);
    } else {
      console.log(`Element not found: #page_${pageId}`);
    }
  }

  zoomIn() {
    this.isScrollingPage = true;
    const element = document.querySelector(`${this.editorId} .zoomer`);
    let width = parseFloat(element.style.width.replace("%", ""));
    if (isNaN(width)) {
      width = this.defaultWidth;
    }

    width = width * 1.1 >= 100 ? 100 : width * 1.1;
    element.style.width = `${width}%`;

    // this.scrollPageTo(this.activeThumbnailIndex);
  }
  zoomOut() {
    this.isScrollingPage = true;
    const element = document.querySelector(`${this.editorId} .zoomer`);
    let width = parseFloat(element.style.width.replace("%", ""));
    if (isNaN(width)) {
      width = this.defaultWidth;
    }
    width = width * 0.9 <= 25 ? 25 : width * 0.9;
    element.style.width = `${width}%`;
    // this.scrollPageTo(this.activeThumbnailIndex);
  }
  zoomReset() {
    const element = document.querySelector(`${this.editorId} .zoomer`);
    element.style.width = `${this.defaultWidth}%`;
  }

  moveItemInArray(array = [], fromIndex, toIndex) {
    // Check if fromIndex and toIndex are within bounds of the array
    if (
      fromIndex < 0 ||
      fromIndex >= array.length ||
      toIndex < 0 ||
      toIndex >= array.length
    ) {
      throw new Error("Index out of bounds");
    }

    const item = array.splice(fromIndex, 1)[0];

    array.splice(toIndex, 0, item);
    return array;
  }

  // reorderArray(array, newOrder) {
  //   if (!newOrder) {
  //     return array; // If newOrder is not provided, return the original array
  //   }

  //   // Check if newOrder is a valid permutation of array indices
  //   if (newOrder.length !== array.length) {
  //     throw new Error(
  //       "Invalid newOrder length. It must match the length of the input array."
  //     );
  //   }

  //   const reorderedArray = [];

  //   for (const index of newOrder) {
  //     if (index < 1) {
  //       throw new Error("Index out of bounds.");
  //     }
  //     reorderedArray.push(array[index - 1]);
  //   }

  //   return reorderedArray;
  // }

  async reorderInPdf() {
    this.isRendering = true;
    const pages = this.pdfPages;

    const newPdfDoc = await PDFDocument.create();

    // Reorder pages based on the newPageOrder array
    for (const pageIndex of pages) {
      const [copiedPage] = await newPdfDoc.copyPages(this.blobPdfFinal, [
        this.lastOrderPage.indexOf(pageIndex),
      ]);
      newPdfDoc.addPage(copiedPage);
    }
    this.blobPdfFinal = newPdfDoc;
    this.updateLastOrder();
  }
  reorderMainPages(_newOrder) {
    try {
      const zoomer = document.querySelector(`${this.editorId} .zoomer`);
      if (!zoomer) {
        throw new Error("Zoomer element not found");
      }

      const pages = Array.from(zoomer.children);
      const pageMap = new Map();

      // Map each page's canvas ID to the page element
      pages.forEach((page) => {
        const canvas = page.querySelector("canvas");
        if (canvas) {
          const canvasId = parseInt(canvas.id.split("_")[1]);
          if (!isNaN(canvasId)) {
            pageMap.set(canvasId, page);
          }
        }
      });

      // Store references to all pages we want to keep
      const reorderedPages = _newOrder
        .map((id) => pageMap.get(id))
        .filter((page) => page != null);

      // Remove all pages from zoomer
      while (zoomer.firstChild) {
        zoomer.removeChild(zoomer.firstChild);
      }

      // Append pages in the new order
      reorderedPages.forEach((page) => {
        zoomer.appendChild(page); // Directly move the original elements
      });

      return true;
    } catch (error) {
      console.error("Error reordering main pages:", error);
      return false;
    }
  }

  createAction(thumbnail, currentIndex) {
    const page = this.pdfPages[currentIndex - 1];
    const checkBoxValue = this.selectedPages.has(page);
    const miniPageContainer = thumbnail.querySelector(".mini-page-container");
    const miniPage = thumbnail.querySelector(".miniPage");
    const action = thumbnail.querySelector(".thumbnail-action");
    if (action) {
      action.remove();
    }

    const checkBox = thumbnail.querySelector(".checkbox-container");
    if (checkBox) {
      checkBox.remove();
    }

    if (miniPage) {
      miniPage.remove();
    }

    const thumbnailActionDiv = document.createElement("div");
    thumbnailActionDiv.classList.add("thumbnail-action");

    const moveIconUp = document.createElement("span");
    moveIconUp.title = "deplacer vers le haut";
    moveIconUp.className = "zoom-button";
    moveIconUp.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-up"><path d="M8 6L12 2L16 6"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
    moveIconUp.style.color = "#eb2f96";
    moveIconUp.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent triggering thumbnail click event
      this.movePageUp(currentIndex);
    });
    thumbnailActionDiv.appendChild(moveIconUp);

    // move down Icon
    const moveIconDown = document.createElement("span");
    moveIconDown.title = "deplacer vers le bas";
    moveIconDown.className = "zoom-button";
    moveIconDown.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-down"><path d="M8 18L12 22L16 18"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
    moveIconDown.style.color = "#eb2f96";
    moveIconDown.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent triggering thumbnail click event
      this.movePageDown(currentIndex);
    });
    thumbnailActionDiv.appendChild(moveIconDown);

    // const text = document.createElement("h4");
    // text.innerHTML = currentIndex;
    // thumbnailActionDiv.appendChild(text);

    //
    // Replace Icon
    const replaceIcon = document.createElement("span");
    replaceIcon.title = "Replace this page.";
    replaceIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right-left"><path d="m16 3 4 4-4 4"/><path d="M20 7H4"/><path d="m8 21-4-4 4-4"/><path d="M4 17h16"/></svg>`; // Replace with actual icon
    replaceIcon.style.color = "#3182c8";
    replaceIcon.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent triggering thumbnail click event
      this.onAddImage(currentIndex, currentIndex + 1, true);
    });
    thumbnailActionDiv.appendChild(replaceIcon);

    const checkboxContainer = document.createElement("div");
    checkboxContainer.classList.add("checkbox-container");

    const checkbox = document.createElement("input");
    checkbox.type = "checkbox";
    checkbox.id = `page-checkbox-${currentIndex}`;
    checkbox.classList.add("page-checkbox");
    checkbox.checked = checkBoxValue; // Default to checked
    checkbox.addEventListener("change", (e) => {
      e.stopPropagation(); // Prevent triggering thumbnail click event
      this.onPageSelectionChange(currentIndex, e.target.checked);
    });

    const checkboxLabel = document.createElement("label");
    checkboxLabel.htmlFor = `page-checkbox-${currentIndex}`;
    checkboxLabel.textContent = "Include";

    checkboxContainer.appendChild(checkbox);
    // miniPage.remove();
    miniPageContainer.appendChild(checkboxContainer);
    miniPageContainer.appendChild(miniPage);
    miniPageContainer.appendChild(thumbnailActionDiv);
  }

  async movePageUp(pageId) {
    if (!isConnected && typeof showLogForPdfModal === "function" && pvgisParamsForPdf) {
    showLogForPdfModal(pvgisParamsForPdf, "pvgis.need_log_to_use");
    return;
  }
    const currentIndex = pageId - 1;
    this.reorderStepOne(currentIndex, "up");
  }

  movePageDown(pageId) {
    if (!isConnected && typeof showLogForPdfModal === "function" && pvgisParamsForPdf) {
      showLogForPdfModal(pvgisParamsForPdf, "pvgis.need_log_to_use");
      return;
    }
    const currentIndex = pageId - 1;
    this.reorderStepOne(currentIndex, "down");
  }

  async reorderStepOne(index, direction) {
    // Handle edge cases first
    const maxIndex = this.thumbnailContainer.querySelectorAll(".thumbnail").length - 1;

    if (direction === "up" && index === 0) {
      return; // Can't move first page up
    }
    if (direction === "down" && index === maxIndex) {
      return; // Can't move last page down
    }

    // Calculate new index based on direction
    const newIndex = direction === "up" ? index - 1 : index + 1;

    const thumbnails = this.thumbnailContainer.querySelectorAll(".thumbnail");
    const orderedThumbnails = Array.from(thumbnails);
    for (
      let curent_index = 0;
      curent_index < orderedThumbnails.length;
      curent_index++
    ) {
      if (curent_index === index) {
        const value = orderedThumbnails[newIndex];
        orderedThumbnails[newIndex] = orderedThumbnails[index];
        orderedThumbnails[index] = value;
      }
    }

    // Vider le conteneur des miniatures
    this.thumbnailContainer.innerHTML = "";

    orderedThumbnails.forEach((thumbnail, index) => {
      this.thumbnailContainer.appendChild(thumbnail);
    });
    this.updateLastOrder();
    const thumbnail = this.thumbnailContainer.children[newIndex];
    const oldThumbnail = this.thumbnailContainer.children[index];

    thumbnail.querySelector(`.add-page`).remove();
    oldThumbnail.querySelector(`.add-page`).remove();

    thumbnail.appendChild(this.createAddPageDiv(+newIndex + 1));
    oldThumbnail.appendChild(this.createAddPageDiv(index + 1));

    this.createAction(thumbnail, +newIndex + 1);
    this.createAction(oldThumbnail, index);

    this.moveItemInArray(this.pdfPages, index, newIndex);
    this.reorderMainPages(this.pdfPages);
    this.reorderThumbnailDataPage();

    // this.createAndDownloadPdf();

    this.setActiveThumbnail(newIndex + 1);
    this.scrollPageTo(newIndex + 1);
  }

  async reorder(event) {
    if (event.oldIndex != event.newIndex) {
      this.updateLastOrder();
      const thumbnail = event.to.children[event.newIndex];
      const oldThumbnail = event.to.children[event.oldIndex];
      const canvas = thumbnail.querySelector("canvas");
      const pageId = canvas?.id?.split("_")?.[1];

      this.createAction(thumbnail, +event.newIndex + 1);
      this.createAction(oldThumbnail, pageId);

      this.moveItemInArray(this.pdfPages, event.oldIndex, event.newIndex);
      this.reorderMainPages(this.pdfPages);
      this.reorderThumbnailDataPage();
      // this.createAndDownloadPdf();

      this.setActiveThumbnail(+event.newIndex + 1);
      this.scrollPageTo(+event.newIndex + 1);
    }
  }

  reorderThumbnailDataPage() {
    const elements = this.thumbnailContainer.querySelectorAll(".thumbnail");
    const lastPage = elements.length - 1;

    elements.forEach((element, index) => {
      const currentIndex = index + 1;
      element.setAttribute("data-page", currentIndex);
      const checkbox = element.querySelector(".page-checkbox");
      checkbox.id = `page-checkbox-${currentIndex}`;

      const canvas = element.querySelector("canvas");
      canvas.id = `miniPage_${currentIndex}`;

      this.createAction(element, currentIndex);
    });
  }

  static reorderNewThumbnail(evt, containerElement) {
    // Ensure we have valid indices
    const { oldIndex, newIndex } = evt;
    if (oldIndex === undefined || newIndex === undefined) {
      console.error("Invalid reorder indices");
      return;
    }

    // Get all thumbnail elements
    const thumbnails = Array.from(containerElement.children);

    // Validate indices
    if (
      oldIndex < 0 ||
      oldIndex >= thumbnails.length ||
      newIndex < 0 ||
      newIndex >= thumbnails.length
    ) {
      console.error("Indices out of bounds");
      return;
    }

    // Remove the element from its old position
    const [removedThumbnail] = thumbnails.splice(oldIndex, 1);

    // Insert the element at the new position
    thumbnails.splice(newIndex, 0, removedThumbnail);

    // Clear and repopulate the container
    containerElement.innerHTML = "";
    thumbnails.forEach((thumbnail) => containerElement.appendChild(thumbnail));

    // Update page numbers and data attributes
    this.updatePageNumbers(containerElement);
  }

  static updatePageNumbers(containerElement) {
    const thumbnails = containerElement.children;

    Array.from(thumbnails).forEach((thumbnail, index) => {
      // Update current page number
      thumbnail.setAttribute("data-page", index + 1);

      // Update checkbox ID
      const checkbox = thumbnail.querySelector(".page-checkbox");
      if (checkbox) {
        checkbox.id = `page-checkbox-${index + 1}`;
      }

      // Update canvas ID
      const canvas = thumbnail.querySelector("canvas");
      if (canvas) {
        canvas.id = `miniPage_${index + 1}`;
      }
    });
  }

  async renderImageInMiniPage(img, page) {
    const miniPageCanvas = document.querySelector(
      `${this.editorId} #miniPage_${page - 1}`
    );
    if (!miniPageCanvas) {
      console.error(`Canvas not found for index ${page - 1}`);
      return;
    }

    // Insert the image into the PDF document
    try {
      // Convert the image to bytes
      const response = await fetch(img.src);
      const imgBytes = await response.arrayBuffer();
      const mimeType = response.headers.get("content-type");

      const thumbnailDiv = document.createElement("div");
      thumbnailDiv.classList.add("thumbnail");
      thumbnailDiv.dataset.page = page;
      thumbnailDiv.dataset.originalPage = page;
      thumbnailDiv.classList.toggle(
        "active",
        this.activeThumbnailIndex === page
      );
      if (!(isMobileDevice() || window.innerWidth <= 768)) {
        thumbnailDiv.draggable = true;
      }
      thumbnailDiv.addEventListener("click", () => this.scrollPageTo(page));

      // Create checkbox for page selection
      const checkboxContainer = document.createElement("div");
      checkboxContainer.classList.add("checkbox-container");

      const checkbox = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.id = `page-checkbox-${page}`;
      checkbox.classList.add("page-checkbox");
      checkbox.checked = true; // Default to checked
      checkbox.addEventListener("change", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.onPageSelectionChange(
          parseInt(e.target.id.split("-")[2]),
          e.target.checked
        );
      });

      const checkboxLabel = document.createElement("label");
      checkboxLabel.htmlFor = `page-checkbox-${page}`;
      checkboxLabel.textContent = "Include";

      checkboxContainer.appendChild(checkbox);
      // checkboxContainer.appendChild(checkboxLabel);

      // thumbnailDiv.appendChild(checkboxContainer);

      const miniPageContainer = document.createElement("div");
      miniPageContainer.classList.add("mini-page-container");

      // Create miniPage div with a canvas for rendering the mini-page
      const miniPageDiv = document.createElement("div");
      miniPageDiv.classList.add("miniPage");
      const canvas = document.createElement("canvas");
      canvas.id = `miniPage_${page}`;
      canvas.classList.add("thumbnail-canvas");

      // Append the canvas to the miniPageDiv or to the document as needed
      miniPageDiv.appendChild(canvas);
      document.body.appendChild(miniPageDiv); // Add miniPageDiv to the document for it to appear on the page

      const [A4Width, A4Height] = PageSizes.A4;

      const ctx = canvas.getContext("2d");
      const aspectRatio = img.width / img.height;
      let newWidth, newHeight;

      if (aspectRatio > 1) {
        // Landscape
        newWidth = A4Height;
        newHeight = A4Width;
      } else {
        // Portrait
        newWidth = A4Width;
        newHeight = A4Height;
      }

      canvas.width = newWidth;
      canvas.height = newHeight;

      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the image centered on the canvas
      const x = (canvas.width - newWidth) / 2;
      const y = (canvas.height - newHeight) / 2;
      ctx.drawImage(img, x, y, newWidth, newHeight);

      console.log(`Image rendered on miniPage_${page}`);

      miniPageDiv.appendChild(canvas);
      miniPageContainer.appendChild(miniPageDiv);

      // Create the thumbnail-action div with delete and replace icons
      const thumbnailActionDiv = document.createElement("div");
      thumbnailActionDiv.classList.add("thumbnail-action");

      // // Delete Icon
      // const deleteIcon = document.createElement("span");
      // deleteIcon.title = "Delete this page.";
      // deleteIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>`; // Replace with actual icon
      // deleteIcon.style.color = "#eb2f96";
      // deleteIcon.addEventListener("click", (e) => {
      //   e.stopPropagation(); // Prevent triggering thumbnail click event
      //   this.deletePage(page);
      // });
      // thumbnailActionDiv.appendChild(deleteIcon);

      // Move up Icon
      const moveIconUp = document.createElement("span");
      moveIconUp.title = "deplacer vers le haut";
      moveIconUp.className = "zoom-button";
      moveIconUp.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-up"><path d="M8 6L12 2L16 6"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
      moveIconUp.style.color = "#eb2f96";
      moveIconUp.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.movePageUp(page);
      });
      thumbnailActionDiv.appendChild(moveIconUp);

      // move down Icon
      const moveIconDown = document.createElement("span");
      moveIconDown.title = "deplacer vers le bas";
      moveIconDown.className = "zoom-button";
      moveIconDown.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-move-down"><path d="M8 18L12 22L16 18"/><path d="M12 2V22"/></svg>`; // Replace with actual icon
      moveIconDown.style.color = "#eb2f96";
      moveIconDown.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.movePageDown(page);
      });
      thumbnailActionDiv.appendChild(moveIconDown);

      //
      // Replace Icon
      const replaceIcon = document.createElement("span");
      replaceIcon.title = "Replace this page.";
      replaceIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right-left"><path d="m16 3 4 4-4 4"/><path d="M20 7H4"/><path d="m8 21-4-4 4-4"/><path d="M4 17h16"/></svg>`; // Replace with actual icon
      replaceIcon.style.color = "#3182c8";
      replaceIcon.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent triggering thumbnail click event
        this.onAddImage(page, page + 1, true);
      });
      thumbnailActionDiv.appendChild(replaceIcon);

      // miniPageContainer.appendChild(thumbnailActionDiv);

      miniPageContainer.prepend(checkboxContainer);

      // Append the action div to the thumbnail
      thumbnailDiv.appendChild(miniPageContainer);

      // Add the "add-page" div with a click event for adding pages
      thumbnailDiv.appendChild(this.createAddPageDiv(page));

      this.createAction(thumbnailDiv, page);

      const titleDiv = document.createElement("div");
      titleDiv.classList.add("thumbnail-title");
      titleDiv.textContent = `Page ${page}`.toUpperCase();
      thumbnailDiv.prepend(titleDiv);

      // Get the child at the target index
      const referenceNode = this.thumbnailContainer.children[page - 1];

      // If the index is valid, insert the new element before the reference node
      if (referenceNode) {
        this.thumbnailContainer.insertBefore(thumbnailDiv, referenceNode);
      } else {
        // If the index is out of bounds, append the element at the end
        this.thumbnailContainer.appendChild(thumbnailDiv);
      }

      // Now, to select all thumbnailDiv elements after the inserted one
      let nextThumbnailDivs = [];
      let currentElement = thumbnailDiv.nextElementSibling;

      console.log(this.pdfPages);
      this.pdfPages.splice(page - 1, 0, page);
      for (let i = page; i < this.pdfPages.length; i++) {
        this.pdfPages[i] += 1;
      }

      console.log("page", page);
      const newArray = Array.from(this.deselectedPages);

      for (let i = 0; i < newArray.length; i++) {
        if (newArray[i] >= page) {
          // Check if the newArray value is greater than myValue
          newArray[i] += 1; // Increment the value by 1
        }
      }

      this.deselectedPages = new Set(newArray);

      while (currentElement) {
        const currentIndex = +currentElement.getAttribute("data-page");
        const updatedCurrentIndex =
          +currentElement.getAttribute("data-page") + 1;
        console.log("currentIndex", updatedCurrentIndex);

        currentElement.dataset.page = updatedCurrentIndex;
        currentElement.dataset.originalPage = updatedCurrentIndex;

        const checkbox = currentElement.querySelector(
          `#page-checkbox-${currentIndex}`
        );
        if (checkbox) {
          console.log("checkbox", checkbox, currentIndex);
          checkbox.id = `page-checkbox-${updatedCurrentIndex}`;
          checkbox.checked = this.selectedPages.has(currentIndex);
          this.selectedPages.add(updatedCurrentIndex);
        }

        const canvas = currentElement.querySelector(
          `#miniPage_${currentIndex}`
        );
        if (canvas) {
          canvas.id = `miniPage_${updatedCurrentIndex}`;
        }

        this.createAction(currentElement, updatedCurrentIndex);

        const addPage = currentElement.querySelector(`.add-page`);
        addPage.remove();

        currentElement.appendChild(this.createAddPageDiv(updatedCurrentIndex));

        currentElement = currentElement.nextElementSibling;
      }

      // Embed the image in the PDF based on its MIME type
      // let embeddedImage;
      // if (mimeType === "image/png") {
      //   embeddedImage = await this.blobPdfFinal.embedPng(imgBytes);
      // } else if (mimeType === "image/jpeg" || mimeType === "image/jpg") {
      //   embeddedImage = await this.blobPdfFinal.embedJpg(imgBytes);
      // } else {
      //   throw new Error(`Unsupported image type: ${mimeType}`);
      // }

      // // Create a new page with the correct aspect ratio
      // const { width, height } = embeddedImage.scale(1);
      // const page = this.blobPdfFinal.addPage([newWidth, newHeight]);

      // // Draw the image on the new page
      // page.drawImage(embeddedImage, {
      //   x: 0,
      //   y: 0,
      //   width: page.getHeight() > page.getWidth() ? A4Width : A4Height,
      //   height: page.getHeight() < page.getWidth() ? A4Width : A4Height,
      // });

      // // Insert the new page at the correct position
      // const pageIndex = this.currentAddPosition
      //   ? this.currentAddPosition
      //   : this.pdfPages.length;

      // this.blobPdfFinal.insertPage(pageIndex, page);

      // // Update the pdfPages array
      // this.pdfPages.splice(this.currentAddIndex, 0, this.pdfPages.length + 1);

      // // Update thumbnails and main view
      // // await this.updateThumbnailsAndMainView();
      // // await this.updatePdfView(pageIndex);

      // console.log(`Image inserted into PDF at index ${pageIndex}`);
    } catch (error) {
      console.error("Error inserting image into PDF:", error);
    }
  }

  async renderImageInMainPage(img, page) {
    const zoomerDiv = document.querySelector(`${this.editorId} .zoomer`);

    const pageDiv = document.createElement("div");
    pageDiv.classList.add("page");

    const canvas = document.createElement("canvas");
    canvas.id = `page_${page}`;
    canvas.classList.add("pdf-canvas");

    const [A4Width, A4Height] = PageSizes.A4;

    const ctx = canvas.getContext("2d");
    const aspectRatio = img.width / img.height;
    let newWidth, newHeight;

    if (aspectRatio > 1) {
      // Landscape
      newWidth = A4Height;
      newHeight = A4Width;
    } else {
      // Portrait
      newWidth = A4Width;
      newHeight = A4Height;
    }

    canvas.width = newWidth;
    canvas.height = newHeight;

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw the image centered on the canvas
    const x = (canvas.width - newWidth) / 2;
    const y = (canvas.height - newHeight) / 2;
    ctx.drawImage(img, x, y, newWidth, newHeight);

    pageDiv.appendChild(canvas);

    zoomerDiv.appendChild(pageDiv);

    const referenceNode = zoomerDiv.children[page - 1];

    if (referenceNode) {
      zoomerDiv.insertBefore(pageDiv, referenceNode);
    } else {
      zoomerDiv.appendChild(pageDiv);
    }

    let currentElement = pageDiv.nextElementSibling;

    console.log("currentElement", currentElement);

    while (currentElement) {
      const canvas = currentElement.querySelector("canvas");
      const updatedCurrentIndex = +canvas.getAttribute("id").split("_")[1] + 1;
      canvas.id = `page_${updatedCurrentIndex}`;

      currentElement = currentElement.nextElementSibling;
    }
  }

  async updatePdfView(insertedPageIndex) {
    // Get the PDF data URL
    const pdfBytes = await this.blobPdfFinal.save();
    const pdfDataUrl = URL.createObjectURL(
      new Blob([pdfBytes], { type: "application/pdf" })
    );

    // Update the main viewer if it exists
    const mainViewer = document.querySelector(`${this.editorId} #pdfViewer`);
    if (mainViewer) {
      // If using PDF.js viewer, you can use their API to load the new page
      await mainViewer.loadDocument(pdfDataUrl);
      await mainViewer.scrollToPage(insertedPageIndex + 1);
    }

    // Update only the affected thumbnail
    const thumbnailContainer = document.querySelector(
      `${this.editorId} #thumbnailContainer`
    );
    if (thumbnailContainer) {
      const newThumbnail = document.createElement("canvas");
      newThumbnail.id = `miniPage_${insertedPageIndex}`;
      // Add your thumbnail styling classes here
      newThumbnail.classList.add("pdf-thumbnail");

      // Insert the new thumbnail at the correct position
      const nextThumbnail = thumbnailContainer.children[insertedPageIndex];
      if (nextThumbnail) {
        thumbnailContainer.insertBefore(newThumbnail, nextThumbnail);
      } else {
        thumbnailContainer.appendChild(newThumbnail);
      }

      // Render the new thumbnail
      const page = await this.blobPdfFinal.getPage(insertedPageIndex + 1);
      const scale = newThumbnail.width / page.getWidth();
      await page.render({
        canvas: newThumbnail,
        scale: scale,
      });
    }

    // this.downloadPdf("dslfjlksdjfl.pdf");

    // Clean up the data URL
    URL.revokeObjectURL(pdfDataUrl);
  }

  // async updateThumbnailsAndMainView() {
  //   // Clear existing thumbnails and main view
  //   this.thumbnailContainer.innerHTML = "";
  //   this.pageContainer.querySelector(".zoomer").remove();

  //   // Recreate thumbnails and main view
  //   this.createThumbnailContainer(this.pdfPages);
  //   this.createPageContainer(this.pdfPages);

  //   // Re-render all pages
  //   const pdf = await pdfjsLib.getDocument(await this.blobPdfFinal.save())
  //     .promise;
  //   this.pdfCanvasContainer = document.querySelectorAll(
  //     `${this.editorId} .pdf-canvas`
  //   );
  //   this.pdfThumbnailsContainer = document.querySelectorAll(
  //     `${this.editorId} .thumbnail-canvas`
  //   );

  //   this.pdfCanvasContainer.forEach(async (canvas, index) => {
  //     const page = await pdf.getPage(index + 1);
  //     await this.renderPage(page, canvas, 2.3);
  //     const miniPage = this.pdfThumbnailsContainer[index];
  //     if (miniPage) {
  //       await this.renderPage(page, miniPage, 1);
  //     }
  //   });

  //   // Update page titles
  //   this.pageTitles = await this.extractPageTitles(pdf);
  //   this.updatePageTitles();
  // }

  onPageSelectionChange(index, isSelected) {
    const page = this.pdfPages[index - 1];
    console.log(`Page ${page} selection changed to ${isSelected}`, index);
    // You can implement the logic to include/exclude pages from the final PDF here
    // For example, you could maintain an array of selected pages
    if (isSelected) {
      this.selectedPages.add(page);
      this.deselectedPages.delete(page);
    } else {
      this.selectedPages.delete(page);
      this.deselectedPages.add(page);
    }
    // You might want to update the UI or trigger a re-render of the PDF preview
    this.updatePdfPreview();
  }

  async updatePdfPreview() {
    // Get all page divs in the main view
    const pageDivs = document.querySelectorAll(
      `${this.editorId} .zoomer .page`
    );

    // Update visibility of pages based on selection
    pageDivs.forEach((pageDiv, index) => {
      const pageNumber = this.pdfPages[index];
      if (this.selectedPages.has(pageNumber)) {
        pageDiv.style.display = "block";
      } else {
        pageDiv.style.display = "none";
      }
    });

    // Update thumbnails to reflect selection state
    this.updateThumbnailSelection();

    // Optionally, scroll to the first visible page
    this.scrollToFirstVisiblePage();
  }

  updateThumbnailSelection() {
    const thumbnails = document.querySelectorAll(`${this.editorId} .thumbnail`);
    let allSelected = true;
    thumbnails.forEach((thumbnail, index) => {
      const pageNumber = this.pdfPages[index];
      const checkbox = thumbnail.querySelector(".page-checkbox");

      if (this.selectedPages.has(pageNumber)) {
        thumbnail.classList.remove("deselected");
        checkbox.checked = true;
      } else {
        thumbnail.classList.add("deselected");
        checkbox.checked = false;
        allSelected = false;
      }
    });

    // Update Select All checkbox
    const selectAllCheckbox = document.querySelector(
      `${this.editorId} #select-all-checkbox`
    );
    if (selectAllCheckbox) {
      selectAllCheckbox.checked = allSelected;
    }
  }

  scrollToFirstVisiblePage() {
    const firstVisiblePage = document.querySelector(
      `${this.editorId} .zoomer .page[style*="display: block"]`
    );
    if (firstVisiblePage) {
      firstVisiblePage.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }

  // Helper method to convert bytes to data URL
  async bytesToDataUrl(bytes) {
    const blob = new Blob([bytes], { type: "application/pdf" });
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  onSelectAllChange(isSelected) {
    console.log(`Select All changed to ${isSelected}`);
    const checkboxes = document.querySelectorAll(
      `${this.editorId} .page-checkbox`
    );
    checkboxes.forEach((checkbox) => {
      if (isSelected || parseInt(checkbox.id.split("-")[2]) !== 1) {
        checkbox.checked = isSelected;
        const page = parseInt(checkbox.id.split("-")[2]);
        this.onPageSelectionChange(page, isSelected);
      }
    });
  }

  /**
   * Download PDF with custom filename
   * @param {string} filename - Name for the downloaded file
   */
  async downloadPdf(filename = "document.pdf") {
    try {
      // Ensure filename has .pdf extension
      const sanitizedFilename = this.sanitizeFilename(filename);

      // Get the PDF bytes
      const pdfBytes = await this.blobPdfFinal.save();

      // Create blob from bytes
      const blob = new Blob([pdfBytes], { type: "application/pdf" });

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = sanitizedFilename;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log("PDF downloaded successfully");
      return true;
    } catch (error) {
      console.error("Error downloading PDF:", error);
      throw new Error("Failed to download PDF");
    }
  }

  /**
   * Sanitize filename and ensure .pdf extension
   * @param {string} filename - Original filename
   * @returns {string} Sanitized filename
   */
  sanitizeFilename(filename) {
    // Remove invalid characters
    let sanitized = filename.replace(/[<>:"/\\|?*\x00-\x1F]/g, "-");

    // Ensure .pdf extension
    if (!sanitized.toLowerCase().endsWith(".pdf")) {
      sanitized += ".pdf";
    }

    return sanitized;
  }

  /**
   * Get the PDF as a data URL
   * @returns {Promise<string>} Data URL of the PDF
   */
  async getPdfDataUrl() {
    try {
      const pdfBytes = await this.blobPdfFinal.save();
      const blob = new Blob([pdfBytes], { type: "application/pdf" });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error("Error creating PDF data URL:", error);
      throw new Error("Failed to create PDF data URL");
    }
  }
}

function createFileInputContainer() {
  const uploadDiv = document.createElement("div");
  uploadDiv.className = "file-input-container";

  // Create label
  const label = document.createElement("label");
  label.setAttribute("for", "file-upload");
  label.className = "file-label";
  label.textContent = "Choose a file";

  // Create input (file)
  const fileInput = document.createElement("input");
  fileInput.setAttribute("type", "file");
  fileInput.id = "file-upload";
  fileInput.className = "file-input";
  fileInput.setAttribute("accept", "image/*");

  // Create span for displaying file name
  const fileNameSpan = document.createElement("span");
  fileNameSpan.id = "file-name";
  fileNameSpan.className = "file-name";
  fileNameSpan.textContent = "No file chosen";

  // Append label, input, and span to container div
  uploadDiv.appendChild(label);
  uploadDiv.appendChild(fileInput);
  uploadDiv.appendChild(fileNameSpan);

  return uploadDiv;
}

// Usage example:
// const fileInputContainer = createFileInputContainer();
// someParentElement.appendChild(fileInputContainer);
function pdfEditorContentLoad(){
  updateBlurredState();
  const editModeBtn = document.querySelector("#edit-mode");
  if(editModeBtn){
    editModeBtn.addEventListener("click", function() {
      const toShow = document.querySelector(".thumbnail-wrapper");
      if(toShow){
        if(toShow.classList.contains("o-mode")){
          toShow.classList.remove("o-mode");
        }else{
          toShow.classList.add("o-mode");
        }
      }
    });
  }
}
document.addEventListener("DOMContentLoaded", function() {
  pdfEditorContentLoad();
});
function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

window.PdfEditor = PdfEditor;
