<?php

namespace App\View\Components\Filament\Plans;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;

class All extends \App\View\Components\Plans\All
{
    /**
     * Get the view / contents that represent the component.
     */

    public $preselectedType = 'Professionnel';
    public $preselectedIntervalHere = 'month';

    public function render(): View|\Closure|string
    {
        return view('components.filament.plans.all', $this->calculateViewData());
    }

    protected function calculateViewData()
    {
        $checkoutUrl = config('services.base_url.app');
        $subscription = null;
        if ($this->currentSubscriptionUuid !== null) {
            $subscription = $this->subscriptionService->findActiveByUserAndSubscriptionUuid(auth()->user()->id, $this->currentSubscriptionUuid);
        }

        $planType = null;
        if ($subscription !== null) {
            $planType = $subscription->plan->type;
        }

        $plans = $this->planService->getAllPlansWithPrices(
            $this->products,
            $planType,
        );

        Log::debug($plans);

        $viewData['subscription'] = $subscription;
        // $viewData['professionalPlans'] = $plans->filter(fn($plan) => $plan->product->account_type === 'Professionnel');
        // $viewData['checkoutUrl'] = $plans->filter(fn($plan) => $plan->product->account_type === 'Particulier');


        $productsProfessional = [];
        $productsParticular = [];
        foreach ($plans as $plan) {
            $product = $plan->product;
            $features = $product->features->map(function ($feature) {
                return [
                    'label' => $feature->name,
                    'key' => $feature->key,
                    'enabled' => $feature->pivot->enabled ?? false,
                    'ui_order' => $feature->ui_order
                ];
            })->toArray();
            $priceValue = 0;
            foreach ($plan->prices as $price) {
                if ($price->currency_id == 30 && $price->getOriginal('price')) {
                    $priceValue = $price->getOriginal('price') / 100;
                    break;
                }
            }
            $productData = [
                'name' => $plan->name,
                'price' => $priceValue,
                'slug' => $plan->slug,
                'description' => $plan->description ?? $product->description,
                'dossier' => $plan->user_count ?? 1,
                'type' => $plan->interval?->name === "month" ? "Monthly" : "Yearly",
                'account_type' => $product->account_type,
                'user_count' => $plan->user_count,
                'recommande' => $product->is_popular == 1,
                'features' => $features,
            ];
            if ($product->account_type === "Professionnel") {
                $productsProfessional[] = $productData;
            } elseif ($product->account_type === "Particulier") {
                $productsParticular[] = $productData;
            }
        }

        if($subscription){
            $checkoutUrl = $checkoutUrl.'/checkout/change/';
        }else{
            $checkoutUrl = $checkoutUrl.'/checkout/plan/';   
        }

        $viewData['professionalPlans'] = $productsProfessional;
        $viewData['particularPlans'] = $productsParticular;
        $viewData['checkoutUrl'] = $checkoutUrl;

        return $this->enrichViewData($viewData, $plans);
    }
}
