<?php

namespace App\Constants;

enum AccountTypeEnum: string
{
    case PARTICULAR = '1';
    case PROFESSIONAL = '2';
    case OTHER = '3';

    public static function toArray(): array
    {
        return array_reduce(self::cases(), function ($carry, $case) {
            $carry[$case->value] = __(strtolower($case->name));
            return $carry;
        }, []);
    }

    public static function fromName(string $name): ?self
    {
        foreach (self::cases() as $case) {
            if (strtolower($case->name) === strtolower($name)) {
                return $case;
            }
        }
        return null;
    }
}