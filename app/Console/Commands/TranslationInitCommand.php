<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Services\TranslationInitService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
final class TranslationInitCommand extends Command
{
    protected $signature = 'translations:init 
                        {--lang= : Comma-separated language codes} 
                        {--app= : Comma-separated app ids}';
    protected $description = 'Initialize translations for all apps and languages';

    public function handle(TranslationInitService $init): int
    {
        try {
            $source = base_path('lang');            
            $target = resource_path('lang'); 
            
            $langObjects = [];
            $appObjects = [];

            if($this->option('lang')) {
                $langs = array_map('trim', explode(',', $this->option('lang') ?? ''));
                $langObjects = array_map(fn($l) => ['languageISO2' => $l], $langs);
            }

            if($this->option('app')) {
                $apps  = array_map('trim', explode(',', $this->option('app') ?? ''));
                $appObjects = array_map(fn($a) => ['id' => $a], $apps);
            }

            $result = $init->initTranslation($langObjects, $appObjects);
            $this->info('Finished. File created: ' . $result['written']);

            File::ensureDirectoryExists($target);

            File::copyDirectory($source, $target);

            $this->info("Language files copied from {$source} to {$target}");
            
            if (!empty($result['errors'])) {
                foreach ($result['errors'] as $e) {
                    $this->error($e);
                }
                return self::FAILURE;
            }
            return self::SUCCESS;
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
            return self::FAILURE;
        }
    }
}
