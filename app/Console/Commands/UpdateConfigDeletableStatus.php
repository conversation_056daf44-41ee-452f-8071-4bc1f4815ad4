<?php

namespace App\Console\Commands;

use App\Constants\ConfigConstants;
use App\Models\Config;
use Illuminate\Console\Command;

class UpdateConfigDeletableStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'config:update-deletable-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the deletable status of configs based on whether they are managed by the settings UI';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating config deletable status...');

        // Mark configs that are managed by the settings UI as non-deletable
        $managedConfigs = ConfigConstants::OVERRIDABLE_CONFIGS;

        Config::whereIn('key', $managedConfigs)->update(['deletable' => false]);

        $this->info('Updated ' . count($managedConfigs) . ' managed configs as non-deletable.');

        // Mark all other configs as deletable (this is already the default, but just to be explicit)
        Config::whereNotIn('key', $managedConfigs)->update(['deletable' => true]);

        $otherConfigsCount = Config::whereNotIn('key', $managedConfigs)->count();
        $this->info('Updated ' . $otherConfigsCount . ' other configs as deletable.');

        $this->info('Config deletable status update completed!');
    }
}
