<?php

namespace App\Notifications;

use App\Models\User;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class Notify extends Notification implements ShouldQueue, ShouldBroadcast
{
    use Queueable;

    public User $user;
    public string $notificationType;
    public string $bodyMessage;
    public string $title;
    

    public function __construct(User $user, string $type = 'info', string $title = '', string $body = '')
    {
        $this->user = $user;
        $this->notificationType = $type;
        $this->bodyMessage = $body ?: "Welcome aboard, {$user->name}!";
        $this->title = $title ?: "Hello, {$user->name}!";
    }

    public function via($notifiable): array
    {
        return ['database', 'broadcast'];
    }

    public function toDatabase($notifiable): array
    {
        $title = '';
        switch ($this->notificationType) {
            case 'success': $title = 'Action Successful!'; break;
            case 'warning': $title = 'Warning Issued'; break;
            case 'error':   $title = 'An Error Occurred'; break;
            default:        $title = 'New Notification';
        }
        return [
            'type' => $this->notificationType, 
            'title' => $this->title,
            'message' => $this->bodyMessage, 
            'original_user_id' => $this->user->id,
            'link' => '/users/' . $this->user->id, 
        ];
    }

    public function toArray($notifiable): array
    {
        $title = '';
        switch ($this->notificationType) {
            case 'success': $title = 'Action Successful!'; break;
            case 'warning': $title = 'Warning Issued'; break;
            case 'error':   $title = 'An Error Occurred'; break;
            default:        $title = 'New Notification';
        }
        return [
            'type' => $this->notificationType, 
            'title' => $this->title,
            'message' => $this->bodyMessage, 
            'original_user_id' => $this->user->id,
            'link' => '/users/' . $this->user->id, 
            'toaster_duration' => 5000,
        ];
    }

    public function broadcastOn(): array
    {
        if ($this->user && $this->user->id) {
            $channel = new PrivateChannel('App.Models.User.'.$this->user->id);
            return [$channel];
        }
        return [];
    }
}
