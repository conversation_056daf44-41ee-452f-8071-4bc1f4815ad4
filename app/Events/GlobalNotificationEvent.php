<?php
namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class GlobalNotificationEvent implements ShouldBroadcast
{
    use Dispatchable, SerializesModels;

    public string $message;

    public function __construct(string $message)
    {
        $this->message = $message;
    }

    // Send to all users via a public channel
    public function broadcastOn()
    {
        $channel = new PrivateChannel('App.Models.User.1');
        return [$channel];
    }
}

