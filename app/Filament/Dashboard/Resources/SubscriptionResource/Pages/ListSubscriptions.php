<?php

namespace App\Filament\Dashboard\Resources\SubscriptionResource\Pages;

use App\Filament\Dashboard\Resources\SubscriptionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSubscriptions extends ListRecords
{
    protected static string $resource = SubscriptionResource::class;

    public bool $hasRecords = false;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function mount(): void
    {
        parent::mount(); 

        $this->hasRecords = $this->getTableQuery()->count() > 0;
    }

    // public function getView(): string
    // {
    //     // return $this->hasRecords
    //     //     ? 'filament.dashboard.resources.subscription-resource.pages.my-subscriptions'
    //     //     : 'filament.dashboard.resources.subscription-resource.pages.subscriptions';
    //     return 'filament.dashboard.resources.subscription-resource.pages.my-subscriptions';
    // }

    public function getView(): string
    {
        if ($this->getTableRecords()->count() === 0) {
            return 'filament.dashboard.resources.subscription-resource.pages.subscriptions';
        }

        // return 'filament.dashboard.resources.subscription-resource.pages.subscriptions';
        return 'filament.dashboard.resources.subscription-resource.pages.my-subscriptions';
        // return parent::getView();
    }
}
