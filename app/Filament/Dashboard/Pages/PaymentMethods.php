<?php

namespace App\Filament\Dashboard\Pages;

use App\Constants\PaymentProviderConstants;
use App\Services\PaymentProviders\PaymentService;
use App\Services\PaymentProviders\Stripe\StripeProvider;

use Filament\Pages\Page;
use Illuminate\Support\Facades\Log;

class PaymentMethods extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected $listeners = ['payment-method-added' => 'loadCards'];
    protected static string $view = 'filament.dashboard.pages.payment-methods';

    public $cards = [];
    public $selectedCardId = null;

    public function getHeaderActions(): array
    {
        return [];
    }

    public function getTitle(): string
    {
        return '';
    }


    public function updateCardChange($cardId)
    {
        $user = auth()->user();

        $paymentProviderStrategy = app(PaymentService::class)->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        /** @var StripeProvider $stripeProvider */
        $stripeProvider = $paymentProviderStrategy;

        try {
            $stripeProvider->updateDefaultPaymentMethod($user, $cardId);
        } catch (ApiErrorException $e) {
            logger()->error('An error occurred while updating the default card');
            Log::error('An error occurred while updating the default card: ' . $e->getMessage());
        }
    }


    public function loadCards(): void
    {
        $paymentProviderStrategy = app(PaymentService::class)->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );

        $user = auth()->user();
        $this->cards = $paymentProviderStrategy->getPaymentMethods($user);
        $this->selectedCardId = collect($this->cards)->firstWhere('is_default', true)['id'] ?? null;
        Log::info('Payment methods loaded', ['cards' => $this->cards]);
        Log::info('Selected card ID', ['selectedCardId' => $this->selectedCardId]);
        $this->dispatch('close-modal', id: 'payment-method-form-modal');

    }

    public function mount(): void
    {
        $this->selectedCardId = collect($this->cards)->firstWhere('is_default', true)['id'] ?? null;
    }

    public function confirmCardChange(PaymentService $paymentService)
    {



        $user = auth()->user();

        $paymentProviderStrategy = $paymentService->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        /** @var StripeProvider $stripeProvider */
        $stripeProvider = $paymentProviderStrategy;


        try {

            $stripeProvider->updateDefaultPaymentMethod($user, $this->pendingCardId);
            $this->selectedCardId = $this->pendingCardId;
            $this-> closeCardChangeModal();

        } catch (ApiErrorException $e) {
            logger()->error('An error occurred while updating the default card');

            Log::error('An error occurred while updating the default card: ' . $e->getMessage());

        }
    }

    public $pendingCardId = null;

    public function promptCardChangeModal($cardId)
    {
        $this->pendingCardId = $cardId;
        $this->dispatch('open-modal', id: 'confirm-update-payment-method-modal');
    }
    public function closeCardChangeModal()
    {
        $this->dispatch('close-modal', id: 'confirm-update-payment-method-modal');
        $this->pendingCardId = null;
    }

}
