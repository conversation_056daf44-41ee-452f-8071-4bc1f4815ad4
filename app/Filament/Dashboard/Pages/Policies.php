<?php

namespace App\Filament\Dashboard\Pages;

use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

class Policies extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = 'Policies';
    protected static ?string $slug = 'policies';
    protected static bool $shouldRegisterNavigation = true;
    protected static string $view = 'filament.dashboard.pages.policies';

    public function getTitle(): string | Htmlable
    {
        return '';
    }
}

