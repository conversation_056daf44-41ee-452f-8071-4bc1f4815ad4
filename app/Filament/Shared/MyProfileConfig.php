<?php

namespace App\Filament\Shared;

use App\Filament\Shared\CustomMyProfilePage;
use Jeffgreco13\FilamentBreezy\BreezyCore;

class MyProfileConfig
{
    public static function breezyCorePlugin()
    {
        return BreezyCore::make()
            ->myProfile()
            ->myProfileComponents([
                \App\Livewire\CustomPersonalInfo::class,
                \App\Livewire\AddressForm::class,
                \App\Livewire\ContactForm::class,
                \App\Livewire\CustomUpdatePassword::class,
                \App\Livewire\DeleteAccountForm::class,
            ])
            ->customMyProfilePage(CustomMyProfilePage::class)
            ->withoutMyProfileComponents([
                'personal_info',
                'update_password',
            ]);
    }
}
