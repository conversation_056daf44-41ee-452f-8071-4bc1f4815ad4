<?php

namespace App\Filament\Shared;

use Illuminate\Support\Collection;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Jeffgreco13\FilamentBreezy\Pages\MyProfilePage;

class CustomMyProfilePage extends MyProfilePage
{
    // public function getTitle(): string
    // {
    //     return t('core.my_account.title');
    // }
    

    protected static string $view = 'filament.dashboard.pages.custom-my-profile';

    public function getTitle(): string
    {
        return '';
    }

    public function getHeading(): string
    {
        return '';
    }

    public function getSubheading(): ?string
    {
        return '';
    }


    public static function getNavigationLabel(): string
    {
        return '';
    }

    public function getMyProfileComponents(): Collection
    {
        return collect([
            \App\Livewire\CustomPersonalInfo::class,
            \App\Livewire\AddressForm::class,
            \App\Livewire\ContactForm::class,
            \App\Livewire\CustomUpdatePassword::class,
            \App\Livewire\DeleteAccountForm::class,
        ]);
    }

}
