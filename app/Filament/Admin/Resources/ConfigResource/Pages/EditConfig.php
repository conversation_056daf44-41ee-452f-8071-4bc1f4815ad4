<?php

namespace App\Filament\Admin\Resources\ConfigResource\Pages;

use App\Filament\Admin\Resources\ConfigResource;
use App\Models\Config;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditConfig extends EditRecord
{
    protected static string $resource = ConfigResource::class;

    public function getTitle(): string
    {
        return 'Edit Setting';
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        $record->update($data);

        Notification::make()
            ->success()
            ->title(__('Config updated'))
            ->body(__('The config has been updated successfully.'))
            ->send();

        return $record;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->visible(fn (Config $record): bool => $record->deletable)
                ->requiresConfirmation()
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title(__('Config deleted'))
                        ->body(__('The config has been deleted successfully.'))
                ),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
