<?php

namespace App\Filament\Admin\Resources\ConfigResource\Pages;

use App\Filament\Admin\Resources\ConfigResource;
use App\Models\Config;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateConfig extends CreateRecord
{
    protected static string $resource = ConfigResource::class;

    public function getTitle(): string
    {
        return 'Create Setting';
    }

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        $config = Config::create($data);

        Notification::make()
            ->success()
            ->title(__('Config created'))
            ->body(__('The config has been created successfully.'))
            ->send();

        return $config;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
