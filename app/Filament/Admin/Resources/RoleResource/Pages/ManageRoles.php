<?php

namespace App\Filament\Admin\Resources\PermissionResource\Pages;

use App\Filament\Admin\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;

class ManageRoles extends ManageRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label(t('core.action.add'))
                ->using(function (array $data): Model {
                    $data['user_id'] = Auth::user()->id;
                    $record = static::getModel()::create($data);
                    if (isset($data['permissions'])) {
                        $permissionIds = $data["permissions"];
                        $permissionNames = Permission::whereIn('id', $permissionIds)->pluck('name')->all();
                        $record->syncPermissions($permissionNames);
                    }
                    return $record;
                })
        ];
    }
}
