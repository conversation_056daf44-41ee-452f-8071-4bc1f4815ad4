<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RoleAppResource\Pages;
use App\Filament\Admin\Resources\RoleAppResource\RelationManagers;
use App\Models\RoleApp;
use App\Models\AppModel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RoleAppResource extends Resource
{
    protected static ?string $model = RoleApp::class;

    // protected static ?string $navigationGroup = 'App Management';

    public static function getModelLabel(): string
    {
        // return t('core.role_app');
        return 'Role App';
    }

    public static function getNavigationGroup(): ?string
    {
        // return t('core.user_management');
         return t('core.app_management');
    }
    

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('appId')
                    ->label('Application')
                    ->options(AppModel::query()->pluck('name', 'id'))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('role')
                    ->label('Role')
                    ->options([
                        'admin' => 'Admin',
                        'pvgis24-customer' => 'PVGIS24 Customer',
                        'helpdesk-admin' => 'Helpdesk Admin',
                    ])
                    ->required()
                    ->searchable(),

                Forms\Components\Toggle::make('default')
                    ->label(t('core.default')),

                Forms\Components\Toggle::make('deleted')
                    ->label(t('core.deleted')),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('app.name')->label('Application')->searchable(),
                Tables\Columns\TextColumn::make('role')->searchable(),
                Tables\Columns\IconColumn::make('default')->boolean()->label(t('core.default')),
                Tables\Columns\IconColumn::make('deleted')->boolean()->label(t('core.deleted')),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->since(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('appId')
                    ->options(AppModel::query()->pluck('name', 'id')),
                Tables\Filters\TernaryFilter::make('default')->label(t('core.default')),
                Tables\Filters\TernaryFilter::make('deleted')->label(t('core.deleted')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoleApps::route('/'),
            'create' => Pages\CreateRoleApp::route('/create'),
            'edit' => Pages\EditRoleApp::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false; 
    }
}
