<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RedirectionUrlResource\Pages;
use App\Services\CmsService;
use Filament\Forms\Form;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Exceptions\Halt;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class RedirectionUrlResource extends Resource
{
    protected static ?string $slug = 'redirection-url';

    protected static ?string $model = null;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return t('core.redirection_urls');
    }

    public static function getNavigationGroup(): ?string
    {
        return t('core.cms');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(name: 'url')
                    ->maxLength(1000)
                    ->required()
                    ->columnSpanFull()
                    ->label(t('core.url')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('updatedAt', 'desc')
            ->paginated([200, 'all'])
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->listWithLineBreaks()
                    ->searchable()
                    ->wrap()
                    ->grow(false)
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->label(t('core.key')),
                Tables\Columns\TextColumn::make('oldUrl')
                    // ->prefix('https://domain.com')
                    ->formatStateUsing(function ($state, $record) {
                        $isocode = $record->languageISO2 ?? null;
                        if ($isocode) {
                            return parseTemplate($state, ['lang' => strtolower($isocode)]);
                        }
                        return $state;
                    })
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 200px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->grow(false)
                    ->label(t('core.old_url')),
                Tables\Columns\TextColumn::make('newUrl')
                    // ->prefix('https://domain.com')
                    ->formatStateUsing(function ($state, $record) {
                        $isocode = $record->languageISO2 ?? null;
                        if ($isocode) {
                            return parseTemplate($state, ['lang' => strtolower($isocode)]);
                        }
                        return $state;
                    })
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 200px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->grow(false)
                    ->label(t('core.new_url')),
                Tables\Columns\TextColumn::make('flagCode')
                    ->searchable()
                    ->wrap()
                    ->grow(false)
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->label(t('core.language'))
                    ->getStateUsing(function ($record) {
                        $flagCode = strtolower($record->flagCode ?? '');
                        $isocode = strtoupper($record->languageISO2 ?? '');
                        $flagHtml = $flagCode ? "<span class='flag-icon flag-icon-{$flagCode}' style='display:block;margin:auto;width:32px;height:24px;'></span>" : '';
                        $isoHtml = $isocode ? "<div style='font-weight:bold;'>{$isocode}</div>" : '';
                        return new HtmlString("<div style='display:flex; gap:10px; align-items:center;'>{$flagHtml}{$isoHtml}</div>");
                    })
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Edit')
                    ->modalHeading('New Redirection URL')
                    ->modalWidth('md')
                    ->using(function (\App\Models\RedirectionUrl $record, array $data) {
                        self::saveRedirectionUrl($record, $data);
                    })
            ]);
    }

    protected static function saveRedirectionUrl(\App\Models\RedirectionUrl $record, array $data){
        $cmsService = app(CmsService::class);
        $response = $cmsService->createCMSRedirectionUrl($record['cmsId'], $data['url'], $record['languageId']);
        if($response == null) {
            Notification::make()
                ->danger()
                ->title('Error')
                ->body('Redirection Url Already Exist')
                ->send();
            throw new Halt();
        }
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageRedirectionUrls::route('/'),
        ];
    }
}
