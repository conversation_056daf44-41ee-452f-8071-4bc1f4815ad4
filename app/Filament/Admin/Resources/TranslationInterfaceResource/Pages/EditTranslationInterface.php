<?php

namespace App\Filament\Admin\Resources\TranslationInterfaceResource\Pages;

use App\Filament\Admin\Resources\TranslationInterfaceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTranslationInterface extends EditRecord
{
    protected static string $resource = TranslationInterfaceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
