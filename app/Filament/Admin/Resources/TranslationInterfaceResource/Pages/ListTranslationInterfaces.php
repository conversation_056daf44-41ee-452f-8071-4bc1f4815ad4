<?php
namespace App\Filament\Admin\Resources\TranslationInterfaceResource\Pages;

use App\Filament\Admin\Resources\TranslationInterfaceResource;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;

// Change from ListRecords to Page

class ListTranslationInterfaces extends Page// Extend Page

{
    protected static string $resource = TranslationInterfaceResource::class;

    protected static string $view = 'filament.admin.pages.list-translation-interfaces';

    // You might want to keep the "Create" action if it's standard
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()
            //     ->url(TranslationInterfaceResource::getUrl('create')),
        ];
    }

    public function getTitle(): string | Htmlable
    {
        return t('core.translation');
    }
}
