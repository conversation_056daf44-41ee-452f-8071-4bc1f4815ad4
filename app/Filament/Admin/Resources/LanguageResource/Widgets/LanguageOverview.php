<?php
namespace App\Filament\Admin\Resources\LanguageResource\Widgets;

use App\Services\TranslationApiService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class LanguageOverview extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        $apiService         = app(TranslationApiService::class);
        $interfaceKeysCount = $apiService->getInterfaceTranslationKeyStat();

        return [
            Stat::make('English words', $interfaceKeysCount['wordCount'] ?: 0)
                ->extraAttributes([
                    'class' => 'flagi flagi-us text-red-500',
                ]),
            Stat::make('Keys', $interfaceKeysCount['keyCount'] ?: 0),
        ];
    }
}
