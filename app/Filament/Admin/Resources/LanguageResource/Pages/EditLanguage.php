<?php

namespace App\Filament\Admin\Resources\LanguageResource\Pages;

use App\Filament\Admin\Resources\LanguageResource;
use App\Services\TranslationApiService; 
use App\Models\Language;              
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;  
use Illuminate\Database\Eloquent\Model;   

class EditLanguage extends EditRecord
{
    protected static string $resource = LanguageResource::class;

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $apiService = app(TranslationApiService::class);

        $response = $apiService->updateLanguage($record->id, $data);

        if (isset($response['affected'])) {
            // Notification::make()
            //     ->success()
            //     ->title('Language Updated')
            //     ->body('The language has been successfully updated via the API.')
            //     ->send();

            Language::bustSushiCache();


            $record->fill($response);
            return $record;
        } else {
            // 11. The API call failed
            // Notification::make()
            //     ->danger()
            //     ->title('API Error')
            //     ->body($response['message'] ?? 'Failed to update the language via the API.')
            //     ->send();

            $this->halt();
            return $record; 
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->action(function (Language $record) { 
                    $apiService = app(TranslationApiService::class);
                    $success = $apiService->deleteLanguage($record->id);

                    if ($success) {
                        Notification::make()
                            ->success()
                            ->title('Language Deleted')
                            ->body('The language has been successfully deleted via the API.')
                            ->send();
                        Language::bustSushiCache();
                        // Redirect to index after successful deletion
                        $this->redirect($this->getResource()::getUrl('index'));
                    } else {
                        Notification::make()
                            ->danger()
                            ->title('API Error')
                            ->body('Failed to delete the language via the API.')
                            ->send();
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    // Optional: Customize notification titles
    // protected function getSavedNotificationTitle(): ?string
    // {
    //     return 'Language updated via API';
    // }
}
