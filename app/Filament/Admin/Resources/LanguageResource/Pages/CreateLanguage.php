<?php

namespace App\Filament\Admin\Resources\LanguageResource\Pages;

use App\Filament\Admin\Resources\LanguageResource;
use App\Services\TranslationApiService; 
use App\Models\Language;            
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification; 
use Illuminate\Database\Eloquent\Model;   

class CreateLanguage extends CreateRecord
{
    protected static string $resource = LanguageResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $apiService = app(TranslationApiService::class);

        $response = $apiService->createLanguage($data);

        if (isset($response["identifiers"])) {
            // Notification::make()
            //     ->success()
            //     ->title('Language Created')
            //     ->body('The language has been successfully created via the API.')
            //     ->send();

            Language::bustSushiCache();

            return new Language($response);
        } else {
            // Notification::make()
            //     ->danger()
            //     ->title('API Error')
            //     ->body($response['message'] ?? 'Failed to create the language via the API. Please check the logs or API response.') // Provide a more specific error if available
            //     ->send();

            $this->halt();

            return new Language($data);
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
