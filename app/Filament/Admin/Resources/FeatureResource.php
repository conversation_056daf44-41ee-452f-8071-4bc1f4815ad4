<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\FeatureResource\Pages;
use App\Filament\Admin\Resources\FeatureResource\RelationManagers;
use App\Models\Feature;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FeatureResource extends Resource
{
    protected static ?string $model = Feature::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Product Management');
    }

    public static function getNavigationLabel(): string
    {
        return 'Product Features'; 
    }

    public static function getModelLabel(): string
    {
        return 'Product Feature';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                
                    Forms\Components\Textarea::make('description')
                        ->maxLength(65535)
                        ->required()
                        ->rows(3),
                    
                    Forms\Components\TextInput::make('key')
                        ->required()
                        ->maxLength(255),
                    
                    Forms\Components\TextInput::make('ui_order')
                        ->numeric()
                        ->required(),
                    Forms\Components\Card::make()
                        ->columns(3)
                        ->schema([
                            Forms\Components\Toggle::make('display_for_free')
                                ->label('Display for Free'),
                        
                            Forms\Components\Toggle::make('allow_for_free')
                                ->label('Allow for Free'),
                            Forms\Components\Toggle::make('hide')
                                ->label('Hide'),
                        ])
                    
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('key')->sortable(),
                Tables\Columns\TextColumn::make('ui_order')->sortable(),
                // Tables\Columns\IconColumn::make('display_for_free')
                //     ->boolean(),
                // Tables\Columns\IconColumn::make('allow_for_free')
                //     ->boolean(),
                // Tables\Columns\IconColumn::make('hide')
                //     ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->since()
                    ->sortable()
                    ->label('Created'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFeatures::route('/'),
            'create' => Pages\CreateFeature::route('/create'),
            'edit' => Pages\EditFeature::route('/{record}/edit'),
        ];
    }
}
