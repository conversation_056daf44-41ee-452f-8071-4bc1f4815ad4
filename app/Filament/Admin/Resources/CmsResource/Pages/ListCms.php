<?php

namespace App\Filament\Admin\Resources\CmsResource\Pages;

use App\Filament\Admin\Resources\CmsResource;
use App\Services\CmsService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCms extends ListRecords
{
    protected static string $resource = CmsResource::class;

    // protected static string $view = 'filament.admin.pages.cms.cms-create';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
