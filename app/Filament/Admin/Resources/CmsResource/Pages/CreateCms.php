<?php

namespace App\Filament\Admin\Resources\CmsResource\Pages;

use App\Filament\Admin\Resources\CmsResource;
use App\Models\Cms;
use App\Services\CmsService;
use App\Services\TranslationApiService;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class CreateCms extends CreateRecord
{
    protected static string $resource = CmsResource::class;


    protected function performUrl($url) {
        if ($url && strpos($url ?? '', '/{lang}/') !== 0) {
            $url = '/{lang}/' . $url;
        }
        return $url;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $data['status'] = (int) $data['status'];
        $data['url'] = $this->performUrl($data['url']);

        $response = app(CmsService::class)->createContentManagementSystem($data);

        if (isset($response['key'])) {
            Cms::bustSushiCache();
            if($data['url']) {
                $translatedUrlBody = [
                    'apiId'      => config('services.cms_api.api_id'),
                    'tableName'  => "ContentManagementSystem",
                    'rowId'      => $response['id'],
                    'columnName' => "url",
                    'type'       => "string",
                    'translations' => [
                        [
                            'languageId'  => 1,
                            'translation' => $data['url']
                        ]
                    ],
                    'translateAll' => false,
                ];
                app(TranslationApiService::class)->saveResource($translatedUrlBody);
            }
            $model = new Cms([
                'id' => $response['id'] ?? null,
                ...$data,
            ]);
            $model->exists = true;
            return $model;
        } else {
            if(isset($response['error']) && strpos($response['error'], 'ER_DUP_ENTRY') !== false) {
                Notification::make()
                    ->title('Erreur lors de la création')
                    ->body('Clé dupliquée, veuillez vérifier les données saisies.')
                    ->danger()
                    ->send();
            }
            Log::error('Error creating CMS', [
                'response' => $response,
                'data' => $data,
            ]);
            $this->halt();
            return new Cms($data);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('mediaManagerContainer')
            ->label('') // no label, so it's just the view content
            ->modal(false) // not clickable
            ->hiddenLabel() // hide the label space
            ->extraAttributes(['style' => 'padding:0; border:none; background:none;'])
            ->view('filament.admin.pages.cms.media-manager')
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
