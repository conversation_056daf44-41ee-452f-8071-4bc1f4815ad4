<?php

namespace App\Filament\Admin\Resources\IpBlockResource\Pages;

use App\Filament\Admin\Resources\IpBlockResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageIpBlocks extends ManageRecords
{
    protected static string $resource = IpBlockResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
