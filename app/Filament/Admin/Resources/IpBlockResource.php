<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\IpBlockResource\Pages;
use App\Filament\Admin\Resources\IpBlockResource\RelationManagers;
use App\Models\IpBlock;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class IpBlockResource extends Resource
{
    protected static ?string $model = IpBlock::class;

    
    protected static ?string $navigationLabel = 'IP Management';
    protected static ?string $navigationGroup = 'Locales';
    protected static ?string $slug = 'ip-managements';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('country_id')
                    ->relationship('country', 'name')
                    ->required(),
                Forms\Components\TextInput::make('cidr')
                    ->required()
                    ->label('IP address (CIDR)'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('country.name')
                    ->label('Country')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('cidr')
                    ->label('IP address')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageIpBlocks::route('/'),
        ];
    }

    public static function getPluralLabel(): string
    {
        return 'IP Management';
    }

    public static function getModelLabel(): string
    {
        return 'IP address ';
    }
}
