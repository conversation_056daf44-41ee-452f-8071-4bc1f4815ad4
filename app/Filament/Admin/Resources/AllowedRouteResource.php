<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\AllowedRouteResource\Pages;
use App\Filament\Admin\Resources\AllowedRouteResource\RelationManagers;
use App\Models\AllowedRoute;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AllowedRouteResource extends Resource
{
    protected static ?string $model = AllowedRoute::class;

    //  protected static ?string $navigationGroup = 'App Management';

    public static function getModelLabel(): string
    {
        // return t('core.role_app');
        return 'Allowed Route';
    }


    public static function getNavigationGroup(): ?string
    {
        // return t('core.user_management');
         return t('core.app_management');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('roleAppId')
                    ->label('Role App')
                    ->options(\App\Models\RoleApp::query()->pluck('role', 'id'))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('routeId')
                    ->label('Route')
                    ->options(\App\Models\Route::query()->pluck('pattern', 'id'))
                    ->searchable()
                    ->required(),

                Forms\Components\Select::make('methods')
                    ->label('Methods')
                    ->options([
                        'GET' => 'GET',
                        'POST' => 'POST',
                        'PUT' => 'PUT',
                        'DELETE' => 'DELETE',
                        'PATCH' => 'PATCH',
                        'OPTIONS' => 'OPTIONS',
                    ])
                    ->multiple()
                    ->required()
                    ->columnSpan(6),

                Forms\Components\Toggle::make('deleted')
                    ->label(t('core.deleted')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('roleApp.role')->label('Role App')->searchable(),
                Tables\Columns\TextColumn::make('route.pattern')->label('Route')->searchable(),
                Tables\Columns\TextColumn::make('methods')->searchable(),
                Tables\Columns\IconColumn::make('deleted')->boolean()->label(t('core.deleted')),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->since(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roleAppId')
                    ->label('Role App')
                    ->options(\App\Models\RoleApp::query()->pluck('role', 'id')),
                Tables\Filters\SelectFilter::make('routeId')
                    ->label('Route')
                    ->options(\App\Models\Route::query()->pluck('pattern', 'id')),
                Tables\Filters\TernaryFilter::make('deleted')->label(t('core.deleted')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAllowedRoutes::route('/'),
            'create' => Pages\CreateAllowedRoute::route('/create'),
            'edit' => Pages\EditAllowedRoute::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false; 
    }
}
