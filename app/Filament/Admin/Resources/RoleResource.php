<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PermissionResource\Pages\ManageRoles;
use Filament\Forms;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?int $navigationSort = 3;

    public static function getNavigationGroup(): ?string
    {
       return t('core.app_management');
        
    }

    public static function getNavigationLabel(): string
    {
        return t('core.role.page_title');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->helperText(t('core.role.text_helper'))
                        ->maxLength(255),
                    
                    Forms\Components\View::make('filament.forms.components.permission-checkboxes')
                        ->statePath('permissions')
                        ->dehydrated(true)
                        ->afterStateHydrated(function (Forms\Components\View $component, ?Model $record) {
                            $permissions = Permission::all()->groupBy(function ($permission) {
                                $parts = explode(' ', $permission->name, 2);
                                return count($parts) > 1
                                    ? ucwords(str_replace('-', ' ', $parts[1]))
                                    : 'Other';
                            });

                            $selected = $record
                                ? $record->permissions()->pluck('id')->all()
                                : [];

                            $component->viewData([
                                'permissions' => $permissions,
                                'selectedPermissions' => $selected,
                                'getStatePath' => $component->getStatePath(),
                            ]);

                             $component->state($selected);
                        }),
                    
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with('permissions'))
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('permissions.name')
                    ->label('Permissions')
                    ->badge()
                    ->wrap()
                    ->toggleable()
                    ->searchable(), 
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime(config('app.datetime_format'))->sortable(),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\EditAction::make()->label(t('core.action.edit'))
                    ->using(function (Model $record, array $data): Model {
                        $data['user_id'] = Auth::user()->id;
                        $record->update($data);
                        if (isset($data['permissions'])) {
                            $permissionIds = $data["permissions"] ?? [];
                            $permissionNames = Permission::whereIn('id', $permissionIds)->pluck('name')->all();
                            $record->syncPermissions($permissionNames);
                        }
                        return $record;
                }),
                Tables\Actions\DeleteAction::make()
                    ->label(t('core.action.delete')),
            ])
            ->bulkActions([
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => ManageRoles::route('/'),
        ];
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
