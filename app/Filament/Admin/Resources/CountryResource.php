<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CountryResource\Pages;
use App\Filament\Admin\Resources\CountryResource\RelationManagers;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CountryResource extends Resource
{
    protected static ?string $model = Country::class;


    public static function getModelLabel(): string
    {
        return t('core.country.title');
    }

    public static function getNavigationGroup(): ?string
    {
        return t('core.locales');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(t('core.country.name'))
                            ->required(),
                        Forms\Components\Select::make('region_id')
                            ->label(t('core.region.title'))
                            ->relationship('region', 'name')
                            ->searchable()
                            ->preload()
                            ->nullable(),
                        Forms\Components\TextInput::make('normalized_name')
                            ->label(t('core.country.normalized_name'))
                            ->required(),
                        Forms\Components\TextInput::make('code_alpha_2')
                            ->label(t('core.country.code_alpha_2'))
                            ->required(),
                        Forms\Components\TextInput::make('code_alpha_3')
                            ->label(t('core.country.code_alpha_3'))
                            ->required(),
                        Forms\Components\TextInput::make('timezone_offset')
                            ->label(t('core.country.timezone_offset'))
                            ->required(),
                        Forms\Components\Toggle::make('status')
                            ->label(t('core.country.status')),
                ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(t('core.country.name')),
                Tables\Columns\TextColumn::make('region.name')
                    ->label(t('core.region.title'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('normalized_name')
                    ->label(t('core.country.normalized_name')),
                Tables\Columns\TextColumn::make('code_alpha_2')
                    ->label(t('core.country.code_alpha_2'))
                    ->limit(50),
                Tables\Columns\TextColumn::make('code_alpha_3')
                    ->label(t('core.country.code_alpha_3'))
                    ->limit(50),
                Tables\Columns\TextColumn::make('timezone_offset')
                    ->label(t('core.country.timezone_offset'))
                    ->limit(50),
                Tables\Columns\IconColumn::make('status')->boolean()
                    ->label(t('core.country.status')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('region_id')
                    ->label(t('core.region.title'))
                    ->relationship('region', 'name')
                    ->preload()
                    ->searchable(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(t('core.action.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(t('core.action.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label(t('core.action.bulk_delete')),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCountries::route('/'),
        ];
    }
}
