<?php

namespace App\Filament\Admin\Resources;

use App\Models\Feature;
use App\Models\Product;
use App\Services\ProductService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Unique;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Builder;
class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?int $navigationSort = 1;
    

    public static function getNavigationGroup(): ?string
    {
        return __('Product Management');
    }

    // public static function getNavigationBadge(): ?string
    // {
    //     $productService = app(ProductService::class);
    //     return (string) $productService->countProducts();
    // }

    // public function getData(): array
    // {
    //     $productService = app(ProductService::class);

    //     return [
    //         'data' => $productService->getAllProducts(),
    //     ];
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('slug')
                        ->dehydrateStateUsing(function ($state, \Filament\Forms\Get $get) {
                            if (empty($state)) {
                                // add a random string if there is a product with the same slug
                                $state = Str::slug($get('name'));
                                if (Product::where('slug', $state)->exists()) {
                                    $state .= '-'.Str::random(5);
                                }

                                return Str::slug($state);
                            }

                            return $state;
                        })
                        ->helperText(__('Leave empty to generate slug automatically from product name.'))
                        ->maxLength(255)
                        ->rules(['alpha_dash'])
                        ->unique(ignoreRecord: true)
                        ->disabledOn('edit'),
                    Forms\Components\Textarea::make('description')
                        ->helperText(__('One line description of the product.')),
                    Forms\Components\Toggle::make('is_popular')
                        ->label(__('Popular product'))
                        ->helperText(__('Mark this product as popular. This will be used to highlight this product in the pricing page.')),
                    Forms\Components\Toggle::make('is_default')
                        ->label(__('Is default product'))
                        ->validationAttribute(__('default product'))
                        ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule) {
                            return $rule->where('is_default', true);
                        })
                        ->default(false)
                        ->helperText(__('A default product is a kind of a hidden product that allows you to set the features (and metadata) for users that have no active plan. Add a default product if you want to offer a free tier to your users. You can only have 1 default product and it cannot have any plans.')),
                    Forms\Components\Select::make('account_type')
                        ->options([
                            'Professional' => 'Professional',
                            'Particular' => 'Particular',
                            'Credit' => 'Credit',
                        ]),

                    Forms\Components\Grid::make(1)->schema([
                        Forms\Components\CheckboxList::make('features')
                            ->label(__('Select Features'))
                            ->relationship('features', 'name')
                            ->searchable()
                            ->columns(2)
                            ->helperText(__('Choose existing features to attach to this product.'))
                            ->afterStateHydrated(function ($component, $state, $record) {
                                if ($record && $record->exists) {
                                    $selected = $record->features()
                                        ->wherePivot('enabled', true)
                                        ->pluck('features.id')
                                        ->toArray();

                                    $component->state($selected);
                                }
                            })
                            ->saveRelationshipsUsing(function ($component, $state, $record) {
                                $selectedFeatures = is_array($state) ? $state : [];
                                $allFeatures = \App\Models\Feature::pluck('id')->toArray();
                                $syncData = [];
                                foreach ($allFeatures as $featureId) {
                                    $syncData[$featureId] = [
                                        'enabled' => in_array($featureId, $selectedFeatures),
                                    ];
                                }
                                $record->features()->sync($syncData);
                            })
                            ->dehydrated(false), 
                        Forms\Components\Actions::make([
                                Forms\Components\Actions\Action::make('select_all_features')
                                    ->label(__('Select All'))
                                    ->color('primary')
                                    ->size('sm')
                                    ->action(function (Forms\Set $set) {
                                        $allFeatureIds = \App\Models\Feature::pluck('id')->toArray();
                                        $set('features', $allFeatureIds);
                                    }),
                                Forms\Components\Actions\Action::make('remove_all_features')
                                    ->label(__('Remove All'))
                                    ->color('danger')
                                    ->size('sm')
                                    ->action(function (Forms\Set $set) {
                                        $set('features', []);
                                    }),
                        ])->alignStart()
                    ])
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading(__('A product is bundle of features that you offer to your customers.'))
            ->description(__('If you want to provide a Starter, Pro and Premium offerings to your customers, create a product for each of them.'))
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
                Tables\Columns\TextColumn::make('account_type')->searchable()->sortable(),
                Tables\Columns\IconColumn::make('is_popular')->label(__('Popular'))->boolean(),
                Tables\Columns\TextColumn::make('features_count')
                    ->label(__('Features'))
                    ->getStateUsing(function ($record) {
                        $features = $record->features;

                        if (is_null($features)) {
                            return "0/0";
                        }

                        if (is_a($features, \Illuminate\Database\Eloquent\Collection::class)) {
                            $enabled = $features->where('pivot.enabled', true)->count();
                            $total = $features->count();
                        } else {
                            if (is_string($features)) {
                                $features = json_decode($features, true);
                            }
                            if (is_array($features)) {
                                $enabled = count(array_filter($features, function ($feature) {
                                    return $feature['pivot']['enabled'] ?? false;
                                }));
                                $total = count($features);
                            } else {
                                $enabled = 0;
                                $total = 0;
                            }
                        }

                        return "{$enabled}/{$total}";
                    })
                    ->tooltip(__('Enabled features / Total features')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with('features');
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Admin\Resources\ProductResource\Pages\ListProducts::route('/'),
            'create' => \App\Filament\Admin\Resources\ProductResource\Pages\CreateProduct::route('/create'),
            'edit' => \App\Filament\Admin\Resources\ProductResource\Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    public static function getModelLabel(): string
    {
        return __('Product');
    }

    public static function getNavigationLabel(): string
    {
        return __('Products');
    }
}
