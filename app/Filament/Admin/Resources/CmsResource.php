<?php

namespace App\Filament\Admin\Resources;

// use Abdelhamid<PERSON>rrahmouni\FilamentMonacoEditor\MonacoEditor;

use App\Constants\CmsTypeEnum;
use App\Forms\Components\CustomMonacoEditor;
use App\Filament\Admin\Resources\CmsResource\Pages;
use App\Models\Cms;
use App\Services\CmsService;
use App\Services\TranslationApiService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Log;

class CmsResource extends Resource
{
    // protected static ?string $slug = 'cms';

    protected static ?string $model = null;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return t('core.cms');
    }

    public static function getNavigationGroup(): ?string
    {
        return t('core.cms');
    }

    public static function getLanguages(): array {
        $apiService = resolve(TranslationApiService::class);
        $selectOptions = [];

        try {
            $languagesApiResponse = $apiService->getLanguages(['limit' => 200]);
            $rawLanguageItems = [];

            if (isset($languagesApiResponse['data']) && is_array($languagesApiResponse['data'])) {
                $rawLanguageItems = $languagesApiResponse['data'];
            } elseif (is_array($languagesApiResponse) && !isset($languagesApiResponse['data'])) {
                $rawLanguageItems = $languagesApiResponse;
            }

            if (!empty($rawLanguageItems)) {
                foreach ($rawLanguageItems as $language) {
                    if (isset($language['id']) && isset($language['languageFull'])) {
                        $value = $language['id'];
                        $fullLanguageName = htmlspecialchars($language['languageFull']); // Sanitize
                        $flagCode = isset($language['flagCode']) ? htmlspecialchars($language['flagCode']) : null;

                        $labelHtml = $fullLanguageName;
                        if ($flagCode) {
                            $labelHtml = "<span class='flag-icon flag-icon-".strtolower($flagCode)." rounded me-2'></span>" . $fullLanguageName . " (" . strtoupper($language['languageISO2']) .")";
                        }

                        $selectOptions[$value] = $labelHtml;
                    }
                }
            }
        } catch (\Throwable $e) {
            Log::error('Failed to fetch languages for select component: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
        }
        return $selectOptions;
    }

    public static function removePlaceholderFromUrl($url) {
        if($url) {
            $url = str_replace('/{lang}/', '', $url);
        }
        return $url ;
    }

    public static function form(Form $form): Form
    {
        $defaultLanguageId = 1;
        $selectOptions = self::getLanguages();
        $cmsTypeOptions = CmsTypeEnum::toArray();
        $cmsRecordData = $form->getRecord();

        return $form
            ->schema([
                Forms\Components\Select::make('lang')
                    ->label(t('core.language'))
                    ->required()
                    ->options(function () use ($selectOptions): array {
                        return $selectOptions;
                    })
                    ->allowHtml()
                    ->searchable()
                    ->placeholder(t('core.select_a_language'))
                    ->default($defaultLanguageId)
                    ->live()
                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get, ?string $state, Forms\Components\Select $component) use ($cmsRecordData) {
                        $record = $component->getRecord();
                        $recordId = $record['id'];
                        $transformedRecord = [
                            'title' => isset($cmsRecordData['title']) ? $cmsRecordData['title'] : '',
                            'url' => isset($cmsRecordData['url']) ? self::removePlaceholderFromUrl($cmsRecordData['url']) : '',
                            'content' => isset($cmsRecordData['content']) ? $cmsRecordData['content'] : '',
                            'metaTitle' => isset($cmsRecordData['metaTitle']) ? $cmsRecordData['metaTitle'] : '',
                            'metaDescription' => isset($cmsRecordData['metaDescription']) ? $cmsRecordData['metaDescription'] : '',
                            'oldUrl' => [],
                        ];
                        if ($state && $get('key')) {
                            $language = resolve(TranslationApiService::class)->getLanguage($state);
                            $languageId = $language['id'];
                            $languageISO2 = $language['languageISO2'];
                            $isBaseLanguage = $language['isBaseLanguage'];
                            $cmsTranslationRequestBody = [
                                "meta" => [
                                    [
                                        "apiId" => config('services.cms_api.api_id'),
                                        "tableName" => "ContentManagementSystem",
                                        "accessor" => "data",
                                        "rowIds" => [
                                            $recordId
                                        ]
                                    ]
                                ]
                            ];
                            $translatedCmsItemResponse = resolve(TranslationApiService::class)->cmsTranslationTranslate($languageISO2, $cmsTranslationRequestBody);

                            if (!$isBaseLanguage) {
                                $translatedCmsItemData = [];
                                if(isset($translatedCmsItemResponse[config('services.cms_api.api_id')])) {
                                    $translatedCmsItemData = $translatedCmsItemResponse[config('services.cms_api.api_id')]['ContentManagementSystem'][$recordId];
                                }
                                foreach ($transformedRecord as $field => $defaultValue) {
                                    if($field === 'oldUrl') {
                                        continue;
                                    }
                                    $value = isset($translatedCmsItemData[$field]) ? $translatedCmsItemData[$field] : '';
                                    if ($field === 'url') {
                                        $value = self::removePlaceholderFromUrl($value);
                                    }
                                    $transformedRecord[$field] = $value;
                                }
                            }

                            $oldUrlQueryParams = [
                                'where' => json_encode([
                                    'cmsId' => [
                                        'value' => $recordId,
                                        'operator' => '=='
                                    ],
                                    'languageId' => [
                                        'value' => $languageId,
                                        'operator' => '=='
                                    ]
                                ]),
                                'fields' => json_encode(['id', 'url']),
                            ];

                            $cmsService = app(CmsService::class);
                            $oldUrlResponse = $cmsService->getCMSRedirectionUrlV4($oldUrlQueryParams);
                            if(!empty($oldUrlResponse['data'])){
                                $transformedRecord['oldUrl'] = $oldUrlResponse['data'];
                                foreach ($transformedRecord['oldUrl'] as &$item) {
                                    $item['url'] = str_replace('/{lang}/', '', $item['url']);
                                }
                            }

                            foreach ($transformedRecord as $field => $value) {
                                $set($field, isset($value) ? $value : '');
                            }
                        } elseif ($state) {
                            // If creating new, or key is not set, maybe clear fields or set defaults
                            // $set('title', '');
                            // $set('content', '');
                        }
                    }),
                Forms\Components\TextInput::make('key')
                    ->required()
                    ->maxLength(255)
                    ->disabled(fn (string $context): bool => $context === 'edit')
                    ->label(t('core.key')),
                Forms\Components\Select::make('type')
                    ->required()
                    ->options($cmsTypeOptions)
                    ->default("page")
                    ->label(t('core.type')),
                Forms\Components\TextInput::make(name: 'url')
                    ->maxLength(1000)
                    ->nullable()
                    ->label(t('core.url')),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->label(t('core.title')),
                Forms\Components\TextInput::make('metaTitle')
                    ->label(t('core.meta_title')),
                CustomMonacoEditor::make('content')
                    ->required()
                    ->language('html')
                    ->theme('blackboard')
                    ->fontSize('15px')
                    ->placeholderText(t('core.cms_content_placeholder'))
                    ->monacoId('cms-content-editor')
                    ->height('500px')
                    ->enablePreview(true)
                    ->reactive()
                    ->previewHeadEndContent(function ($get) {
                        $style = $get('style');
                        return "<style>$style</style>";
                    })
                    ->previewBodyStartContent(function ($get) {
                        if ($get('type') === 'mail') {
                            return view('components.layouts.email-header')->render();
                        }
                        return '';
                    })
                    ->previewBodyEndContent(function ($get) {
                        if ($get('type') === 'mail') {
                            return view('components.layouts.email-footer')->render();
                        }
                        return '';
                    })
                    ->label(t('core.content')),
                CustomMonacoEditor::make('style')
                    ->language('css')
                    ->theme('blackboard')
                    ->fontSize('15px')
                    ->monacoId('cms-style-editor')
                    ->placeholderText(t('core.cms_style_placeholder'))
                    ->height('175px')
                    ->reactive()
                    ->enablePreview(false)
                    ->label(t('core.style')),
                Forms\Components\TextInput::make('metaDescription')
                    ->maxLength(1000)
                    ->nullable()
                    ->label(t('core.meta_description')),
                Forms\Components\Toggle::make('status')
                    ->default(false)
                    ->required()
                    ->label(t('core.status')),
                Forms\Components\Repeater::make('oldUrl')
                    ->label(t('core.old_url'))
                    ->addable(false)
                    ->reorderable(false)
                    ->deletable(false)
                    ->schema([
                        Forms\Components\TextInput::make('url')
                            ->label('')
                            ->disabled(),
                    ]),
                Forms\Components\Hidden::make('lastChangedBy')
                    ->default(auth()->user()->id),
            ])->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->wrap()
                    ->grow(false)
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->label(t('core.key')),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 200px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->grow(false)
                    ->label(t('core.title')),
                Tables\Columns\TextColumn::make('url')
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 200px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->grow(false)
                    ->label(t('core.cms_url')),
                Tables\Columns\TextColumn::make('username')
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 200px; white-space: normal; word-break: break-all; text-align: center;'])
                    ->alignCenter()
                    ->grow(false)
                    ->label(t('core.last_changed_by')),
                Tables\Columns\TextColumn::make('updatedAt')
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; text-align: center;'])
                    ->alignCenter()
                    ->label(t('core.updated_at'))
                    ->dateTime('d/m/Y H:i'),
                Tables\Columns\TextColumn::make('deletedAt')
                    ->searchable()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; text-align: center;'])
                    ->label(t('core.deleted_at'))
                    ->alignCenter()
                    ->dateTime('d/m/Y H:i'),
                Tables\Columns\IconColumn::make('status')
                    ->boolean()
                    ->wrap()
                    ->extraAttributes(['style' => 'max-width: 120px; white-space: normal; text-align: center;'])
                    ->alignCenter()
                    ->label(t('core.status')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label(t('core.action.edit')),
                Tables\Actions\DeleteAction::make()
                    ->action(function (Cms $record, CmsService $cmsService) {
                        $response = $cmsService->deleteContentManagementSystem($record->id, true);
                        // remove related translation resource Data
                        if (isset($response['error'])) {
                            // Handle the error response
                            // You can use Filament's notification system to show an error message
                            // Notification::make()->danger()->title('Error')->body($response['error'])->send();
                            return;
                        } else {
                            Cms::bustSushiCache();
                        }
                    })
                    ->requiresConfirmation()
                    ->label(t('core.action.delete')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                    ->label(t('core.action.bulk_delete')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCms::route('/'),
            'create' => Pages\CreateCms::route('/create'),
            'edit' => Pages\EditCms::route('/{record}/edit'),
        ];
    }
}
