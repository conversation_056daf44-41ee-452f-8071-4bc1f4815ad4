<?php
namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\LanguageResource\Widgets\LanguageOverview;
use App\Filament\Admin\Resources\TranslationInterfaceResource\Pages;
use App\Models\TranslationInterface;
use App\Services\TranslationApiService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Arr;



class TranslationInterfaceResource extends Resource
{
    public static function getModelLabel(): string
    {
        return t('core.translation_interfaces');
    }
    public static function getPluralModelLabel(): string
    {
        return t('core.translation_interfaces');
    }

    protected static ?string $slug = 'translation-interface';

    protected static ?int $navigationSort = 2;
    
    public static function canCreate(): bool
    {
        return false;
    }
    public static function getNavigationGroup(): ?string
    {
        return t('core.locales');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function table(Table $table): Table
    {
        $apiService    = app(TranslationApiService::class);
        $languagesData = $apiService->getAllLanguages(['limit' => 1000]);

        $languageOptions = [];
        if (isset($languagesData['data']) && is_array($languagesData['data'])) {
            // Use Arr::pluck if 'id' and 'languageFull' are direct keys
            $languageOptions = Arr::pluck($languagesData['data'], 'languageFull', 'id');
        } elseif (is_array($languagesData) && ! isset($languagesData['data'])) {
            // If the response is directly the array of languages
            $languageOptions = Arr::pluck($languagesData, 'languageFull', 'id');
        }

        $data = $apiService->getInterfaceTranslationKey(1, 1, ['limit' => 100]);

        // dd($data);

        return $table
            ->columns([
                TextColumn::make('title'),
            ])
            ->filters([
                SelectFilter::make('language')
                    ->label("Language")
                    ->options($languageOptions)
                    ->searchable()
                    ->default('en'),

                SelectFilter::make('comparison_language')
                    ->label("Comparison language")
                    ->options($languageOptions)
                    ->searchable(),

            ], layout: FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getWidgets(): array
    {
        return [
            LanguageOverview::class,
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListTranslationInterfaces::route('/'),
            // 'create' => Pages\CreateTranslationInterface::route('/create'),
            // 'edit'   => Pages\EditTranslationInterface::route('/{record}/edit'),
        ];
    }
}
