<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RouteResource\Pages;
use App\Filament\Admin\Resources\RouteResource\RelationManagers;
use App\Models\AppModel;
use App\Models\Route;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RouteResource extends Resource
{
    protected static ?string $model = Route::class;

    //  protected static ?string $navigationGroup = 'App Management';

    public static function getModelLabel(): string
    {
        // return t('core.route');
        return 'Route';
    }

    public static function getNavigationGroup(): ?string
    {
        // return t('core.user_management');
         return t('core.app_management');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(12)->schema([

                    Select::make('appId')
                        ->label('Application')
                        ->native(false)
                        ->preload()
                        ->searchable()
                        ->options(fn () => \App\Models\AppModel::orderBy('name')
                            ->pluck('name', 'id')->toArray())
                        ->required()
                        ->columnSpan(4),   

                    TextInput::make('pattern')
                        ->label('Pattern')
                        ->placeholder('/users/{id}')
                        ->helperText('/users/{id}, /health, /v1/orders')
                        ->required()
                        ->maxLength(100)
                        ->rule('regex:/^\\//')
                        ->validationAttribute('pattern')
                        ->columnSpan(6),   

                    Toggle::make('deleted')
                        ->label(t('core.deleted'))
                        ->inline(false)
                        ->onIcon('heroicon-o-check-circle')
                        ->offIcon('heroicon-o-x-circle')
                        ->onColor('danger')
                        ->offColor('success')
                        ->default(false)
                        ->columnSpan(2),   
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('app.name')->label('Application'),
                Tables\Columns\TextColumn::make('pattern')->searchable(),
                Tables\Columns\IconColumn::make('deleted')->boolean(),
                Tables\Columns\TextColumn::make('created_at')->dateTime(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoutes::route('/'),
            'create' => Pages\CreateRoute::route('/create'),
            'edit' => Pages\EditRoute::route('/{record}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false; 
    }
}
