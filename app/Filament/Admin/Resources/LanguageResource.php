<?php
namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\LanguageResource\Pages;
use App\Filament\Admin\Resources\LanguageResource\Widgets\LanguageOverview;
use App\Models\Language;
use App\Services\TranslationApiService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class LanguageResource extends Resource
{
    protected static ?string $slug = 'languages';

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return t('core.languages');
    }

    public static function getPluralModelLabel(): string
    {
        return t('core.languages');
    }

    public static function getNavigationGroup(): ?string
    {
        return t('core.locales');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('languageFull')->label(t('core.language_full'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('languageFullNative')->label(t('core.language_full_native'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('languageISO2')->label(t('core.language_iso_2'))
                    ->required()
                    ->length(2),
                Forms\Components\TextInput::make('languageISO3')->label(t('core.language_iso_3'))
                    ->required()
                    ->length(3),
                Forms\Components\TextInput::make('flagImage')->label(t('core.flag_image'))
                    ->maxLength(255)
                    ->url()
                    ->nullable(),
                // Assuming 'flagCode' is also a field from your DB schema
                Forms\Components\TextInput::make('flagCode')->label(t('core.flag_code'))
                    ->maxLength(255)
                    ->nullable(),
                Forms\Components\Toggle::make('isBaseLanguage')->label(t('core.is_base_language'))
                    ->required()->default(false),
                Forms\Components\Toggle::make('isPopularLanguage')->label(t('core.is_popular_language'))
                    ->required()->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label(t('core.id'))->sortable(),
                TextColumn::make('languageFull')->label(t('core.name'))->searchable()->sortable(),
                TextColumn::make('languageISO2')->label(t('core.iso_2'))->searchable(),
                IconColumn::make('isBaseLanguage')->label(t('core.base_language'))->boolean(),
                IconColumn::make('isPopularLanguage')->label(t('core.popular_language'))->boolean(),
            ])
            ->filters([
                // Basic filters will work on the in-memory Sushi data.
                // For API-side filtering, you'd need custom filter logic
                // that modifies the getRows() call in the Sushi model.
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->action(function (Language $record, TranslationApiService $apiService) {
                        $success = $apiService->deleteLanguage($record->id);
                        if ($success) {
                            // Notification::make()->success()->title('Language deleted successfully.')->send();
                            Language::bustSushiCache();
                        } else {
                            // Notification::make()->danger()->title('Failed to delete language.')->body('The API reported an error.')->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (TranslationApiService $apiService, \Illuminate\Database\Eloquent\Collection $records) {
                            $all_successful = true;
                            foreach ($records as $record) {
                                $success = $apiService->deleteLanguage($record->id);
                                if (! $success) {
                                    $all_successful = false;
                                }

                            }
                            if ($all_successful) {
                                // Notification::make()->success()->title('Selected languages deleted.')->send();
                                Language::bustSushiCache();
                            } else {
                                // Notification::make()->warning()->title('Some languages could not be deleted.')->send();
                                Language::bustSushiCache();
                            }
                        }),
                ]),
            ])
            ->paginated([200, 'All'])
            ->defaultPaginationPageOption(200);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getWidgets(): array
    {
        return [
            LanguageOverview::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListLanguages::route('/'),
            'create' => Pages\CreateLanguage::route('/create'),
            'edit'   => Pages\EditLanguage::route('/{record}/edit'),
        ];
    }
}
