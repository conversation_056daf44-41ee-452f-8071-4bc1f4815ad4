<?php

namespace App\Filament\Admin\Resources\RedirectionUrlResource\Pages;

use App\Filament\Admin\Resources\RedirectionUrlResource;
use App\Services\CmsService;
use App\Services\HtAccessService;
use Filament\Actions\Action;
use Filament\Resources\Pages\ManageRecords;

class ManageRedirectionUrls extends ManageRecords
{
    protected static string $resource = RedirectionUrlResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('Download htaccess')
            ->label('Download .htaccess')
            ->action(function () {
                $cmsService = app(HtAccessService::class);
                $htaccessContent = $cmsService->generateHtaccessTxt();
                return response()->streamDownload(function () use ($htaccessContent) {
                    echo $htaccessContent;
                }, '.htaccess');
            })
            ->icon('heroicon-o-arrow-down-tray')
            ->requiresConfirmation(),
        ];
    }
}
