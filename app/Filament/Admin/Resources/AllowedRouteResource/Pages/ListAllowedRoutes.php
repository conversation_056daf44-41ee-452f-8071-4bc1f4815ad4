<?php

namespace App\Filament\Admin\Resources\AllowedRouteResource\Pages;

use App\Filament\Admin\Resources\AllowedRouteResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAllowedRoutes extends ListRecords
{
    protected static string $resource = AllowedRouteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
