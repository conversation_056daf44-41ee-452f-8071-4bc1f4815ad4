<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UserResource\RelationManagers\OrdersRelationManager;
use App\Filament\Admin\Resources\UserResource\RelationManagers\SubscriptionsRelationManager;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?int $navigationSort = 1;

    public static function getNavigationGroup(): ?string
    {
         return t('core.app_management');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()->schema([
                    Forms\Components\TextInput::make('first_name')
                        ->label(t('core.user.first_name'))
                        ->required()
                        ->maxLength(255),

                    Forms\Components\TextInput::make('last_name')
                        ->label(t('core.user.last_name'))
                        ->required()
                        ->maxLength(255),

                    Forms\Components\TextInput::make('public_name')
                        ->label(t('core.user.public_name'))
                        ->required()
                        ->nullable()
                        ->helperText(t('core.user.public_name_helper_text'))
                        ->maxLength(255),

                    Forms\Components\TextInput::make('email')
                        ->label(t('core.user.email'))
                        ->email()
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('password')
                        ->label(t('core.user.password'))
                        ->password()
                        ->revealable()
                        ->dehydrateStateUsing(fn ($state) => Hash::make($state))
                        ->dehydrated(fn ($state) => filled($state))
                        ->required(fn (string $context): bool => $context === 'create')
                        ->helperText(fn (string $context): string => ($context !== 'create') ? t('core.user.password_helper_text') : '')
                        ->maxLength(255),
                    Forms\Components\RichEditor::make('notes')
                        ->label(t('core.user.notes'))
                        ->nullable()
                        ->helperText(t('core.user.note_helper_text')),
                    Forms\Components\Select::make('roles')
                        ->label(t('core.user.roles'))
                        ->multiple()
                        ->relationship('roles', 'name')
                        ->preload()
                        ->saveRelationshipsUsing(function (User $record, array $state) {
                            $roleNames = Role::whereIn('id', $state)->pluck('name')->all();
                            $record->syncRoles($roleNames);
                            $isAdmin = collect($roleNames)->contains(fn ($role) => strtolower($role) === 'admin');
                            $record->is_admin = $isAdmin;
                        }),
                    Forms\Components\Checkbox::make('is_blocked')
                        ->label(t('core.user.is_blocked'))
                        ->disabled(fn (User $user, string $context): bool => $user->is_admin == true || $context === 'create')
                        ->helperText(t('core.user.is_blocked_helper_text'))
                        ->default(false),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('full_name')
                    ->label(t('core.user.full_name'))
                    ->sortable(
                        query: fn ($query, $direction) =>
                            $query->orderBy('last_name', $direction)
                                  ->orderBy('first_name', $direction)
                    )
                    ->searchable(['first_name', 'last_name']),
                Tables\Columns\TextColumn::make('email')->searchable()->sortable(),
                Tables\Columns\IconColumn::make('email_verified_at')
                    ->label(t('core.user.verified_email'))
                    ->getStateUsing(fn (User $user) => $user->email_verified_at ? true : false)
                    ->boolean(),
                Tables\Columns\TextColumn::make('last_seen_at')
                    ->label(t('core.user.last_seen_at'))
                    ->sortable()
                    ->dateTime(config('app.datetime_format')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime(config('app.datetime_format')),
                Tables\Columns\TextColumn::make('created_at')
                    ->sortable()
                    ->dateTime(config('app.datetime_format')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Impersonate::make()->redirectTo(route('home-lang', ['lang' => app()->getLocale()])),
                Tables\Actions\Action::make('resend_verification_email')
                    ->iconButton()
                    ->label(t('core.user.resend_verification_email'))
                    ->icon('heroicon-s-envelope-open')
                    ->requiresConfirmation()
                    ->action(function (User $record) {
                        $record->sendEmailVerificationNotification();

                        Notification::make()
                            ->success()
                            ->body(__('A verification link has been queued to be sent to this user.'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ])->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            OrdersRelationManager::class,
            SubscriptionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Admin\Resources\UserResource\Pages\ListUsers::route('/'),
            'create' => \App\Filament\Admin\Resources\UserResource\Pages\CreateUser::route('/create'),
            'edit' => \App\Filament\Admin\Resources\UserResource\Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }

    public static function getNavigationLabel(): string
    {
        return __('Users');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['first_name', 'last_name', 'email'];
    }
}
