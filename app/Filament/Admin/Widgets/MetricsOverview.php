<?php

namespace App\Filament\Admin\Widgets;

use App\Services\MetricsService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Carbon;

class MetricsOverview extends BaseWidget
{
    protected static ?int $sort = 0;

    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        /** @var MetricsService $metricsService */
        $metricsService = resolve(MetricsService::class);

        $currentMrr = $metricsService->calculateMRR(now());
        $previewMrr = $metricsService->calculateMRR(Carbon::yesterday());
        $mrrDescription = '';
        $mrrIcon = '';
        $color = 'gray';

        if ($previewMrr) {
            $mrrDescription = $previewMrr == $currentMrr ? '' : ($previewMrr > $currentMrr ? t('core.decrease') : t('core.increase'));

            if (strlen($mrrDescription) > 0) {
                $mrrDescription = money(abs($currentMrr - $previewMrr), config('app.default_currency')).' '.$mrrDescription;
                $mrrIcon = $previewMrr > $currentMrr ? 'heroicon-m-arrow-down' : 'heroicon-m-arrow-up';
                $color = $previewMrr > $currentMrr ? 'danger' : 'success';
            }
        }

        return [
            Stat::make(
                t('core.mrr'),
                money($currentMrr, config('app.default_currency'))
            )->description($mrrDescription)
                ->descriptionIcon($mrrIcon)
                ->color($color)
                ->chart([7, 2, 10, 3, 15, 4, 17])  // just for decoration :)
            ,
            Stat::make(
                t('core.active_subscriptions'),
                $metricsService->getActiveSubscriptions()
            ),
            Stat::make(
                t('core.total_revenue'),
                $metricsService->getTotalRevenue()
            ),
            Stat::make(
                t('core.total_user_subscription_conversion'),
                $metricsService->getTotalCustomerConversion()
            )->description(t('core.subscribed_total_users')),
            Stat::make(
                t('core.total_transactions'),
                $metricsService->getTotalTransactions()
            ),

            Stat::make(
                t('core.total_users'),
                $metricsService->getTotalUsers()
            ),
        ];
    }
}
