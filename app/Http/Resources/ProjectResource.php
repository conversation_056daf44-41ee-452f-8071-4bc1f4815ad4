<?php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // full data payload
        $data = [
            'id' => $this->id,
            'project_name' => $this->project_name,
            'project_info' => $this->project_info,
            'project_type' => $this->project_type,
            'location_type' => $this->location_type,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'street_number' => $this->street_number,
            'street' => $this->street,
            'city' => $this->city,
            'country_id' => $this->country_id,
            'subscription_id' => $this->subscription_id,
            'created_by' => $this->created_by,
            'last_user_to_interact' => $this->last_user_to_interact,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'items' => ProjectItemResource::collection(
            $this->whenLoaded('items')
        ),
        ];

        // handle ?columns=project_name,project_type
        $requested = $request->query('columns');
        if ($requested) {
            $fields = array_map('trim', explode(',', $requested));
            return array_intersect_key($data, array_flip($fields));
        }

        return $data;
    }
}
