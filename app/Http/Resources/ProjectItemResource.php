<?php
namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProjectItemResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'project_id' => $this->project_id,
            'item_name' => $this->item_name,
            'item_info' => $this->item_info,
            'item_type' => $this->item_type,
            'created_by' => $this->created_by,
            'last_user_to_interact' => $this->last_user_to_interact,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
