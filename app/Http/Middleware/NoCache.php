<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class NoCache
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // // All file non mis en cache
        // $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        // $response->headers->set('Pragma', 'no-cache');
        // $response->headers->set('Expires', '0');

        // // Mettre en cache seulement les fichiers css et js
        // $path = $request->getPathInfo();
        // $mime = $request->getMimeType($path);
        // if (
        //     !str_ends_with($path, '.js') &&
        //     !str_ends_with($path, '.css') &&
        //     !in_array($mime, ['application/javascript', 'text/css'])
        // ) {
        //     $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        //     $response->headers->set('Pragma', 'no-cache');
        //     $response->headers->set('Expires', '0');
        // }

        // // Mettre en cache suelement les fichiers css, js et images
        $path = $request->getPathInfo();
        $mime = $request->getMimeType($path);
        $allowedExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'];
        $allowedMimeTypes = ['application/javascript','text/css','image/png','image/jpeg','image/gif','image/webp','image/svg+xml',];
        $isAllowed = false;
        foreach ($allowedExtensions as $ext) {
            if (str_ends_with($path, $ext)) {
                $isAllowed = true;
                break;
            }
        }
        if (!$isAllowed && !in_array($mime, $allowedMimeTypes)) {
            $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
            $response->headers->set('Pragma', 'no-cache');
            $response->headers->set('Expires', '0');
        }

        // //Fichier js non mis en cache
        // if ($request->is('*.js') || $request->is('js/*.js') || $request->getMimeType($request->getPathInfo()) === 'application/javascript') {
        //     $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        //     $response->headers->set('Pragma', 'no-cache');
        //     $response->headers->set('Expires', '0');
        // }

        // $buildPath = storage_path('framework/build.flag');
        // if (!file_exists($buildPath)) {
        //     return $response;
        // }

        // $buildVersion = trim(file_get_contents($buildPath));
        // $clientVersion = $request->cookie('client_build');

        // if ($clientVersion !== $buildVersion) {
        //     $response->headers->set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0');
        //     $response->headers->set('Pragma', 'no-cache');
        //     $response->headers->set('Expires', '0');
        // }

        return $response;
    }
}
