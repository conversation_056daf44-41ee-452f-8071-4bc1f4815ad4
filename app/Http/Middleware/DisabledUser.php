<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DisabledUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user()) {

            if (($request->user()->is_blocked || $request->user()->isDeleted())) {
                auth()->logout();
                return redirect()->route('login')->with('error', __('Your account has been disabled.'));
            }

            if (!$request->user()->hasVerifiedEmail()) {
                auth()->logout();
                return redirect()->route('login')->with('error', __("La vérification de l'e-mail n'est pas terminée, veuillez vérifier votre e-mail pour continuer"));
            }
        }

        return $next($request);
    }
}
