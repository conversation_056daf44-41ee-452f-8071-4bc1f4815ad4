<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UpdateUserLastSeenAt
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            /** @var User $user */
            $user = Auth::user();

            if ($user->last_seen_at === null || now()->diffInMinutes($user->last_seen_at) >= 10) { // not to overload the database
                // defer(function () use ($user) {
                    $user->last_seen_at = now();
                    $user->save();
                // });
            }
        }

        return $next($request);
    }
}
