<?php

namespace App\Http\Middleware;

use App\Services\RouteCollectorService;
use App\Http\Controllers\CmsController;
use App\Services\TranslationApiService;
use Closure;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\App;
class TranslationRoutingMiddleware
{
  protected $routeCollectorService;
  protected $translationApiService;
  protected $cmsController;

  public function __construct(
    RouteCollectorService $routeCollectorService,
    TranslationApiService $translationApiService,
    CmsController $cmsController,
  ) {
    $this->routeCollectorService = $routeCollectorService;
    $this->translationApiService = $translationApiService;
    $this->cmsController = $cmsController;
  }
  public function handle(Request $request, Closure $next)
  {
    $uri = urldecode($request->getPathInfo());
    $lang = $request->route('lang');

    if (strpos($uri, ' ') !== false) {
      $newUri = str_replace(' ', '-', $uri);
      return Redirect::to($newUri, 301);
    }

    $lang = $this->translationApiService->useIpBasedLanguage($lang, $request);

    if (is_array($lang) && isset($lang["url"], $lang["statusCode"])) {
      return Redirect::to($lang["url"], $lang["statusCode"]);
    }



    View::share('language', $lang);
    $request->attributes->set('language', $lang);

    $translatedRoute = $this->translationApiService->translateUri($uri, $lang);

    if ($translatedRoute) {
      if (trim($uri) !== trim($translatedRoute["path"])) {
        $query = $request->getQueryString();
        $redirectUrl = urldecode($translatedRoute["path"]) . ($query ? '?' . $query : '');
        return Redirect::to($redirectUrl, 301);
      }

      $cmsUrl = trim($translatedRoute["cmsUrl"] ?? '');

      if ($cmsUrl) {
        View::share('cmsUrl', $cmsUrl);
        $request->attributes->set('cmsKey', $cmsUrl);

        $cmsResponse = $this->cmsController->handleRequestCms($request, [
          "cmsUrl" => $cmsUrl,
          "lang" => $lang
        ]);

        if ($cmsResponse && isset($cmsResponse["status"]) && $cmsResponse["status"] !== '1') {
            return Redirect::to('/coming-soon', 301);
        }

        if (is_array($cmsResponse) && isset($cmsResponse["hybrideCmsKey"])) {
          $cmsKey = $cmsResponse["hybrideCmsKey"];
          View::share('cmsKey', $cmsKey);
          $request->attributes->set('cmsKey', $cmsKey);

          try {
            $locationHead = $cmsResponse["url"];
            $args = array_merge(
              $request->route()?->parameters() ?? [],
              $translatedRoute['args'] ?? [],
              [
                'cms_url' => $cmsUrl,
                'cms_key' => $cmsKey
              ]
            );

            return $this->loadContent($locationHead, $request, $args);
          } catch (\Throwable $th) {
            return Redirect::to('/not-found', 301);
          }
        }

        return response($cmsResponse);
      }
    }

    return Redirect::to('/not-found', 301);
  }

  protected function loadContent(string $path, Request $request, array $args)
  {
    $routeInfo = $this->routeCollectorService->getRouteControllerByPath($path);
    $controller = App::make($routeInfo['controller']);
    $method = $routeInfo['method'];

    $result = App::call([$controller, $method], array_merge(['request' => $request], $args));

    if ($result instanceof \Illuminate\Contracts\View\View) {
        return response($result);
    }

    if (is_string($result) || is_array($result)) {
        return response($result);
    }

    return $result;
  }
}
