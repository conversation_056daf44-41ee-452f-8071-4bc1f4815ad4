<?php

namespace App\Http\Controllers;

use App\Services\LanguageService;
use Illuminate\Support\Facades\Log;

class LanguageController extends Controller
{
    protected LanguageService $languageService;

    public function __construct(LanguageService $languageService)
    {
        $this->languageService = $languageService;
    }

    public function setLanguage($locale)
    {
        Log::debug("Setting language to mandalo ato ve: $locale");
        return $this->languageService->setLanguage($locale);
    }
}
