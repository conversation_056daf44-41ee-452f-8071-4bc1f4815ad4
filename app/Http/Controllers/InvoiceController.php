<?php

namespace App\Http\Controllers;

use App\Constants\PaymentProviderConstants;
use App\Models\Receipt;
use App\Models\Transaction;
use App\Models\User;
use App\Services\CalculationService;
use App\Services\InvoiceService;
use App\Services\PaymentProviders\PaymentService;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Illuminate\Http\Request;
use LaravelDaily\Invoices\Classes\Buyer;
use LaravelDaily\Invoices\Classes\InvoiceItem;
use LaravelDaily\Invoices\Classes\Seller;
use LaravelDaily\Invoices\Invoice;

class InvoiceController extends Controller
{
    public function __construct(
        private InvoiceService $invoiceService,
        private CalculationService $calculationService,
        private PaymentService $paymentService
    ) {}
    public function getInvoiceCustomerName($customerJson) {
        return $customerJson["name"];
    }
    public function generateCustomerInfoPdfVars($customerJson){
        $addressLines = [
            $customerJson['address']['address_line_1'] ?? '',
            $customerJson['address']['address_line_2'] ?? '',
        ];
        $addressLines = array_filter($addressLines, function($line) {
            return strlen($line) > 0;
        });
        $addressStr = implode(', ', $addressLines);
        $customerInfo =  [
            "customerName" => $this->getInvoiceCustomerName($customerJson),
            "postalCode" => $customerJson['address']['zip'] ?? '',
            "city" =>  $customerJson['address']['city'] ?? '',
            "country" => isset($customerJson['address']['country']) ?   $customerJson['address']['country']['name'] : '',
            "streetAddress" => $addressStr ?? '',
            "mobileNumber" => $customerJson['address']['phone'] ?? '',
            "email" => $customerJson['email'] ?? '',
        ];
        return $customerInfo;
    }
    public function fetchStripeObject($stripeSubscriptionId) {
        /** @var StripeProvider $paymentProviderStrategy */
        $paymentProviderStrategy = $this->paymentService->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        return $paymentProviderStrategy->fetchStripeObject($stripeSubscriptionId);
    }
    public function getStripeMetadata($stripeObject){
        if($stripeObject === null) {
            return [];
        }
        if($stripeObject->object === 'invoice'){
            return $stripeObject->lines->data[0]->metadata ?? [];
        }
        if($stripeObject->object === 'checkout.session'){
            return $stripeObject->metadata ?? [];
        }
        return $stripeObject->metadata ?? [];
    }
    public function getStripeReference($docType, $transaction, $receipt){
        $stripeReference = null;
        if($docType === 'receipt') {
            $stripeReference = $receipt->payment_provider_object_id;
        }else {
            $stripeReference = $transaction->payment_provider_reference ?? $transaction->payment_provider_transaction_id;
        }

        return  $stripeReference;

    }
    public function generatePdfVars(User $user, string $docType, Transaction $transaction, ?Receipt $receipt){


        $cdnUrl = config('services.cdn.url');
        $translationUrl = config('services.translation_api.url');


        $language = $user->settings["base_language"] ?? 'en';
        $customerJson = $transaction->customer_json;
        $planJson = $transaction->cart->plan_json;
        $subscriptionJson = $transaction->subscription_json;
        $customerInfo = $this->generateCustomerInfoPdfVars($customerJson);
        $stripeObject = $this->fetchStripeObject($transaction->payment_provider_transaction_id);
        $stripeMetadata = $this->getStripeMetadata($stripeObject);
        $planPriceAmount = $this->calculationService->getPlanPriceFromArray($transaction->cart->plan_json)['price'];


        $accounting = json_decode($stripeMetadata['accountingJsonStr'] ?? 'null', true) ?? [
                        'amountToPay' => $transaction->amount,
                        'amountToRefund' => 0,
                        'total' => $transaction->amount,
        ];
        if($receipt){
            $accounting = array_merge($accounting, [
                "amountAlreadyPaid" => $receipt->amount_already_paid ?? 0,
                "remainingToPay" => $receipt->remaining_to_pay ?? 0,
                "amountPaid" =>  $receipt->amount_paid ?? 0,
            ]);
        }
        $templateVars =  [
                "currencyCode" => $transaction->currency->code,
                "docType" => $docType,
                "language" => $language,
                "customerInfo" => $customerInfo,
                "payment" => [
                    "accounting" => $accounting,
                    "expectedPaymentDate" => $transaction->created_at->format('Y-m-d'),
                    "effectivePaymentDate" => $receipt ? $receipt->created_at->format('Y-m-d'): null,
                    "paymentMethod" => "Stripe",
                    "stripeReference" => $this->getStripeReference($docType, $transaction, $receipt),
                    "invoiceNumber" =>  $transaction->invoice_number,
                    "receiptNumber" => $receipt ? $receipt->receipt_number : null,
                    "subtotal" => $planPriceAmount,
                    "total" => $transaction->amount,
                    "amountPaid" => $transaction->amount,
                ],
                "product" => [
                    [
                        "name" => $planJson['product']['name'],
                        "quantity" => 1,
                        "billingPeriodIntervalAdv" => $planJson['interval']['adverb'],
                        "unitPrice" => $planPriceAmount,
                        "price" => $planPriceAmount,

                        "startedDate" => $subscriptionJson ? $subscriptionJson['ends_at']: null,
                        "expiredDate" =>$subscriptionJson ? $subscriptionJson['starts_at']: null,
                        "userCount" => $planJson['product']['user_count'] ?? 1,
                        "creditCount" => $planJson['product']['credit_count'],
                        "productFeatures" => $planJson['product']['features'],

                    ],
                ],
            ];
        $data = [
            "templateVars" => $templateVars,
            "env" => [
                "TRANSLATION_API_URL" => $translationUrl,
                "CDN_URL" =>  $cdnUrl,
            ],
            "settings" => [
                "backgroundImageUrl" => str_replace('{{CDN_URL}}',  $cdnUrl, config('invoices_receipts.background_image_url')),
                "colorTheme" => json_decode(config('invoices_receipts.color_theme'), true),
                "companyData" => json_decode(config('invoices_receipts.company_data'), true),
                "logoUrl" => str_replace('{{CDN_URL}}', $cdnUrl, config('invoices_receipts.logo_url')),
                "appName" => config('app.name')
            ]
        ];
        return $data;
    }
    public function generatePdf(string $docType, string $uuid){
        $transaction = null;
        $receipt = null;
        if($docType == "receipt") {
            $receipt = Receipt::where('uuid', $uuid)->firstOrFail();
            $transaction = Transaction::with(['cart'])->where('id', $receipt->transaction_id)->firstOrFail();
        }else {
            $transaction = Transaction::with(['cart'])->where('uuid', $uuid)->firstOrFail();
        }


        $user = auth()->user();
        if(!$user || ($user->id !== $transaction->user_id && !$user->isAdmin())){
            abort(403);
        }

        $pdfVars = $this->generatePdfVars($user, $docType, $transaction, $receipt);

        // Create Gotenberg client
        $client = Gotenberg::chromium(config('services.gotenberg.url'));




        // Build HTMLRequest
        $outputFilename =  ($docType == "receipt" ? 'receipt_' : 'invoice_') . $transaction->uuid;
        $request = $client->pdf()->outputFilename($outputFilename)->margins('10mm', '10mm', '10mm', '10mm');
        $pages = [

                'pdf.accounting.partials.fonts' => $pdfVars,
                'pdf.accounting.partials.style' => $pdfVars,
                'pdf.accounting.'.$docType => $pdfVars,

        ];

        $request->skipNetworkIdleEvent(false);
        // CONSTRUCT HTML
        $html = '';
        foreach ($pages as $blade => $pageData) {
            $html .= view($blade, $pageData)->render();
        }

        // Save the html
        // file_put_contents(storage_path('tmp/indexx.html'), $html);

        $request = $request->html(Stream::string('index.html', $html));
        $response = Gotenberg::send($request);
        if ($response->getStatusCode() === 200) {
            $fileContent = $response->getBody()->getContents();
            return response($fileContent)
                ->header('Content-Type', 'application/pdf')  // Set the MIME type for PDF
                ->header('Content-Disposition', 'attachment; filename="' . $outputFilename . '.pdf"') ;
        }
        return response()->json(['error' => 'Failed to generate PDF'], 500);
    }

    public function generate(string $transactionUuid)
    {
        $transaction = Transaction::where('uuid', $transactionUuid)->firstOrFail();

        $forceRegenerate = request()->boolean('regenerate', false) && auth()->user()->isAdmin();


        $result = $this->invoiceService->generate($transaction, $forceRegenerate);

        if ($result === null) {
            abort(404);
        }

        return $result;
    }

    /**
     * Preview invoice (used to generate PDF and show it from admin panel)
     */
    public function preview(Request $request)
    {
        if (! auth()->user()->isAdmin()) {
            abort(403);
        }

        $customer = new Buyer([
            'name' => 'John Doe',
            'custom_fields' => [
                'email' => '<EMAIL>',
                'order number' => '1234',
            ],
        ]);

        $item = InvoiceItem::make('Product 1')->formattedPricePerUnit('$10');

        $seller = new Seller;
        $seller->name = $request->get('seller_name', '');
        $seller->address = $request->get('seller_address', '');
        $seller->code = $request->get('seller_code', '');
        $seller->vat = $request->get('seller_tax_number', '');
        $seller->phone = $request->get('seller_phone', '');

        $invoice = Invoice::make()
            ->buyer($customer)
            ->seller($seller)
            ->series($request->get('serial_number_series') ?? '')
            ->formattedTotalTaxes('$1.99')
            ->formattedTotalAmount('$11.99')
            ->logo(public_path(config('app.logo.1')))
            ->addItem($item);

        return $invoice->stream();
    }
}
