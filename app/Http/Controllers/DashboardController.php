<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectItem;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    private SubscriptionService $subscriptionService;
    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }
    private function getSubscription() {
        $user = auth()->user();
        $id = $user->is_sub_user ? $user->owner_id : $user->id;
        $subscriptionData = $this->subscriptionService->findCurrentActiveUserSubscription($id);
        $subscription = null;
        if ($subscriptionData) {
            $planJson = $subscriptionData->last_cart->plan_json;

            $totalProjects = Project::where('created_by', $user->id)
                ->orWhere('created_by', $user->owner_id)
                ->count();

            $projectsThisMonth = Project::where('created_by', $user->id)
                ->orWhere('created_by', $user->owner_id)
                ->whereYear('created_at', now()->year)
                ->whereMonth('created_at', now()->month)
                ->count();

            $periode = $planJson['period'] ?? 'monthly';
            $productCredit = $planJson['credit'] ?? 0;
            $subscription = [
                'id' => $subscriptionData->id,
                'plan' => $planJson['name'],
                'users' => $planJson['user_count'],
                'since' => $subscriptionData['starts_at'],
                'periode' => $periode,
                //Yearly credits
                'creditsYear' => $periode != 'monthly' ? $productCredit : 0,
                'projectsTotal' => $totalProjects,
                'balance' => $subscriptionData['credit_balance'],

                //Monthly credits
                'creditsMonth' => $periode != 'monthly' ? floor($productCredit / 12) : $productCredit,
                'credits' => $planJson['credit'],
                'periode' => $planJson['period'],
                'projectsMonth' => $projectsThisMonth,
                'balanceMonth' => ( $periode != 'monthly' ? floor($productCredit / 12) : $productCredit ) -  $projectsThisMonth,
            ];
        }
        return $subscription;
    }
    public function index(
        Request $request
    ) {
        $user = auth()->user();
        $styles = ['resources/sass/components/dashboard.scss'];

        // $totalProjects = Project::where('created_by', $user->id)->count();

        // $projectsThisMonth = Project::where('created_by', $user->id)
        //     ->whereYear('created_at', now()->year)
        //     ->whereMonth('created_at', now()->month)
        //     ->count();


        $subscription = $this->getSubscription();


        return view('dashboard.index', [
            'styles' => $styles,
            'subscription' => $subscription,
            'user' => $user,
        ]);
    }

    public function createProject(
        Request $request
    ) {

        // $user = auth()->user();
        $subscription = $this->getSubscription();

        $styles = ['resources/sass/components/project-info.scss'];

        return view('dashboard.project-info', [
            'isInfo' => 'false',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function createSimulation(Request $request, $id)
    {
        $subscription = $this->getSubscription();
        $project = Project::find($id);

        if (!$project || $project->created_by !== auth()->id()) {
            abort(404, 'Project not found or you do not have access to this project.');
        }

        $styles = ['resources/sass/components/simulation.scss'];

        return view('dashboard.simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
            'project' => $project,
        ]);
    }

    public function simulationInfo(Request $request, $id, $simId)
    {
        $subscription = $this->getSubscription();
        $project = Project::find($id);
        $simulation = ProjectItem::find($simId);

        if (!$project || $project->created_by !== auth()->id()) {
            abort(404, 'Project not found or you do not have access to this project.');
        }

        if (!$simulation || $simulation?->project_id !== $project->id) {
            abort(404, 'Simulation not found in this project.');
        }

        $styles = ['resources/sass/components/simulation.scss'];

        return view('dashboard.simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
            'project' => $project,
            'simulation' => $simulation,
        ]);
    }

    public function pdfSimulation(Request $request)
    {
        $subscription = $this->getSubscription();;

        $styles = ['resources/sass/components/simulation.scss', 'resources/sass/components/pdf_editor.scss'];

        return view('dashboard.pdf-simulation', [
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

    public function projectInfo(Request $request, $id)
    {
        $project = Project::findOrFail($id);

        $subscription = $this->getSubscription();;

        $styles = ['resources/sass/components/project-info.scss'];

        if (!$project || $project->created_by !== auth()->id()) {
            abort(404, 'Project not found or you do not have access to this project.');
        }

        return view('dashboard.project-info', [
            'project' => $project,
            'isInfo' => 'true',
            'styles' => $styles,
            'subscription' => $subscription,
        ]);
    }

}
