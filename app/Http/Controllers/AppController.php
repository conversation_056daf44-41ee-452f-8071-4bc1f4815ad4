<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AppModel;

class AppController extends Controller
{
    public function getAppByKey(Request $request, $appKey)
    {

        $app = AppModel::where('appKey', $appKey)->first();

        if (!$app) {
            return response()->json([
                'success' => false,
                'message' => 'App not found with the provided app key'
            ], 404);
        }

        return response()->json($app);
    }

    public function getAppList(Request $request)
    {
        $apps = AppModel::all();

        if ($apps->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No apps found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $apps
        ]);
    }


}
