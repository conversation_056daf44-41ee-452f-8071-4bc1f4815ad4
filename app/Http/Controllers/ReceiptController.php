<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\InvoiceService;
use App\Services\ReceiptService;
use Illuminate\Http\Request;

class ReceiptController extends Controller
{
    public function __construct(protected ReceiptService $receiptService)
    {
    }

    public function generate(string $transactionUuid)
    {
        $transaction = Transaction::where('uuid', $transactionUuid)->firstOrFail(); 
        $forceRegenerate = request()->boolean('regenerate', false) && auth()->user()->isAdmin(); 
        $result = $this->receiptService->generate($transaction, $forceRegenerate);

        if ($result === null) {
            abort(404);
        }

        return $result;
    }



}
