<?php

namespace App\Http\Controllers;

use App\Services\CmsService;
use App\Services\LanguageService;
use App\Services\TranslationApiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Redirect;

class CmsController extends Controller
{
    protected $cmsService;
    protected LanguageService $languageService;
    protected TranslationApiService $translationApiService;

    public function __construct()
    {
        $this->cmsService = app(CmsService::class);
        $this->languageService = app(LanguageService::class);
        $this->translationApiService = app(TranslationApiService::class);
    }

    public function handleRequestCms(Request $request, $args)
    {
        $cmsUrl = $args["cmsUrl"];
        $lang = $args["lang"];
        $this->languageService->setLanguage($lang);
        $cmsKeyUrl = $this->cmsService->getContentManagementSystemUrlKey();
        $cmsKey = $cmsKeyUrl[$cmsUrl] ?? null;
        $cmsData = [];
        if ($cmsKey) {
            // check if is hybrid-cms
            $hybridCmsParams = [
                "where"  => json_encode([
                    "key" => [
                        "operator" => "==",
                        "value" => $cmsKey
                    ],
                    "type" => [
                        "operator" => "==",
                        "value" => "hybrid-cms"
                    ],
                ]),
                "fields" => '["id", "key"]',
            ];

            $hybridCmsResponse = $this->cmsService->getCmsV4($hybridCmsParams);

            if (isset($hybridCmsResponse["data"][0]["key"]) && !empty($hybridCmsResponse["data"][0]["key"])) {
                return [
                    "hybrideCmsKey" => $cmsKey,
                    "url" => $cmsUrl
                ];
            }

            $cmsData = $this->cmsService->getContentByKey($cmsKey, $lang);
        }

        if ($cmsData && $cmsData['deletedAt'] === null) {
            return view('cms.content', [
                'title' => $cmsData['title'] ?? '',
                'content' => $cmsData['content'] ?? '',
                'style' => $cmsData['style'] ?? '',
                'metaDescription' => $cmsData['metaDescription'] ?? '',
                'status' => $cmsData['status'] ?? '0',
            ]);
        } else {
            return view('errors.404');
        }
    }

    /**
     * EXAMPLE HYBRID CMS
     */
    public function controlPage(Request $request) {
        $language = $request->attributes->get('language');
        $cmsKey = $request->attributes->get('cmsKey');

        $cmsData = $this->cmsService->getContentByKey($cmsKey, $language);
        $cmsContent = parseTemplate($cmsData['content'], [ 'testimony_section' => 'Load content of testimony and remplace placeholder']);
        return view('cms.content', [
            'title' => $cmsData['title'] ?? '',
            'content' => $cmsContent ?? '',
            'style' => $cmsData['style'] ?? '',
            'metaDescription' => $cmsData['metaDescription'] ?? '',
        ]);
    }

    public function showHomepage(Request $request, string $lang)
    {
        App::setLocale($lang);

        $lang = $this->translationApiService->useIpBasedLanguage($lang, $request);

        if (is_array($lang) && isset($lang["url"], $lang["statusCode"])) {
            return Redirect::to($lang["url"], $lang["statusCode"]);
        }

        $pvgisApi = config('services.pvgis.api');
        $deviationThresholdGreen = Config::get(
            'deviation_threshold_green_percent',
        ) ?? 5;
        $deviationThresholdOrange = Config::get(
            'deviation_threshold_orange_percent',
        ) ?? 10;

        return view('home', [
            'pvgisApi' => $pvgisApi,
            'deviationThresholdGreen' => $deviationThresholdGreen,
            'deviationThresholdOrange' => $deviationThresholdOrange,
        ]);
    }
}
