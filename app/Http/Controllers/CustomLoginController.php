<?php

namespace App\Http\Controllers;

use App\Models\AppModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

class CustomLoginController extends Controller
{

    private function authenticate($user) {
        $token = $user->createToken('login', ['*']); 

        $companyId = isset($user->company_id) ? $user->company_id : null;

        if(!$user->is_admin && !$companyId ) {
            return response()->json(['message' => 'Forbiden'], 403);
        }

        $user["username"] = $user["email"];
        $user["lastname"] = $user["name"];
        
        $result = [
            'id' => $user->id,
            'token' => $token->plainTextToken, 
            'token_type' => 'Bearer',
            'user' => $user,
            'sci' => $companyId,
            'role' => isset($user->role) ? $user->role->name : ''
        ];

        $appName = '';
        if($companyId) {
            $appName = 'PsmSupplier';
            $result['role'] = 'supplier';
        } elseif($user->is_admin) {
            $appName = 'PsmAdmin';
            $result['role'] = 'admin';
        }

        $app = AppModel::where('name', $appName)->first();
        $result['appKey'] = $app ? $app->appKey : '';
        
        return $result; 
    }

    public function autoLogin(Request $request) {
        if (!auth()->check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $user = auth()->user();

        $credentials = $this->authenticate($user); 
        $encoded = base64_encode(json_encode($credentials));

        return response()->json([
            'url' => config('services.product_supplier.ui_url').'/auth/auto-login?token='.$encoded,
        ]);
    }

    public function checkToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        $plainTextToken = $request->input('token');

        if (strpos($plainTextToken, '|') === false) {
            return response()->json(['valid' => false, 'message' => 'auth.token_invalid_format'], 400);
        }

        [$id, $tokenPart] = explode('|', $plainTextToken, 2);

        $tokenRecord = PersonalAccessToken::find($id);

        if (! $tokenRecord) {
            return response()->json(['valid' => false, 'message' => 'auth.token_not_found'], 404);
        }

        $hash = hash('sha256', $tokenPart);

        if (hash_equals($tokenRecord->token, $hash)) {
            return response()->json(['valid' => true, 'message' => 'auth.token_valid']);
        }

        return response()->json(['valid' => false, 'message' => 'auth.token_invalid'], 401);
    }

    public function logout(Request $request) {
        // delete the current access token
        $currentToken = $request->user()->currentAccessToken();
        if($currentToken) {
            $currentToken->delete();
            return response()->json([
                'message' => 'auth.logout_successful',
            ]);
        } else {
            return response()->json([
                'message' => 'auth.token_not_found'
            ], 404);
        }
    }
}
