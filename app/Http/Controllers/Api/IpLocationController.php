<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\ExampleNotification;
use App\Services\IpLocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

use function PHPSTORM_META\type;

class IpLocationController extends Controller
{
  protected IpLocationService $ipLocationService;

  public function __construct()
  {
    $this->ipLocationService = app(IpLocationService::class);
  }

  public function getLocationByIp($ip)
  {
    $data = $this->ipLocationService->getLocationByIpViaData($ip);
    return response()->json($data);
  }
}
