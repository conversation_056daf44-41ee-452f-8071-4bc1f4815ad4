<?php
namespace App\Http\Controllers\Api;

use App\Events\GlobalNotificationEvent;
use App\Http\Controllers\Controller;
use App\Jobs\SendUserRealtimeNotification;
use App\Models\User;
use App\Notifications\ExampleNotification;
use App\Notifications\Notify;
use function PHPSTORM_META\type;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;

class NotificationController extends Controller
{
    public function send(Request $request)
    {
        
        $data = $request->validate([
            'type'    => 'required|string|max:500',
            'id' => 'required|numeric',
            'body' => 'required|string|max:500',
            'title' => 'required|string|max:500'
        ]);

        $data['title'] = 'Subscription create Successful!';
        $data['body'] = 'Congratulations! Your subscription {{subscriptionName}} has been successfully created . Started on {{startedDate}} to {{expiredDate}} <div> <a href="/pvgis24/app/{{customerId}}/subscription">Click here</a> </div>';

        $userToNotify = User::find($data['id']);

        // return response()->json([
        //     'id' => $data['id'],
        //     'sent_to' => $userToNotify,
        // ], 200);

        if (!$userToNotify) {
            return response()->json(['success' => false, 'message' => 'User not found'], 404);
        }
        // Then notify this single user
        // Notification::send($userToNotify, new ExampleNotification($userToNotify, $data['type'], "This is a notification form the api"));
        
        // Notification::send($userToNotify, new Notify($userToNotify, $data['type'], $data['title'], $data['body'] ?? "This is a notification form the api"));

        // sendNotification($userToNotify, $data);

        SendUserRealtimeNotification::dispatch($userToNotify, $data)->delay(now()->addSecond(2));

        // dispatch(new SendUserRealtimeNotification($userToNotify, $data));
    

        return response()->json([
            'success' => true,
            'sent_to' => $userToNotify->pluck('id'),
            "type" => $data['type']
        ], 200);
    }

    public function test(Request $request)
    {
        broadcast(new GlobalNotificationEvent("Hello! This is a global notification."));
        return response()->json([
            'success' => true,
            'message' => 'Notification event dispatched successfully.'
        ], 200);
    }

}
