<?php

namespace App\Http\Controllers;

use App\Services\CalculationService;
use App\Services\PaymentProviders\PaymentService;
use App\Services\PlanService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use App\Services\CmsService;

class SubscriptionController extends Controller
{

    public function __construct(
        private PlanService $planService,
        private CmsService $cmsService,
        private SubscriptionService $subscriptionService,
        private PaymentService $paymentService,
        private CalculationService $calculationService,
    ) {}

    public function changePlan(string $subscriptionUuid, string $newPlanSlug, Request $request)
    {
        $user = auth()->user();

        $userSubscription = $this->subscriptionService->findActiveByUserAndSubscriptionUuid($user->id, $subscriptionUuid);

        if (! $userSubscription) {
            return redirect()->back()->with('error', __('You do not have an active subscription.'));
        }

        if ($userSubscription->plan->slug === $newPlanSlug) {
            return redirect()->back()->with('error', __('You are already subscribed to this plan.'));
        }

        $paymentProvider = $userSubscription->paymentProvider()->first();

        if (! $paymentProvider) {
            return redirect()->back()->with('error', __('Error finding payment provider.'));
        }

        $paymentProviderStrategy = $this->paymentService->getPaymentProviderBySlug(
            $paymentProvider->slug
        );

        $newPlan = $this->planService->getActivePlanBySlug($newPlanSlug);

        $isProrated = config('app.payment.proration_enabled', true);
        $isDeferred = false; // TODO: setup conditional deferred | config('app.payment.', false);

        $totals = $this->calculationService->calculateNewPlanTotals(
            $user,
            $newPlanSlug,
            $isProrated,
        );

        if ($request->isMethod('post')) {
            $result = $this->subscriptionService->changePlan($userSubscription,
            $paymentProviderStrategy, $newPlanSlug, $isProrated, $isDeferred);

            if ($result) {
                return redirect()->route('subscription.change-plan.thank-you');
            } else {
                return redirect()->route('home-lang', ['lang' => app()->getLocale()])->with('error', __('Error changing plan.'));
            }
        }

        return view('subscription.change', [
            'subscription' => $userSubscription,
            'newPlan' => $newPlan,
            'isProrated' => $isProrated,
            'user' => $user,
            'totals' => $totals,
        ]);
    }

    public function success()
    {
        return view('subscription.change-thank-you');
    }

    public function page(Request $request)
    {
        $language = $request->attributes->get('language');
        $cmsKey = $request->attributes->get('cmsKey');

        $cmsData = $this->cmsService->getContentByKey($cmsKey, $language);

        $plans = $this->planService->getAllActivePlans();

        $plansOff = $this->reArrangePlan($plans);

        $productsProfessional = $plansOff['productsProfessional'];
        $productsParticular = $plansOff['productsParticular'];


        return view('subscription.home', [
            'productsProfessional' => $productsProfessional,
            'productsParticular' => $productsParticular,
            'title' => $cmsData['title'] ?? '',
            'metaDescription' => $cmsData['metaDescription'] ?? '',
        ]);
    }

    private function reArrangePlan($plans){
        $productsProfessional = $this->getDefaultProducts("Professionnel");
        $productsParticular = $this->getDefaultProducts("Particulier");

        foreach ($plans as $plan) {
            $product = $plan->product;

            $features = $product->features->map(function ($feature) {
                return [
                    'label' => $feature->name,
                    'key' => $feature->key,
                    'enabled' => $feature->pivot->enabled ?? false,
                    'ui_order' => $feature->ui_order
                ];
            })->toArray();

            $priceValue = 0;
            foreach ($plan->prices as $price) {
                if ($price->currency_id == 30 && $price->getOriginal('price')) {
                    $priceValue = $price->getOriginal('price') / 100;
                    break;
                }
            }

            $productData = [
                'name' => $plan->name,
                'price' => $priceValue,
                'slug' => $plan->slug,
                'description_translation' => $product->slug,
                'description' => $plan->description ?? $product->description,
                'dossier' => $plan->user_count ?? 1,
               'type' => $plan->interval?->name === "month" ? "Monthly" : "Yearly",
                'account_type' => $product->account_type,
                'user_count' => $plan->user_count,
                'recommande' => $product->is_popular == 1,
                'features' => $features,
            ];

            if ($product->account_type === "Professionnel") {
                $productsProfessional[] = $productData;
            } elseif ($product->account_type === "Particulier") {
                $productsParticular[] = $productData;
            }
        }
        return view('subscription.home', [
            'title' => $cmsData['title'] ?? '',
            'productsProfessional' => $productsProfessional,
            'productsParticular' => $productsParticular
        ]);
    }


    private function getDefaultFeatures(): array
    {
        return $defaultFeatures = [
            ['label' => 'Comparatif Production Réel vs Production PVGIS', 'enabled' => true, 'key' => 'prod_reel_vs_prod_pvgis'],
            ['label' => 'Simulation financière SIMPLIFIÉE', 'enabled' => true, 'key' => 'sim_financiere_simplifie'],
            ['label' => 'Profits / pertes / TRI / ROI', 'enabled' => false, 'key' => 'profit_perte_tri_roi'],
            ['label' => 'Contrôle permanent de la production solaire', 'enabled' => false, 'key' => 'control_prod_sol'],
            ['label' => 'Contrôle de l\'économie réalisée sur facture', 'enabled' => false, 'key' => 'control_eco_facture'],
            ['label' => 'Dossier de rendement et de rentabilité Impression PDF', 'enabled' => false, 'key' => 'rendrement_impression_pdf'],
            ['label' => 'Alerte maintenance Préventive', 'enabled' => false, 'key' => 'alert_prev'],
            ['label' => 'Alerte maintenance Corrective', 'enabled' => false, 'key' => 'alert_corr'],
            ['label' => 'Sauvegarde du dossier', 'enabled' => false, 'key' => 'save_folder'],
            ['label' => 'Support technique en ligne', 'enabled' => false, 'key' => 'tech_support'],
            ['label' => 'Utilisation commerciale autorisée', 'enabled' => false, 'key' => 'use_commercial'],
        ];
    }

    function getDefaultProducts(string $type): array{
        return [
            [
                'name' => 'Gratuit',
                'price' => 0,
                'description' => 'Parfait pour démarrer et découvrir SOLAR CONTROL',
                'description_translation' => '',
                'slug' => '',
                'dossier' => 1,
                'user_count' => 1,
                'type' => 'Monthly',
                'recommande' => false,
                'features' => $this->getDefaultFeatures(),
            ],
            [
                'name' => 'Gratuit',
                'price' => 0,
                'description' => 'Parfait pour démarrer et découvrir SOLAR CONTROL',
                'description_translation' => '',
                'slug' => '',
                'dossier' => 1,
                'user_count' => 1,
                'type' => 'Yearly',
                'recommande' => false,
                'features' => $this->getDefaultFeatures(),
            ]
        ];
    }


}
