<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\User;
use App\Services\IpLocationService;
use App\Services\TranslationApiService;
use App\Services\UserService;
use App\Validator\RegisterValidator;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;
    protected IpLocationService $ipLocationService;
    
    protected TranslationApiService $translationService;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    //    protected $redirectTo = '/email/verify';

    public function __construct(
        private RegisterValidator $registerValidator,
        private UserService $userService,
        TranslationApiService $translationService,
    ) {
        $this->translationService = $translationService;
        $this->middleware('guest');
        $this->ipLocationService = app(IpLocationService::class);
    }

    public function redirectPath()
    {
        return Redirect::getIntendedUrl() ?? route('home-lang', ['lang' => app()->getLocale()]);
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return $this->registerValidator->validate($data);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        $data["settings_json"] = json_encode([
            'base_language' => $data["language"],
            'base_country'  => strtoupper($data["country"]),
            'base_currency' => 'EUR',
        ]);
        return $this->userService->createUser($data);
    }

    /**
     * Show the application registration form.
     *
     * @return \Illuminate\View\View
     */
    public function showRegistrationForm()
    {
        if (url()->previous() != route('login') && Redirect::getIntendedUrl() === null) {
            Redirect::setIntendedUrl(url()->previous()); // make sure we redirect back to the page we came from
        }

        $currentCountry = null;
        $clientIp = getClientIpAddress();
        $ipInfoData = $this->ipLocationService->getLocationByIp($clientIp);
        if(isset($ipInfoData['status']) && $ipInfoData['status'] === 'success') {
            $currentCountry = strtolower($ipInfoData['countryCode']);
        }

        $languageOptions = [];
        if ($this->translationService) {
            try {
                $res = $this->translationService->getAllLanguages(['limit' => 200]);
                $languages = Arr::get($res, 'data', []);

                // Map language code => language name
                $languageOptions = collect($languages)->mapWithKeys(function ($lang) {
                    $fullLanguageName = sprintf('%s (%s)', htmlspecialchars($lang['languageFullNative']), strtoupper($lang['languageISO2'])); // Sanitize
                    $labelHtml = $fullLanguageName;
                    $code = $lang['languageISO2'] ?? null;
                    $name = $labelHtml ?? null;

                    return $code && $name ? [$code => $name] : [];
                })->toArray();
            } catch (\Throwable $e) {
                report($e);
            }
        }

        $countries = Country::query()
            ->orderBy('name')
            ->pluck('name', 'code_alpha_2')
            ->toArray();

        $languageOptionsFormatted = collect($languageOptions)
            ->map(fn($label, $value) => ['value' => strtolower($value), 'label' => $label])
            ->values()
            ->all();

        $countryOptionsFormatted = collect($countries)
            ->map(fn($label, $value) => ['value' => strtolower($value), 'label' => $label])
            ->values()
            ->all();

        return view('auth.register', [
            "currentLanguage" => strtolower(app()->getLocale() ?? "en"),
            "currentCountry" => $currentCountry,
            "countryOptions" => $countryOptionsFormatted,
            "languageOptions" => $languageOptionsFormatted,
        ]);
    }

    protected function registered(Request $request, $user)
    {
        // Log the user out to avoid auto-login
        $this->guard()->logout();

        // Return the same registration view with a flag to show the success message
        return view('auth.register', [
            'registered' => true,
            'userMail' => $user->email,
        ]);
    }
}
