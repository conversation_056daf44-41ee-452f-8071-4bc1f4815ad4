<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;

class LogoutController extends Controller
{
    public function destroy(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        $atMain = $request->getHost() == config('services.app_main.url');
        $otherUrl = $atMain ? config('app.dashboard_url') : config('app.url');

        return redirect()->away("$otherUrl/logout-session");
    }
}
