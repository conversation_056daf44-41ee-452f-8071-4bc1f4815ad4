<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\CartService;
use App\Services\LoginService;
use App\Services\LanguageService;
use App\Validator\LoginValidator;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;
    protected LanguageService $languageService;

    //    /**
    //     * Where to redirect users after login.
    //     *
    //     * @var string
    //     */
    //    protected $redirectTo = RouteServiceProvider::HOME;

    public function __construct(
        private LoginValidator $loginValidator,
        private LoginService $loginService,
        private CartService $cartService,
    ) {
        $this->middleware('guest')->except('logout');
        $this->languageService = app(LanguageService::class);
    }

    public function redirectPath()
    {
        return Redirect::getIntendedUrl() ?? route('home-lang', ['lang' => app()->getLocale()]);
    }

    public function showLoginForm()
    {
        if (Auth::check()) {
            // If you have dashboard subdomain logic, add here:
            return redirect()->route('dashboard.index');
        }

        if (url()->previous() != route('register') && Redirect::getIntendedUrl() === null) {
            Redirect::setIntendedUrl(url()->previous()); // make sure we redirect back to the page we came from
        }

        return view('auth.login');
    }

    protected function authenticated(Request $request, $user)
    {
        if ($user->is_blocked) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => 'Your account has been blocked. Please contact support.',
            ]);
        }

        if ($user->isDeleted()) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => 'Your account has been deleted',
            ]);
        }

        if (!$user->hasVerifiedEmail()) {
            $this->guard()->logout();

            return redirect()->route('login')->withErrors([
                'account' => "La vérification de l'e-mail n'est pas terminée, veuillez vérifier votre e-mail pour continuer",
            ]);
        }

        // Check for cart recovery after successful login
        $cartRedirectUrl = $this->cartService->handleUserLogin($user->id);
        if ($cartRedirectUrl) {
            return redirect()->to($cartRedirectUrl);
        }

        if ($request->filled('redirect-step-2') && str_starts_with($request->input('redirect-step-2'), '/')) {
            return redirect()->to($request->input('redirect-step-2') . '?step=2');
        }

        if ($request->getHost() === config('services.app_main.url')) {
            $token = Str::random(40);
            Cache::put('login_token_' . $token, $user->id, now()->addMinutes(5));

            return redirect()->away(config('services.base_url.dashboard')."/login-with-token?token={$token}");
        }

        return redirect()->intended($this->redirectPath());
    }

    protected function validateLogin(Request $request)
    {
        $this->loginValidator->validateRequest($request);
    }

    protected function attemptLogin(Request $request)
    {
        return $this->loginService->attempt($this->credentials($request), $request->boolean('remember'));
    }

    public function  dashboardLogin(Request $request) {
        $token = $request->query('token');

        if (!$token) {
            abort(403, 'Token manquant');
        }

        $userId = Cache::pull('login_token_' . $token);

        if (!$userId || !$user = User::find($userId)) {
            abort(403, 'Token invalide ou expiré');
        }

        Auth::login($user);
        $language = "en";
        $settings_json = json_decode($user['settings_json'], true);
        if(isset($settings_json["base_language"])) {
            $language = $settings_json["base_language"];
        }
        $this->languageService->updateLanguage($language);

        $request->session()->regenerate();
        return redirect()->intended('/dashboard');
    }

    public function customLogin(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required','email'],
            'password' => ['required'],
        ]);

        if (!Auth::guard()->attempt($credentials)) {
            return response()->json(['message' => 'Invalid credentials'], 422);
        }

        $user = Auth::user();

        $token = $user->createToken('login', ['*']); 

        return response()->json([
            'id' => $user->id,
            'token' => $token->plainTextToken, 
            'token_type' => 'Bearer',
        ]);
    }

    public function loginByPin(Request $request)
    {
        $credentials = $request->validate([
            'pin' => ['required'], 
        ]);

        $user = \App\Models\User::where('pin', $credentials['pin'])->first();

        if (!$user) {
            return response()->json(['message' => 'Invalid PIN'], 422);
        }

        Auth::login($user);

        $token = $user->createToken('login', ['*']);

        return response()->json([
            'id' => $user->id,
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
        ]);
    }

    public function revokeToken(Request $request, $tokenId)
    {
        $token = PersonalAccessToken::find($tokenId);

        if ($token) {
            $token->delete();
            return response()->json(['message' => 'Token revoked successfully']);
        }

        return response()->json(['message' => 'Token not found'], 404);
    }

    public function checkToken(Request $request)
    {
        $request->validate([
            'token' => 'required|string'
        ]);

        $plainTextToken = $request->input('token');

        if (strpos($plainTextToken, '|') === false) {
            return response()->json(['valid' => false, 'message' => 'Invalid token format'], 400);
        }

        [$id, $tokenPart] = explode('|', $plainTextToken, 2);

        $tokenRecord = PersonalAccessToken::find($id);

        if (! $tokenRecord) {
            return response()->json(['valid' => false, 'message' => 'Token not found'], 404);
        }

        $hash = hash('sha256', $tokenPart);

        if (hash_equals($tokenRecord->token, $hash)) {
            return response()->json(['valid' => true, 'message' => 'Token is valid']);
        }

        return response()->json(['valid' => false, 'message' => 'Token is invalid'], 401);
    }

    public function logoutCustom(Request $request)
    {
        $currentHost = $request->getHost();
        $user = Auth::user();

        $logoutToken = Str::random(40);
        Cache::put('logout_token_' . $logoutToken, $user->id, now()->addMinutes(5));

        // Auth::logout();
        // $request->session()->invalidate();
        // $request->session()->regenerateToken();


        dd($currentHost);

        if ($currentHost === config('services.app_main.url')) {
            return redirect()->away(config('services.base_url.admin') . "/logout-from-main?logout_token={$logoutToken}");
        }

        if ($currentHost === 'dashboard.localhost') {
            return redirect()->away(config('services.base_url.app') . "/logout-from-dashboard?logout_token={$logoutToken}");
        }
    }

    public function logoutFromDashboard(Request $request)
    {
        $logoutToken = $request->query('logout_token');

        if ($logoutToken) {
            $userId = Cache::pull('logout_token_' . $logoutToken);

            if ($userId) {
                // Forcer le logout si l'utilisateur est connecté avec le même ID
                if (Auth::check() && Auth::id() == $userId) {
                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                }
            }
        }

        // Logout de sécurité
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

          return response()->json(['success' => true]);
    }

    public function logoutFromMain(Request $request)
    {
        $logoutToken = $request->query('logout_token');

        if ($logoutToken) {
            $userId = Cache::pull('logout_token_' . $logoutToken);

            if ($userId) {
                // Forcer le logout si l'utilisateur est connecté avec le même ID
                if (Auth::check() && Auth::id() == $userId) {
                    Auth::logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                }
            }
        }

        // Logout de sécurité
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

          return response()->json(['success' => true]);
    }
}
