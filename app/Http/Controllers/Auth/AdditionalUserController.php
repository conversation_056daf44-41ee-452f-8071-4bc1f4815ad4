<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\Cms\SendCmsMail;
use App\Models\User;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AdditionalUserController extends Controller
{
    /**
     * Create a new sub-user under the authenticated user.
     */
    public function store(Request $request)
    {
        // Ensure the current user is not a sub-user
        if (auth()->user()->is_sub_user) {
            return response()->json([
                'message' => 'Sub-users cannot create additional users.'
            ], 403);
        }

        $lang = auth()->user()->settings["base_language"] ?? 'en';

        // Validate input
        $validated = $request->validate([
            'name' => 'string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string',
        ]);

        if (!$validated['name']) {
            $validated['name'] = $validated['email']->split('@')[0]; // Default name to the email prefix if not provided
        }
        // Create the sub-user
        $subUser = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'is_sub_user' => true,
            'email_verified_at' => Carbon::now(),
            'owner_id' => auth()->id(),
        ]);

        $vars = [
            'username' => $validated['name'],
            'email' => $validated['email'],
            'password' => $validated['password'],
        ];
        logger(['popo',$vars,$lang]);
        SendCmsMail::make()
            ->to($validated['email'])
            ->template('core.subscription-user-credential-created')
            ->lang($lang)
            ->vars($vars)
            ->send();

        return response()->json([
            'message' => 'Sub-user created successfully.',
            'user' => $subUser,
        ], 201);
    }

    /**
     * Get a list of sub-users for the authenticated owner.
     */
    public function index()
    {
        if (auth()->user()->is_sub_user) {
            return response()->json([
                'message' => 'Sub-users cannot view this resource.'
            ], 403);
        }

        $subUsers = auth()->user()->subUsers;

        return response()->json([
            'sub_users' => $subUsers
        ]);
    }

    /**
     * Delete a specific sub-user by ID.
     */
    public function destroy($id)
    {
        $user = auth()->user();

        if ($user->is_sub_user) {
            return response()->json([
                'message' => 'Sub-users cannot delete this resource.'
            ], 403);
        }

        $subUser = User::where('id', $id)
            ->where('owner_id', $user->id)
            ->where('is_sub_user', true)
            ->first();

        if (!$subUser) {
            return response()->json([
                'message' => 'Sub-user not found or unauthorized.'
            ], 404);
        }

        $subUser->forceDelete();

        return response()->json([
            'message' => 'Sub-user deleted successfully.'
        ]);
    }

    public function getCurrentSubscription(
        Request $request,
        SubscriptionService $subscriptionService
    ) {
        $user = auth()->user();

        if ($user->is_sub_user && $user->owner_id) {
            $subscription = $subscriptionService->findCurrentActiveUserSubscription($user->owner_id);

            if (!$subscription) {
                return response()->json([
                    'message' => 'No active subscription found.'
                ], 404);
            }
            return response()->json([
                'subscription' => $subscription
            ]);
        }

        return response()->json([
            'subscription' => $subscriptionService->findCurrentActiveUserSubscription($user->id)
        ]);
    }
    public function updateUserStatus(Request $request)
    {
        $owner = auth()->user();

        if ($owner->is_sub_user) {
            return response()->json([
                'message' => 'Sub-users cannot do this action.'
            ], 403);
        }
        // Validate the incoming request
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'is_blocked' => 'required|boolean',
        ], [
            'user_id.required' => 'The user ID is required.',
            'user_id.exists' => 'The selected user does not exist.',
            'is_blocked.required' => 'The block status is required.',
            'is_blocked.boolean' => 'The block status must be true or false.',
        ]);
        $subUser = User::where('id', $validated['user_id'])
            ->where('owner_id', $owner->id)
            ->where('is_sub_user', true)
            ->first();

        if (!$subUser) {
            return response()->json([
                'message' => 'Sub-user not found or unauthorized.'
            ], 404);
        }

        $subUser->is_blocked = $validated['is_blocked'];
        $subUser->save();

        return response()->json([
            'message' => "{$subUser->email} status updated: isBlocked -> $subUser->is_blocked. "
        ]);
    }

    public function checkMail(Request $request, User $user)
    {
        $query = User::query();

        if ($request->filled('email')) {
            $query->where('email', 'like', '' . $request->email . '');
        }

        $count = $query->count();
        return response()->json(['count' => $count]);
    }

}
