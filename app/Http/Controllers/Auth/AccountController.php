<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use App\Mail\Cms\SendCmsMail;
use App\Services\ConfigService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class AccountController extends Controller
{

    protected ConfigService $configService;

    public function __construct(ConfigService $configService)
    {
        $this->configService = $configService;
    }

    public function reactivate(Request $request)
    {
        try {
            $data = Crypt::decrypt($request->query('token'));

            if (now()->timestamp > $data['expires_at']) {
                return redirect('/')->with('error', 'This link has expired.');
            }

            $user = User::withTrashed()->findOrFail($data['user_id']);

            if ($user->isDeleted()) {
                $user->to_delete_at = null; // Clear the deletion date
                $user->deleted_at = null; // Clear the deletion date
                $user->save();
                return redirect('/login')->with('success', 'Your account has been reactivated.');
            }

            return redirect('/')->with('info', 'Account is already active.');

        } catch (\Exception $e) {
            return redirect('/')->with('error', 'Invalid or tampered link.');
        }
    }

    /**
     * Delete users whose to_delete_at is due, and send reactivation email.
     * Intended for cron job usage.
     */
    public function deleteExpiredAccountsForCron()
    {
        $storedDuration = (int)($this->configService->get('account.stored_duration') ?? 365);
        $now = now();
        $users = User::whereNotNull('to_delete_at')
            ->whereNull('deleted_at')
            ->get();

        $msg = $users->count() > 0 ? "Deleting expired accounts: " . $users->count() : 'No accounts to delete';

        logger('Delete Accounts Process: ' . $msg);

        foreach ($users as $user) {
            try {
                if ($user->to_delete_at && $now->greaterThanOrEqualTo($user->to_delete_at)) {
                    $to_delete_at = $user->to_delete_at;
                    if (is_string($to_delete_at)) {
                        $to_delete_at = Carbon::parse($to_delete_at);
                    }

                    $limiteDateToDelete = $to_delete_at->addDays($storedDuration)->format('d/m/Y');
                    $lang = $user->settings["base_language"] ?? 'en';
                    $data = [
                        'user_id' => $user->id,
                        'expires_at' => now()->addDays($storedDuration)->timestamp,
                    ];
                    $token = Crypt::encrypt($data);
                    $reactivationUrl = config("app.url") . '/reactivate-account?token=' . urlencode($token);
                    $vars = [
                        'name' => $user->name,
                        'stored_duration' => $storedDuration,
                        'limite_date_to_delete' => $limiteDateToDelete,
                        'reactivation_url' => $reactivationUrl,
                    ];

                    // Account Deletion
                    $user->delete();

                    // Send CMS-based email
                    SendCmsMail::make()
                        ->to($user->email)
                        ->template('core.delete-account-after-cron')
                        ->lang(app()->getLocale() ?? $lang)
                        ->vars($vars)
                        ->send();
                }
            } catch (\Exception $e) {
                // Log error or handle as needed, but continue with next user
                Log::error('Failed to delete/send email for user ID ' . $user->id . ': ' . $e->getMessage());
            }
        }

        return response()->json([
            'message' => $msg,
        ]);
    }
}
