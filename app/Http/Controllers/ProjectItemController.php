<?php
namespace App\Http\Controllers;

use App\Models\ProjectItem;
use Illuminate\Http\Request;
use App\Http\Resources\ProjectItemResource;
use App\Models\Project;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Illuminate\Validation\Rule;

class ProjectItemController extends Controller
{
    public function index()
    {
        return ProjectItemResource::collection(ProjectItem::with('project')->paginate(20));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'item_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('project_items')->where(function ($query) use ($request) {
                    return $query->where('project_id', $request->project_id);
                }),
            ],
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ], [
            'item_name.unique' => 'core.error.simulation_unique'
        ]);

        $item = ProjectItem::create($data);
        return new ProjectItemResource($item->load('project'));
    }

    public function show(ProjectItem $projectItem)
    {
        return new ProjectItemResource($projectItem->load('project'));
    }

    public function update(Request $request, ProjectItem $projectItem)
    {
        $data = $request->validate([
            'item_name' => 'sometimes|required|string|max:255',
            'item_info' => 'nullable|array',
            'item_type' => 'nullable|string|max:255',
            'created_by' => 'nullable|exists:users,id',
            'last_user_to_interact' => 'nullable|exists:users,id',
        ]);

        $projectItem->update($data);
        return new ProjectItemResource($projectItem->fresh('project'));
    }

    public function destroy(ProjectItem $projectItem)
    {
        $projectItem->delete();
        return response()->json(['message' => 'Project item deleted']);
    }

    public function getProjectItemTypes(Request $request)
    {
        $types = ProjectItem::query()
            ->select('item_type')
            ->whereNotNull('item_type')
            ->distinct()
            ->orderBy('item_type', 'asc')
            ->pluck('item_type');

        return response()->json([
            'data' => $types
        ]);
    }

    /**
     * Generate a PDF from simulation variables
     */
    public function generatePdf(Request $request)
    {
        $data = $request->validate([
            'projectId'    => 'required',
            'simulationId' => 'nullable',
            'lang'         => 'string',
        ]);

        $project = Project::find($data['projectId']);
        if (!$project) {
            return response()->json(['error' => 'Project not found'], 404);
        }

        $simulation = null;
        if (isset($data['simulationId'])) {
            $simulation = ProjectItem::find($data['simulationId']);
            if (!$simulation) {
                return response()->json(['error' => 'Simulation not found'], 404);
            }
        }

        $user = auth()->user();
        $data = [
            ...$data,
            "client" => [
                "id" => $user->id,
                "name" => $user->name,
                "email" => $user->email,
                "phoneNumber" => $user->phone_number,
            ],
            "projectInfo" => [
                "id" => $project->id,
                "location" => $project->country,
                "projectName" => $project->project_name,
                "simulationName" => $simulation->item_name ?? '-',
            ]
        ];

        // Create Gotenberg client
        $client = Gotenberg::chromium(config('services.gotenberg.url'));

        // Build HTMLRequest
        $outputFilename = 'simulation_' . $user->id;
        $request = $client->pdf()->outputFilename($outputFilename)->paperSize('29.7cm', '21cm')->margins(0, 0, 0, 0);

        $pages = [
            'pdf.simulation.page-header' => $data,
            'pdf.simulation.page2' => $data,
        ];
        $companyLogo = $user->getCompanyLogoUrl();
        $urlImage = $companyLogo ? config('app.url') . $companyLogo : null;

        $pageNumber = 1; // Start counting at 1

        // CONSTRUCT HTML
        $html = view('pdf.partials.head')->render();
        foreach ($pages as $blade => $pageData) {
            $html .= view($blade, array_merge([
                'lang' => $data['lang'] ?? 'en',
                'pageIdx' => $pageNumber,
                'urlImage' => $urlImage,
            ], $pageData))->render();
            $pageNumber++;
        }
        $html .= view('pdf.partials.foot')->render();

        // Save the html
        // file_put_contents(storage_path('tmp/indexx.html'), $html);

        $request = $request->html(Stream::string('index.html', $html));

        // Send and store or get PDF
        Gotenberg::save($request, storage_path('tmp'));

        return response()->download(storage_path('tmp/' . $outputFilename . '.pdf'))->deleteFileAfterSend(true);
    }

}
