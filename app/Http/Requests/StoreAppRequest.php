<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAppRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name'   => 'required|string|max:255|unique:app,name',
            'appKey' => 'required|string|max:255|unique:app,appKey',
            'secret' => 'required|string|min:16|max:255',
            'url'    => 'required|url|max:255',
        ];
    }
}
