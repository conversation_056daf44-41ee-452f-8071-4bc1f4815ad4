<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAppRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $appClientId = $this->route('app_client') ? $this->route('app_client')->id : null;

        return [
            'name'   => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('app', 'name')->ignore($appClientId),
            ],
            'appKey' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('app', 'appKey')->ignore($appClientId),
            ],
            'secret' => 'sometimes|string|min:16|max:255',
            'url'    => 'sometimes|url|max:255',
        ];
    }
}
