<?php
namespace App\Helpers;

use NumberFormatter;

class FormatHelper
{
    /**
     * Formats a number according to the application's current locale
     * or a specified locale, using the intl NumberFormatter.
     *
     * @param int|float $number The number to format.
     * @param string|null $locale The locale to use (e.g., 'en_US', 'de_DE'). Defaults to app()->getLocale().
     * @param int $style The NumberFormatter style (e.g., NumberFormatter::DECIMAL, NumberFormatter::CURRENCY).
     * @param int $maxFractionDigits For decimal style, the maximum number of fraction digits.
     * @return string|false The formatted number string, or false on error.
     */
    public static function localizedNumber(
        int | float $number,
        ?string $locale = null,
        int $style = NumberFormatter::DECIMAL,
        int $maxFractionDigits = 2
    ): string | false {
        if (! extension_loaded('intl')) {
            if ($style === NumberFormatter::DECIMAL) {
                $decimals = ($number == (int) $number && $maxFractionDigits > 0 && $style == NumberFormatter::DECIMAL && floor($number) == $number) ? 0 : $maxFractionDigits;
                if (floor($number) == $number && $style == NumberFormatter::DECIMAL) {
                    $decimalsToShow = 0;
                } else {
                    $decimalsToShow = $maxFractionDigits;
                }
                return number_format($number, $decimalsToShow);
            }
            return (string) $number;
        }

        $currentLocale = $locale ?? app()->getLocale();

        $formatter = new NumberFormatter($currentLocale, $style);

        if ($style === NumberFormatter::DECIMAL) {
            if (floor($number) == $number) {
                $formatter->setAttribute(NumberFormatter::MAX_FRACTION_DIGITS, 0);
            } else {
                $formatter->setAttribute(NumberFormatter::MAX_FRACTION_DIGITS, $maxFractionDigits);
            }
        }

        return $formatter->format($number);
    }
}
