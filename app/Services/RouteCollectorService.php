<?php

namespace App\Services;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;

class RouteCollectorService
{
    public function __construct()
    {
    }

    /**
     * Get controller instance from route metadata.
     */
    public function getController(array $routeMeta): object
    {
        return App::make($routeMeta['controller']);
    }

    /**
     * Get route pattern path from metadata.
     */
    public function getPattern(array $routeMeta): ?string
    {
        return $routeMeta['path'] ?? null;
    }

    /**
     * Get controller method from route metadata.
     */
    public function getAction(array $routeMeta): ?string
    {
        return $routeMeta['action'] ?? null;
    }

    /**
     * Given a full path like "/en/blog", find the route definition that matches.
     */
    public function getRouteControllerByPath(string $path): ?array
    {
        $normalizedPath = trim($path, '/');
        foreach (Route::getRoutes() as $route) {
            if ($route->uri() === $normalizedPath) {
                $action = $route->getAction();
                if (isset($action['controller'])) {
                    [$controller, $method] = explode('@', $action['controller']);
                    return [
                        'controller' => $controller,
                        'method' => $method,
                    ];
                }
            }
        }
        return null; 
    }

    /**
     * Replace dynamic parameters with regex placeholders.
     */
    protected function convertToRegex(string $routePattern): string
    {
        return preg_replace('/\{[a-zA-Z_]+\}/', '([^/]+)', $routePattern);
    }
}
