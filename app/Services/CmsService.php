<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CmsService
{
    protected $cmsUrl;
    protected PendingRequest $httpClient;
    public function __construct()
    {
        $this->cmsUrl = config('services.cms_api.url');
        $baseUrl = config('services.cms_api.url');

        $this->httpClient = Http::baseUrl($baseUrl)
            ->acceptJson();
    }

    protected function sendRequest($method, $endpoint, $data = [], $queryParams = [])
    {
        try {
            $request = Http::withQueryParameters($queryParams);
            $response = $request->{$method}("{$this->cmsUrl}/{$endpoint}", $method === 'get' ? $queryParams : $data);
            if ($response->failed()) {
                throw new \Exception("Request failed with status: {$response->status()} and message: {$response->body()}");
            }
            return $response->json();
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public function createContentManagementSystem($data)
    {
        return $this->sendRequest('post', 'content-management-system', $data);
    }

    public function getContentManagementSystems($queryParams = [])
    {
        return $this->sendRequest('get', 'content-management-system', [], $queryParams);
    }

    public function getCMSRedirectionUrlV4($queryParams = [])
    {
        return $this->sendRequest('get', 'content-management-system-all-url/V4', [], $queryParams);
    }

    public function getListCMSRedirectionUrl($queryParams = [])
    {
        return $this->sendRequest('get', 'redirection-url', [], $queryParams);
    }

    public function getContentManagementSystemUrlKey($queryParams = [])
    {
        return $this->sendRequest('get', "content-management-system/urlkey", [], $queryParams);
    }

    public function getContentByKey($key, $lang = "en")
    {
        $cmsResponse = $this->sendRequest('get', "content-management-system/key/{$key}");
        if ($lang == "en") {
            return $cmsResponse;
        }
        $translatedCms = $this->getTranslatedCms($cmsResponse["id"], $lang);

        if ($translatedCms) {
            $cmsResponse["content"] = $translatedCms["content"] ?? $cmsResponse["content"];
            $cmsResponse["title"] = $translatedCms["title"] ?? $cmsResponse["title"];
            $cmsResponse["metaTitle"] = $translatedCms["metaTitle"] ?? $cmsResponse["metaTitle"];
            $cmsResponse["metaDescription"] = $translatedCms["metaDescription"] ?? $cmsResponse["metaDescription"];
            $cmsResponse["metaKeywords"] = $translatedCms["metaKeywords"] ?? $cmsResponse["metaKeywords"];
            $cmsResponse["url"] = $translatedCms["url"] ?? $cmsResponse["url"];
        }
        return $cmsResponse;
    }

    function getTranslatedCms($cmsDataId, $language, $columnName = [])
    {
        $translationApiService = app(TranslationApiService::class);

        $whereValue = [
            ['key' => 'tableName', 'value' => 'ContentManagementSystem'],
            ['key' => 'rowId', 'value' => $cmsDataId],
            ['key' => 'apiId', 'value' => config('services.cms_api.api_id')],
            ['key' => 'language.languageISO2', 'value' => $language],
        ];

        if (!empty($columnName)) {
            $formattedColumns = implode(',', array_map(fn($col) => "'$col'", $columnName));
            $whereValue[] = [
                'key' => 'columnName',
                'option' => 'IN',
                'value' => $formattedColumns,
            ];
        }

        $paramsOb = ['where' => json_encode($whereValue)];
        $result = $translationApiService->getResourceTranslation($paramsOb);
        $data = $result['data'] ?? [];

        $translationsMap = [];
        foreach ($data as $entry) {
            foreach ($entry['translations'] as $translation) {
                $langCode = $translation['language']['languageISO2'];
                $column = $entry['columnName'];
                $translatedValue = $translation['translation'] ?? $translation['autoTranslatedValue'];
                $translationsMap[$langCode][$column] = $translatedValue;
            }
        }

        return $translationsMap[$language] ?? [];
    }


    public function getCmsV4($queryParams = [])
    {
        return $this->sendRequest('get', 'content-management-system/v4', [], $queryParams);
    }

    public function getContentById($id, $queryParams = [])
    {
        return $this->sendRequest('get', "content-management-system/{$id}", [], $queryParams);
    }

    public function updateContentManagementSystem($id, $data)
    {
        return $this->sendRequest('patch', "content-management-system/{$id}", $data);
    }

    public function deleteContentManagementSystem($id, $softDelete = false)
    {
        $endpoint = $softDelete
            ? "content-management-system/{$id}/soft"
            : "content-management-system/{$id}";

        return $this->sendRequest('delete', $endpoint);
    }

    public function performUrl($url) {
        if ($url && strpos($url ?? '', '/{lang}/') !== 0) {
            $url = '/{lang}/' . $url;
        }
        return $url;
    }

    /**
     * Handle redirection URL creation if not exists, and bust Sushi cache if created.
     *
     * @param int $cmsId
     * @param string $url
     * @param int|null $languageId
     * @return array|null The created redirection or null if already exists
     */
    public function createCMSRedirectionUrl($cmsId, $url, $languageId = null)
    {

        $url = $this->performUrl($url);

        $redirectionUrlQueryParams = [
            'where' => json_encode([
                'url' => [
                    'value' => $url,
                    'operator' => '=='
                ]
            ]),
            'fields' => json_encode(['id', 'url']),
        ];

        $checkRedirectionUrl = $this->getCMSRedirectionUrlV4($redirectionUrlQueryParams);
        


        if (empty($checkRedirectionUrl['data'])) {
            $redirectionUrlData = [
                'cmsId' => $cmsId,
                'url' => $url,
            ];
            if ($languageId !== null) {
                $redirectionUrlData['languageId'] = $languageId;
            }

            $createRedirectionUrl = $this->sendRequest('post', 'content-management-system-all-url', $redirectionUrlData);
            if (isset($createRedirectionUrl['cmsId'])) {
                \App\Models\Cms::bustSushiCache();
                return $createRedirectionUrl;
            }
        }
        return null;
    }
}
