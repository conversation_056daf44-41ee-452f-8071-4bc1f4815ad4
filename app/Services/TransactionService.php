<?php

namespace App\Services;

use App\Constants\PaymentProviderConstants;
use App\Constants\ReceiptStatus;
use App\Constants\TransactionStatus;
use App\Models\Cart;
use App\Models\Currency;
use App\Models\Order;
use App\Models\PaymentProvider;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class TransactionService
{
    public function stripeCreate(
        int $amount,
        int $remainingToPay,
        string $currencyCode,
        ?string $stripeObjectId,
        TransactionStatus $status,
        Subscription $subscription,
        ?Cart $cart,
        ?string $invoiceStripeReference,
    ): Transaction {
        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)->firstOrFail();
        $currency = Currency::where('code', strtoupper($currencyCode))->firstOrFail();
        $user = User::with(['address', 'address.country'])->where('id',$subscription->user_id)->first();
        $subscriptionCore = Subscription::where('id', $subscription->id)->first();
        $transaction = Transaction::create([
            'uuid' => (string) Str::uuid(),
            'user_id' => $subscription->user_id ,
            'currency_id' => $currency->id,
            'amount' => $amount,
            'total_tax' => 0,
            'total_discount' => 0,
            'total_fees' => 0,
            'status' => $status->value,
            'subscription_id' => $subscription->id ,
            'payment_provider_id' => $paymentProvider->id,
            'customer_json' => $user->toArray(),
            'subscription_json' => $subscriptionCore->toArray(),
            'payment_provider_transaction_id' => $stripeObjectId,
            'payment_provider_reference' =>  $invoiceStripeReference,
            'cart_id' => $cart ? $cart->id : null,
            'remaining_to_pay' => $remainingToPay
        ]);
        $transaction->saveInvoiceNumber();
        return $transaction;
    }
    public function createForSubscription(
        int $amount,
        int $totalTax,
        int $totalDiscount,
        int $totalFees,
        Currency $currency,
        PaymentProvider $paymentProvider,
        string $paymentProviderTransactionId,
        string $paymentProviderStatus,
        TransactionStatus $status = TransactionStatus::NOT_STARTED,
        Subscription $subscription,
        Cart $cart,
    ): Transaction {

        $user = User::with(['address', 'address.country'])->where('id',$subscription->user_id)->first();
        $subscriptionCore = Subscription::where('id', $subscription->id)->first();
        $transaction = Transaction::create([
            'uuid' => (string) Str::uuid(),
            'user_id' => $subscription->user_id ,
            'currency_id' => $currency->id,
            'amount' => $amount,
            'total_tax' => $totalTax,
            'total_discount' => $totalDiscount,
            'total_fees' => $totalFees,
            'status' => $status->value,
            'subscription_id' => $subscription->id ,
            'payment_provider_id' => $paymentProvider->id,
            'customer_json' => $user->toArray(),
            'subscription_json' => $subscriptionCore->toArray(),
            'payment_provider_status' => $paymentProviderStatus,
            'payment_provider_transaction_id' => $paymentProviderTransactionId,
            'cart_id' => $cart->id,
        ]);
        $transaction->saveInvoiceNumber();
        return $transaction;
    }

    public function updateTransactionByPaymentProviderTxId(
        string $paymentProviderTransactionId,
        string $paymentProviderStatus,
        TransactionStatus $status,
        ?string $errorReason = null,
        ?int $newAmount = null,
        ?int $newFees = null,
    ): Transaction {
        $transaction = Transaction::where('payment_provider_transaction_id', $paymentProviderTransactionId)->firstOrFail();

        return $this->updateTransaction(
            $transaction,
            $paymentProviderStatus,
            $status,
            $errorReason,
            $newAmount,
            $newFees,
        );
    }

    public function updateTransaction(
        Transaction $transaction,
        string $paymentProviderStatus,
        TransactionStatus $status,
        ?string $errorReason = null,
        ?int $newAmount = null,
        ?int $newFees = null,
    ) {
        $data = [
            'status' => $status->value,
            'payment_provider_status' => $paymentProviderStatus,
        ];

        if ($newAmount !== null) {
            $data['amount'] = $newAmount;
        }

        if ($errorReason) {
            $data['error_reason'] = $errorReason;
        }

        if ($newFees !== null) {
            $data['total_fees'] = $newFees;
        }

        $transaction->update($data);

        return $transaction;
    }

    public function getTransactionByPaymentProviderTxId(string $paymentProviderTransactionId): ?Transaction
    {
        return Transaction::where('payment_provider_transaction_id', $paymentProviderTransactionId)->first();
    }

    public function getCurrentUserTransaction()
    {
        $user = Auth::user();

        if (!$user) {
            return collect();
        }

        return Transaction::with([
                'cart',
                'receipts'=> function ($q) {
                    $q->where('receipt_status', ReceiptStatus::SUCCESS->value);
                }
            ])
            ->orderBy('created_at', 'desc')
            ->where('user_id', $user->id)->get();
    }


    public function createForOrder(
        Order $order,
        int $amount,
        int $totalTax,
        int $totalDiscount,
        int $totalFees,
        Currency $currency,
        PaymentProvider $paymentProvider,
        string $paymentProviderTransactionId,
        string $paymentProviderStatus,
        TransactionStatus $status = TransactionStatus::NOT_STARTED,
    ): Transaction {
        return $order->transactions()->create([
            'uuid' => (string) Str::uuid(),
            'user_id' => $order->user_id,
            'currency_id' => $currency->id,
            'amount' => $amount,
            'total_tax' => $totalTax,
            'total_discount' => $totalDiscount,
            'total_fees' => $totalFees,
            'status' => $status->value,
            'payment_provider_id' => $paymentProvider->id,
            'payment_provider_status' => $paymentProviderStatus,
            'payment_provider_transaction_id' => $paymentProviderTransactionId,
        ]);
    }
}
