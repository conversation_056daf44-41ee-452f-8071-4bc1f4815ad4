<?php

namespace App\Services;

use App\Models\IpLocation;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
class IpLocationService
{
  public function getLocationByIp($ip): array
  {
    if (!$ip) return [];
    $location = IpLocation::where('ip', $ip)->first();

    if (!$location) {
      $ipLocationUrl = str_replace('{ip}', $ip, config('services.ip_location.url'));
      $response = Http::get($ipLocationUrl);

      if ($response->ok() && $response['status'] === 'success') {
        $location = IpLocation::create([
          'ip' => $ip,
          'data_json' => $response->json(),
        ]);
      } else {
        return ['error' => $response['message'] ?? 'Failed to fetch IP location'];
      }
    }

    return $location->data_json;
  }

  public function getLocationByIpViaData(string $ip): array
  {
    if (!$ip) {
        return [
            'query' => $ip,
            'status' => 'error',
            'message' => 'IP is required'
        ];
    }

   
    $blocks = DB::table('ip_blocks')->get();

   
    $block = $blocks->first(function ($b) use ($ip) {
        return $this->ipInCidr($ip, $b->cidr);
    });

    if (!$block || !isset($block->country_id)) {
        return [
            'query' => $ip,
            'status' => 'fail',
            'message' => 'invalid query'
        ];
    }

    
    $country = DB::table('countries')->where('id', $block->country_id)->first();
  
    if (!$country) {
        return [
            'query' => $ip,
            'status' => 'fail',
            'message' => 'Country not found'
        ];
    }
    $timezone = $this->offsetToTimezone($country->timezone_offset);

    return [
        'query' => $ip,
        'status' => 'success',
        'country' => $country->name,
        'countryCode' => $country->code_alpha_2,
        'idRegion' => $country->id,        
        'timezone' => $timezone ?? null,
        'cidr' => $block->cidr
      
    ];
  }

  
  private function ipInCidr(string $ip, string $cidr): bool
  {
      [$subnet, $maskLength] = explode('/', $cidr);

      $ipLong = ip2long($ip);
      $subnetLong = ip2long($subnet);

      if ($ipLong === false || $subnetLong === false) {
          return false; // IP invalide
      }

      $mask = ~((1 << (32 - (int)$maskLength)) - 1);
      return ($ipLong & $mask) === ($subnetLong & $mask);
  }

 
  private function offsetToTimezone(string $offset): string
  {
      try {
        
          $dtz = new \DateTimeZone($offset);
          $dateTime = new \DateTime('now', $dtz);

        
          foreach (\DateTimeZone::listIdentifiers() as $tz) {
              $tzObj = new \DateTimeZone($tz);
              if ($tzObj->getOffset($dateTime) === $dtz->getOffset($dateTime)) {
                  return $tz; 
              }
          }
      } catch (\Exception $e) {
         
      }

      return 'UTC';
  }
}
