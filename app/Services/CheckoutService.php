<?php

namespace App\Services;

use App\Constants\OrderStatus;
use App\Constants\PaymentProviderConstants;
use App\Dto\CartDto;
use App\Models\PaymentProvider;

class CheckoutService
{
    public function __construct(
        private SubscriptionService $subscriptionService,
        private OrderService $orderService,
        private CartService $cartService,
    ) {}

    public function initSubscriptionCheckout(string $planSlug)
    {
        $subscription = $this->subscriptionService->findNewByPlanSlugAndUser($planSlug, auth()->id());
        if ($subscription === null) {
            $subscription = $this->subscriptionService->create($planSlug, auth()->id());
        }

        return $subscription;
    }

    public function initSubscriptionCheckoutWithUser(
        array $params
    )
    {
        $cart = $params['cart'];
        $planSlug = $params['planSlug'];
        $userId = $params['userId'];
        $paymentProviderSubscriptionId = $params['paymentProviderSubscriptionId'] ?? null;
        $endsAt = $params['endsAt'] ;
        $startsAt = $params['startsAt'] ;
        $newSubscriptionId = $params['newSubscriptionId'];

        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)
            ->where('is_active', 1)
            ->first();

        if (!$paymentProvider) {
            throw new \Exception("Payment provider stripe is not available.");
        }




        $subscription = $this->subscriptionService->activateNew(
            [
                'startsAt' => $startsAt,
                'endsAt' => $endsAt,
                'planSlug' => $planSlug,
                'userId' => $userId,
                'paymentProvider' => $paymentProvider,
                'paymentProviderSubscriptionId' => $paymentProviderSubscriptionId,
                'newSubscriptionId' => $newSubscriptionId,
                'cart' => $cart
            ]
        );


        return $subscription;
    }

    public function initLocalSubscriptionCheckout(string $planSlug)
    {
        $subscription = $this->subscriptionService->findNewByPlanSlugAndUser($planSlug, auth()->id());
        if ($subscription === null) {
            $subscription = $this->subscriptionService->create($planSlug, auth()->id(), localSubscription: true);
        }

        return $subscription;
    }

    public function initProductCheckout(CartDto $cartDto)
    {
        $user = auth()->user();

        $order = null;
        if ($cartDto->orderId !== null) {
            $order = $this->orderService->findNewByIdForUser($cartDto->orderId, $user);
        }

        if ($order === null) {
            $order = $this->orderService->create($user);
        }

        $this->orderService->refreshOrder($cartDto, $order);

        $order->status = OrderStatus::PENDING->value;
        $order->save();

        return $order;
    }
}
