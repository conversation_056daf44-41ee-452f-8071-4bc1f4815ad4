<?php

namespace App\Services;

use App\Constants\AccountTypeEnum;
use App\Models\User;
use App\Models\AccountInformation;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function createUser(array $data): User
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'settings_json' => isset($data["settings_json"]) ? $data["settings_json"] : null,
        ]);

        AccountInformation::updateOrCreate(
            ['user_id' => $user->id],
            [
                'account_type_id' => isset($data['account_type']) ? AccountTypeEnum::fromName($data['account_type']) : 1,
                'company_name' => isset($data["company_name"]) ? $data["company_name"] : null,
            ]
        );

        return $user;
    }
}
