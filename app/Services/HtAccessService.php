<?php

namespace App\Services;

use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HtAccessService
{
    private function adjustTargetUrl(string $sourceUrl, string $targetUrl): string
    {
        // Count occurrences of capture groups and dollar references
        $captureGroupRegex = '/\(\[\^\\\\\/\]\+\)/';
        $dollarReferenceRegex = '/\$\d+/';

        $numCaptureGroups = preg_match_all($captureGroupRegex, $sourceUrl);
        $numDollarReferences = preg_match_all($dollarReferenceRegex, $targetUrl);

        if ($numDollarReferences > $numCaptureGroups) {
            // If there are more $n references than capture groups
            $dollarParts = preg_split($dollarReferenceRegex, $targetUrl);
            preg_match_all($dollarReferenceRegex, $targetUrl, $matches);

            $adjustedTargetUrl = $dollarParts[0] ?? '';

            for ($i = 1; $i <= $numCaptureGroups; $i++) {
                $adjustedTargetUrl .= '$' . $i . ($dollarParts[$i] ?? '');
            }

            // Append remaining unmatched parts
            if ($numCaptureGroups + 1 < count($dollarParts)) {
                $adjustedTargetUrl .= implode('', array_slice($dollarParts, $numCaptureGroups + 1));
            }

            return rtrim($adjustedTargetUrl, '/');
        }

        return rtrim($targetUrl, '/');
    }

    private function transformUrl(?string $url): string
    {
        $transformedUrl = $url ?? '';

        // Replace {lang} with ([^\/]+)
        $transformedUrl = preg_replace('/\{lang\}/', '([^\\/]+)', $transformedUrl);

        // Replace other variables like {city:[^/]+}, {country:[^/]+}, etc.
        $transformedUrl = preg_replace('/\{[^}]+\}/', '([^\\/]+)', $transformedUrl);

        // Remove the first slash if it exists
        $transformedUrl = preg_replace('/^\//', '', $transformedUrl);

        return trim($transformedUrl);
    }


    private function buildTargetUrlDefault(string $url): string
    {
        $captureGroupCounter = 1;

        // Step 1: Replace {something} like {city:[^/]+} with ([^\/]+)
        $processedUrl = preg_replace('/\{[^}]+\}/', '([^\\/]+)', $url);

        // Step 2: Remove leading slash if present
        $processedUrl = preg_replace('/^\//', '', $processedUrl);

        // Step 3: Replace (...) with $1, $2, etc.
        $processedUrl = preg_replace_callback('/\([^\)]+\)/', function () use (&$captureGroupCounter) {
            return '$' . $captureGroupCounter++;
        }, $processedUrl);

        return $processedUrl;
    }

    public function generateHtaccessTxt()
    {
        $cmsService = app(CmsService::class);
        $redirectionData = $cmsService->getListCMSRedirectionUrl();

        if (isset($redirectionData['data']) && empty($redirectionData['data'])) {
            return '';
        }

        $redirectionData = $redirectionData['data'];

        $htaccessTxt = <<<HTACCESS
            <IfModule mod_rewrite.c>
            RewriteEngine On
            
            RewriteRule ^(.+)/$ /$1 [L,R=301]
            HTACCESS;

        foreach ($redirectionData as $row) {

            if ($row['oldUrl'] == $row['newUrl']) {
                continue;
            }

            $sourceUrlDefault = $this->transformUrl($row['oldUrl']);

            $targetUrlDefault =

                $targetUrlDefault = $this->adjustTargetUrl($sourceUrlDefault, $this->buildTargetUrlDefault($row['newUrl']));

            $htaccessTxt .= PHP_EOL . '  RewriteRule "^' . str_replace(' ', '-', $sourceUrlDefault) . '$" "/' . str_replace(' ', '-', $targetUrlDefault) . '" [L,R=301]';
        }

        $htaccessTxt .= PHP_EOL . <<<HTACCESS
            RewriteRule ^index\.php$ - [L]
            RewriteCond %{REQUEST_URI} !^/public/
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteCond %{REQUEST_FILENAME} !-d
            RewriteRule . /index.php [L]
        </IfModule>
        HTACCESS;

        return $htaccessTxt;
    }
}
