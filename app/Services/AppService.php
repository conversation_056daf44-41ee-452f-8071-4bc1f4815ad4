<?php
namespace App\Services;

use App\Models\AppModel;
use Illuminate\Support\Facades\Log;

class AppService
{
    public function getApps()
    {
        try {
            $apps = AppModel::all();

            $apps = AppModel::query()->get();

            if ($apps->isEmpty()) {
                Log::info('No apps found in the database.');
                return [];
            }

            return $apps;

        } catch (\Exception $e) {
            Log::error('Failed to fetch apps from database: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }
}
