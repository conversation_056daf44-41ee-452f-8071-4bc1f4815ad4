<?php
namespace App\Services;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class LanguageService
{
    public function setLanguage($locale)
    {
        App::setLocale($locale);
        Session::put('locale',$locale);
        logger("Language set to: $locale");
        return back()->withCookie(cookie()->forever('locale', $locale));
    }
    // public function setLanguage($locale)
    // {
    //     App::setLocale($locale);
    //     Session::put('locale', $locale);
    //     logger("Language set to: $locale");
    //     $previousUrl = url()->previous();
    //     $parsed = parse_url($previousUrl);
    //     $path = $parsed['path'] ?? '/';
    //     $segments = explode('/', trim($path, '/'));
    //     if (isset($segments) && isset($segments[0])) {
    //         $segments[0] = $locale;
    //     } else {
    //         array_unshift($segments, $locale);
    //     }
    //     $redirectTo = '/' . implode('/', $segments);
    //     return redirect($redirectTo)->withCookie(cookie()->forever('locale', $locale));
    // }

    public function updateLanguage($locale)
    {
        App::setLocale($locale);
        Session::put('locale', $locale);
        Log::info("change language $locale");
        cookie()->queue(cookie()->forever('locale', $locale));
    }
}
