<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use App\Models\Cart;
use Illuminate\Support\Facades\Log;

class GoogleAnalyticsService
{
    private string $measurementId;
    private string $apiSecret;

    public function __construct(
        private CalculationService $calculationService
    )
    {
        $this->measurementId = config('services.google_analytics.measurement_id') ?? '';
        $this->apiSecret     = config('services.google_analytics.api_secret') ?? '';
    }

    public function sendGa4Report(array $payload): void
    {
        if(!$this->measurementId || !$this->apiSecret) {
            return;
        }
        $url = sprintf(
            'https://www.google-analytics.com/mp/collect?measurement_id=%s&api_secret=%s',
            urlencode($this->measurementId),
            urlencode($this->apiSecret)
        );

        $response = Http::timeout(10)->post($url, $payload);

        if ($response->failed()) {
            throw new \RuntimeException(
                sprintf('GA4 MP error %d: %s', $response->status(), $response->body())
            );
        }
    }

    /**
     * Map cart line items to GA4 format.
     * Expects Cart with relation ->product having fields: id, name, monthly_price
     */
    public function lineItemsToGa4(string $cartUid): array
    {
        $cart = Cart::where('cart_uid', $cartUid)->first();

        $price = ((int) $this->calculationService->getPlanPriceFromArray($cart->plan_json)['price'])/100;

        return [[
            'item_id'   => $cart->plan_json['product']['id'],
            'item_name' => $cart->plan_json['product']['name'],
            'quantity'  => 1,
            'price'     => $price,
            'index'     => $cart->id,
        ]];
    }

    /**
     * $checkoutObj shape expected (Stripe-like):
     * [
     *   'currency' => 'usd',
     *   'amount_total' => 12345,             // cents
     *   'total_details' => [
     *     'amount_tax' => 345,               // cents
     *     'amount_shipping' => 0,            // cents
     *   ],
     *   'metadata' => [
     *     'cartReference' => 'ABC123',
     *     'customerId' => '555.111',         // or any GA4 client_id
     *   ],
     * ]
     */
    public function sendPurchaseReport($checkoutObj): void
    {
        $currency = strtoupper($checkoutObj['currency'] ?? 'USD');

        $amountTotal   = (($checkoutObj['amount_total'] ?? 0) / 100);
        $totalDetails  = $checkoutObj['total_details'] ?? [];
        $taxTotal      = (($totalDetails['amount_tax'] ?? 0) / 100);
        $shippingTotal = (($totalDetails['amount_shipping'] ?? 0) / 100);

        $meta = $checkoutObj['metadata'] ?? [];
        $cartUid   = $meta['cartUid'] ?? null;
        $userId  = $meta['userId'] ?? null;

        if (!$cartUid) {
            throw new \InvalidArgumentException('metadata.cartUid is required');
        }
        if (!$userId) {
            throw new \InvalidArgumentException('metadata.userId is required');
        }

        $items = $this->lineItemsToGa4($cartUid);

        $payload = [
            'client_id' => $userId,
            'events'    => [
                [
                    'name'   => 'purchase',
                    'params' => [
                        'affiliation' => config('app.name'),
                        'currency'    => $currency,
                        'value'       => $amountTotal,
                        'tax'         => $taxTotal,
                        'shipping'    => $shippingTotal,
                        'items'       => $items,
                    ],
                ],
            ],
        ];
        
        Log::info($payload);

        try {
            $this->sendGa4Report($payload);
        } catch (\Throwable $e) {
            // Log and continue, same behavior as the Python print
            logger()->warning('GA4 send failed', ['error' => $e->getMessage(), 'payload' => $payload]);
        }
    }
}
