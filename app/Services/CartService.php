<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CartService
{
    private const CART_COOKIE_NAME = 'cart_uid';
    private const CART_COOKIE_LIFETIME = 60 * 24 * 30; // 30 days in minutes

    public function __construct(
        private Request $request,
        private PlanService $planService
    ) {}

    /**
     * Create a new cart for the given plan
     */
    public function createCart(Plan $plan, ?int $userId = null): Cart
    {
        $completePlan = $this->planService->getCompletePlanById($plan->id);
        $cart = Cart::create([
            'plan_id' => $plan->id,
            'plan_json' => $completePlan->toArray(),
            'step_id' => 1,
            'user_id' => $userId,
            'ip_address' => $this->request->ip(),
            'language_iso_2' => app()->getLocale(),
        ]);

        $this->saveCartUidToCookie($cart->cart_uid);

        return $cart;
    }

    /**
     * Find or create a cart for the given plan
     */
    public function findOrCreateCart(Plan $plan, ?int $userId = null): Cart
    {
        $cartUid = $this->getCartUidFromCookie();

        if ($cartUid) {
            $cart = Cart::where('cart_uid', $cartUid)
                ->where('plan_id', $plan->id)
                ->unconverted()
                ->first();

            if ($cart) {
                // Update user_id if user just logged in
                if ($userId && !$cart->user_id) {
                    $cart->update(['user_id' => $userId]);
                }
                return $cart;
            }
        }

        return $this->createCart($plan, $userId);
    }

    /**
     * Get cart by UID from cookie
     */
    public function getCartFromCookie(): ?Cart
    {
        $cartUid = $this->getCartUidFromCookie();

        if (!$cartUid) {
            return null;
        }

        return Cart::where('cart_uid', $cartUid)
            ->unconverted()
            ->first();
    }

    /**
     * Update cart step (only if higher than current step)
     */
    public function updateCartStep(Cart $cart, int $step): void
    {
        $cart->updateStep($step);
    }

    /**
     * Mark cart as converted after successful subscription purchase
     */
    public function convertCart(Cart $cart, ?int $userId = null): void
    {
        $cart->markAsConverted($userId);
        $this->clearCartCookie();
    }

    /**
     * Get the checkout URL for a cart with the appropriate step
     */
    public function getCheckoutUrl(Cart $cart): string
    {
        $plan = $cart->plan;
        $baseUrl = route('checkout.subscription', ['planSlug' => $plan->slug]);

        return $baseUrl . '?step=' . $cart->step_id;
    }

    /**
     * Save cart UID to cookie
     */
    private function saveCartUidToCookie(string $cartUid): void
    {
        Cookie::queue(
            self::CART_COOKIE_NAME,
            $cartUid,
            self::CART_COOKIE_LIFETIME,
            '/',
            null,
            true, // secure
            true, // httpOnly
            false, // raw
            'lax' // sameSite
        );
    }

    /**
     * Get cart UID from cookie
     */
    private function getCartUidFromCookie(): ?string
    {
        return $this->request->cookie(self::CART_COOKIE_NAME);
    }

    /**
     * Clear cart cookie
     */
    private function clearCartCookie(): void
    {
        Cookie::queue(Cookie::forget(self::CART_COOKIE_NAME));
    }

    /**
     * Handle user login - redirect to cart if exists
     */
    public function handleUserLogin(int $userId): ?string
    {
        $cart = $this->getCartFromCookie();

        if ($cart) {
            // Associate cart with logged-in user
            $cart->update(['user_id' => $userId]);

            // Return checkout URL with saved step
            return $this->getCheckoutUrl($cart);
        }

        return null;
    }

    /**
     * Clean up old unconverted carts (for maintenance)
     */
    public function cleanupOldCarts(int $daysOld = 30): int
    {
        return Cart::unconverted()
            ->where('created_at', '<', now()->subDays($daysOld))
            ->delete();
    }

    /**
     * Convert cart for user when subscription becomes active
     */
    public function convertCartForUser(int $userId, int $planId): void
    {
        $cart = Cart::where('user_id', $userId)
            ->where('plan_id', $planId)
            ->whereNull('converted_at')
            ->first();

        if ($cart) {
            $this->convertCart($cart);
        }
    }
}
