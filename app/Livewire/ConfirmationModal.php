<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;

class ConfirmationModal extends Component
{
    public bool $show = false;
    public string $title = 'Confirm Action';
    public string $message = 'Are you sure?';
    public string $confirmButtonText = 'Confirm';
    public string $cancelButtonText = 'Cancel';

    public ?string $confirmEventName = null;
    public array $confirmEventData = [];

    public ?string $cancelEventName = null;
    public array $cancelEventData = [];

    #[On('openConfirmationModal')]
    public function openModal(
        string $title,
        string $message,
        string $confirmEventName,
        array $confirmEventData = [],
        string $confirmButtonText = 'Confirm',
        string $cancelButtonText = 'Cancel',
        ?string $cancelEventName = null,
        array $cancelEventData = []
    ): void {
        $this->title = $title;
        $this->message = $message;
        $this->confirmEventName = $confirmEventName;
        $this->confirmEventData = $confirmEventData;
        $this->confirmButtonText = $confirmButtonText;
        $this->cancelButtonText = $cancelButtonText;
        $this->cancelEventName = $cancelEventName;
        $this->cancelEventData = $cancelEventData;
        $this->show = true;
    }

    public function confirm(): void
    {
        if ($this->confirmEventName) {
            $this->dispatch($this->confirmEventName, ...$this->confirmEventData);
        }
        $this->closeModal();
    }

    public function cancel(): void
    {
        if ($this->cancelEventName) {
            $this->dispatch($this->cancelEventName, ...$this->cancelEventData);
        }
        $this->closeModal();
    }

    public function closeModal(): void
    {
        $this->show = false;
        // Reset to defaults for next use, though openModal will override
        $this->reset(['title', 'message', 'confirmEventName', 'confirmEventData', 'confirmButtonText', 'cancelButtonText', 'cancelEventName', 'cancelEventData']);
    }

    public function render()
    {
        return view('livewire.confirmation-modal');
    }
}