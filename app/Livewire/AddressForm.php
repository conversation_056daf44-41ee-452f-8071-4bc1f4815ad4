<?php

namespace App\Livewire;

use App\Models\UserContactPlatformInformation;
use Filament\Forms\Components\TextInput;
use \Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use Jeffgreco13\FilamentBreezy\Livewire\MyProfileComponent;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

class AddressForm extends MyProfileComponent
{
    protected string $view = 'livewire.address-form';

    public array $data;
    public $accountTypeKey;
    public $formTitle;

    protected $listeners = ['accountTypeChanged' => 'onAccountTypeChanged'];

    public function onAccountTypeChanged($accountType)
    {
        logger('CHANGED');
        $this->accountTypeKey = $accountType;
        $this->updateDynamicLabels();
    }

    public function updateDynamicLabels()
    {
        $this->formTitle = match ($this->accountTypeKey) {
            'professional' => t('core.company_informations'),
            'other' => t('core.address.establishment_informations'),
            default => t('core.address.private_informations'),
        };
        logger('formTitle' . $this->formTitle);

        // Optionally, trigger a re-render or update other dynamic labels if needed
        $this->dispatch('$refresh');
    }

    public function mount(): void
    {
        $user = auth()->guard()->user();
        $address = $user->address()->first();
        $accountInfo = $user->accountInformation()->first();
        $contactWebsite = UserContactPlatformInformation::getByKey($user->id, 'website');

        // Get account type key (particular, professional, other)
        $accountTypeKey = null;
        $companyName = null;
        if ($accountInfo && $accountInfo->account_type_id) {
            $accountType = \App\Models\AccountType::find($accountInfo->account_type_id);
            $accountTypeKey = $accountType?->name;
            $companyName = $accountInfo->company_name;
        }
        $this->accountTypeKey = $accountTypeKey;
        $this->updateDynamicLabels();

        $this->form->fill([
            'company_name' => $companyName,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'website' => $contactWebsite?->value,
            'address_line_1' => $address?->address_line_1,
            'city' => $address?->city,
            'zip' => $address?->zip,
            'country_code' => $address?->country_code,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->formSchema())
            ->statePath('data');
    }


    public function formSchema(): array
    {
        logger('Building formSchema for AddressForm');
        $isSubUser = auth()->user()->is_sub_user;
        $accountTypeKey = $this->accountTypeKey;
        $companyLabel = match ($accountTypeKey) {
            'professional' => t('core.address.company_name'),
            'other' => t('core.address.establishment_name'),
            default => t('core.address.company_name'),
        };
        $isParticular = $accountTypeKey === 'particular';
        $isPro = $accountTypeKey === 'professional';
        $isOther = $accountTypeKey === 'other';

        return [
            Group::make([
                Grid::make(2)
                    ->schema([
                        // Left column
                        Group::make([
                            TextInput::make('company_name')
                                ->label($companyLabel)
                                ->visible(!$isParticular)
                                ->required($isPro)
                                ->extraAttributes([
                                    'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                ])
                                ->disabled($isSubUser),

                            TextInput::make('email')
                                ->label(t('core.address.email'))
                                ->extraAttributes([
                                    'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                ])
                                ->disabled(),
                            PhoneInput::make('phone_number')
                                ->label(t('core.address.phone'))
                                ->extraAttributes([
                                    'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                ])
                                ->inputNumberFormat(PhoneInputNumberType::E164)
                                ->displayNumberFormat(PhoneInputNumberType::INTERNATIONAL)
                                ->enableIpLookup(true)
                                ->countrySearch(true)
                                ->showFlags(true)
                                ->required(!$isOther)
                                ->reactive()
                                ->strictMode()
                                // ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                //     $set('phone_number.country', $state);
                                // })
                                ->countryStatePath('phone_country')
                                ->disabled($isSubUser),
                            TextInput::make('website')
                                ->label(t('core.address.website'))
                                ->extraAttributes([
                                    'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                ])
                                ->prefix(new HtmlString(view('components.platform-icon', ['platform' => 'website'])->render()))
                                ->disabled($isSubUser),
                        ]),
                        // Right column
                        Group::make([
                            Section::make(t('core.address.address'))
                                ->schema([
                                    TextInput::make('address_line_1')
                                        ->label(t('core.address.street_address'))
                                        ->hiddenLabel()
                                        ->placeholder(t('core.address.street_address'))
                                        ->extraAttributes([
                                            'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                        ])
                                        ->required(!$isParticular)
                                        ->disabled($isSubUser),
                                    TextInput::make('zip')
                                        ->label(t('core.address.zip'))
                                        ->hiddenLabel()
                                        ->placeholder(t('core.address.zip'))
                                        ->extraAttributes([
                                            'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                        ])
                                        ->required(!$isParticular)
                                        ->disabled($isSubUser),
                                    TextInput::make('city')
                                        ->label(t('core.address.city'))
                                        ->hiddenLabel()
                                        ->placeholder(t('core.address.city'))
                                        ->extraAttributes([
                                            'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                        ])
                                        ->required(!$isParticular)
                                        ->disabled($isSubUser),
                                    Country::make('country_code')
                                        ->label(t('core.address.country'))
                                        ->hiddenLabel()
                                        ->placeholder(t('core.address.country'))
                                        ->extraAttributes([
                                            'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                                        ])
                                        ->reactive()
                                        // ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        //     $set('phone_country', $state);
                                        // })
                                        ->required(!$isParticular)
                                        ->disabled($isSubUser),
                                ])
                                ->extraAttributes([
                                    'class' => 'information-address',
                                ])
                        ]),
                    ])
            ])
        ];
    }


    public function submit()
    {
        if(auth()->user()->is_sub_user) return;
        $formState = $this->form->getState();
        $user = auth()->guard()->user();
        $address = $user->address()->first();
        $accountInfo = $user->accountInformation()->first();

        // Save company_name to AccountInformation if not particular
        if ($accountInfo) {
            $accountInfo->company_name = $formState['company_name'] ?? null;
            $accountInfo->save();
        }

        // Save address fields to Address
        $addressData = [
            'address_line_1' => $formState['address_line_1'] ?? null,
            'city' => $formState['city'] ?? null,
            'zip' => $formState['zip'] ?? null,
            'country_code' => $formState['country_code'] ?? null,
        ];
        if ($address) {
            $address->update($addressData);
        } else {
            $user->address()->create($addressData);
        }

        UserContactPlatformInformation::updateOrCreateByKey(
            $user->id,
            'website',
            $formState['website'] ?? null
        );

        $user->update([
            'phone_number' => $formState['phone_number'] ?? $user->phone_number ?? null,
        ]);

        Notification::make()
            ->title(t("core.info.updated_successfully"))
            ->success()
            ->send();
    }

    public static function getSort(): int
    {
        return 2;
    }
}
