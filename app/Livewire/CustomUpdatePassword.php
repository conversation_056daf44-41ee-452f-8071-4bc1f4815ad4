<?php

namespace App\Livewire;

use Jeffgreco13\FilamentBreezy\Livewire\UpdatePassword;

class CustomUpdatePassword extends UpdatePassword
{
    protected string $view = 'livewire.custom-update-password';

    public function render()
    {
        if (auth()->user()?->is_sub_user) {
            return <<<'BLADE'
            <div></div>
            BLADE;
        }

        return parent::render();
    }
    public static function getSort(): int
    {
        return 4;
    }
}
