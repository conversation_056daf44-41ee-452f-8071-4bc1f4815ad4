<?php

namespace App\Livewire;

use App\Constants\PaymentProviderConstants;
use App\Services\PaymentProviders\PaymentService;
use App\Services\PaymentProviders\Stripe\StripeProvider;
use Livewire\Component;
use App\Models\Country;

class PaymentMethodFormModal extends Component
{

    public $clientSecret;
    public $isProcessing = false;
    public $countryList = [];
    public $appName = '';
    protected $listeners = ['openPaymentMethodFormModal'];

    public function mount(PaymentService $paymentService)
    {
        $user = auth()->user();
        $this->countryList = $this->getCountryListPropertyFromDb();
        $this->appName = config('app.name');
        $paymentProviderStrategy = $paymentService->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        /** @var StripeProvider $stripeProvider */
        $stripeProvider = $paymentProviderStrategy;
        $clientSecret = $stripeProvider->getSetupIntentClientSecret($user);
        $this->clientSecret = $clientSecret;
    }

    public function handlePaymentMethodAdded(string $paymentMethodId)
{
    $user = auth()->user();

    $paymentProviderStrategy = app(PaymentService::class)->getPaymentProviderBySlug(
        PaymentProviderConstants::STRIPE_SLUG
    );

    /** @var StripeProvider $stripeProvider */
    $stripeProvider = $paymentProviderStrategy;

    try {
        $stripeProvider->updateDefaultPaymentMethod($user, $paymentMethodId);
        $this->selectedCardId = $paymentMethodId;
        return redirect('/subscriptions');
    } catch (ApiErrorException $e) {
        logger()->error('An error occurred while updating the default card');
        Log::error('An error occurred while updating the default card: ' . $e->getMessage());
    }
}


    public function openPaymentMethodFormModal()
    {
        $this->dispatch('open-modal', id: 'payment-method-form-modal');
    }

    public function getCountryListPropertyFromDb()
    {
        return Country::query()
        ->orderBy('name')
            ->get();
    }


    public function render()
    {
        return view('livewire.payment-method-form-modal');
    }
}
