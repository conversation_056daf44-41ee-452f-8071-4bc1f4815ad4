<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Upload;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProfileAvatarForm extends Component
{
    use WithFileUploads;

    public $avatar;

    public function save()
    {
        $this->validate([
            'avatar' => 'image|max:2048', // 2MB Max
        ]);

        $user = Auth::user();

        // Store the file
        $path = $this->avatar->store('avatars', 'public');

        // Save to uploads table
        Upload::create([
            'url' => $path,
            'name' => $this->avatar->getClientOriginalName(),
            'type' => 'profile_picture',
            'file_size' => $this->avatar->getSize(),
            'resource' => 'user',
            'resource_id' => $user->id,
        ]);

        session()->flash('success', 'Avatar updated!');
        $this->reset('avatar');
        $this->emit('avatarUpdated'); // For Livewire/Filament to refresh avatar
    }

    public static function canView(): bool
    {
        return true;
    }

    public static function getSort(): int
    {
        return 10; // You can adjust the number to order your components
    }


    public function render()
    {
        return view('livewire.profile-avatar-form');
    }
}
