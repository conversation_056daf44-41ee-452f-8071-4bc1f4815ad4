<?php

namespace App\Livewire;

use App\Models\AccountCategory;
use App\Models\AccountType;
use App\Models\AccountInformation;
use App\Constants\AgeRangeEnum;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Jeffgreco13\FilamentBreezy\Livewire\PersonalInfo;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\WithFileUploads;
use Illuminate\Support\Str;

class ExCustomPersonalInfo extends PersonalInfo
{
    use WithFileUploads;

    public $avatar;
    public $avatarUrl;
    public $companyLogoUrl;

    protected string $view = 'livewire.custom-personal-info';

    public function mount(): void
    {
        $this->only[] = 'phone_number';

        $user = auth()->guard()->user();
        $this->avatarUrl = $this->prepareFilePath($user->getFilamentAvatarUrl());
        $this->companyLogoUrl = $this->prepareFilePath($user->getCompanyLogoUrl());

        parent::mount();

        $accountInfo = $user->accountInformation()->first();

        if ($accountInfo) {
            $this->form->fill([
                ...$user->only($this->only),
                // 'avatar_url' => $this->avatarUrl,
                // 'company_logo' => $this->companyLogoUrl,
                'account_type_id' => $accountInfo->account_type_id ? [$accountInfo->account_type_id] : [],
                'professional_category_id' => $accountInfo->account_category_id,
                'other_category_id' => $accountInfo->account_category_id,
                'profession' => $accountInfo->profession,
                'age_range' => $accountInfo->age_range,
                'professional_other_category' => $accountInfo->other_category,
                'other_other_category' => $accountInfo->other_category,
            ]);
        }
    }

    private function prepareFilePath(?string $url): ?string
    {
        return $url ? str_replace('/storage/', '', $url) : null;
    }

    protected function getProfileFormSchema(): array
    {
        $groupFields = Group::make([
            Grid::make(2)->schema([
                FileUpload::make('avatar_url')
                    ->image()
                    ->maxSize(4048)
                    ->disk('public')
                    ->directory('avatars')
                    ->label(t('core.photo'))
                    ->avatar()
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => time() . '_' . $file->getClientOriginalName()
                    ),

                FileUpload::make('company_logo')
                    ->image()
                    ->maxSize(4048)
                    ->disk('public')
                    ->directory('logos')
                    ->label(t('core.company_logo'))
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => time() . '_' . $file->getClientOriginalName()
                    )
                    ->visible(function ($get) {
                        $typeIds = AccountType::pluck('id', 'name')->toArray();
                        return in_array($get('account_type_id')[0] ?? null, [
                            $typeIds['professional'] ?? null,
                            $typeIds['other'] ?? null,
                        ]);
                    })
                    ->panelLayout('compact')
                    ->panelAspectRatio('1:1')
                    ->imageCropAspectRatio('1:1')
                    ->loadingIndicatorPosition('center bottom')
                    ->removeUploadedFileButtonPosition('center bottom')
                    ->uploadButtonPosition('center bottom')
                    ->uploadProgressIndicatorPosition('center bottom')
                    ->extraAttributes([
                        'class' => 'square-avatar-upload max-w-[130px] max-h-[130px]',
                    ])

            ]),
            Grid::make()
                ->columns(2)
                ->schema([
                    TextInput::make('name')
                        ->required()
                        ->label(t('core.name'))
                        ->extraAttributes([
                            'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                        ]),
                    // $this->getNameComponent(),
                    // $this->getEmailComponent()
                ]),
        ]);

        $accountFields = $this->getAccountInfoFields();

        return [$groupFields, ...$accountFields];
    }

    public function getAgeRanges() {
        $ageRanges = collect(AgeRangeEnum::cases())
            ->mapWithKeys(function ($case) {
                $parts = explode('_', str_replace('age_', '', $case->value));
                $from = $parts[0];
                $to = $parts[1] ?? null;

                if ($to === 'plus') {
                    return [
                        $case->value => t('core.age_range_from_to_plus', ['from' => $from]),
                    ];
                }

                return [
                    $case->value => t('core.age_range_from_to', ['from' => $from, 'to' => $to]),
                ];
            });
        return $ageRanges;
    }

    public function getAccountInfoFields(): array
    {
        $accountTypes = AccountType::all()
            ->pluck('name', 'id')
            ->filter()
            ->map(fn ($n) => t('core.account_type.' .$n))
            ->toArray();

        $typeIds = AccountType::pluck('id', 'name')->toArray();
        $professionalTypeId = $typeIds['professional'] ?? null;
        $otherTypeId = $typeIds['other'] ?? null;
        $particularTypeId = $typeIds['particular'] ?? null;

        $categories = AccountCategory::all()->groupBy('account_type_id');
        $professionalCategories = $categories[$professionalTypeId] ?? collect();
        $otherCategories = $categories[$otherTypeId] ?? collect();

        $ageRanges = $this->getAgeRanges();

        return [
            CheckboxList::make('account_type_id')
                ->label(t('core.account_type.label'))
                ->options($accountTypes)
                ->columns(3)
                ->reactive()
                ->disableOptionWhen(fn ($value, $state) => is_array($state) && count($state) === 1 && in_array($value, $state)) // prevent unchecking the only one
                ->afterStateUpdated(function ($state, callable $set) {
                    if (is_array($state) && count($state) > 1) {
                        $set('account_type_id', [end($state)]); // immediately update to only one
                    }
                    // Emit event with the new account type key
                    $typeId = end($state) ?? null;
                    $typeName = $typeId ? AccountType::find($typeId)?->name : null;
                    $this->dispatch('accountTypeChanged', $typeName);
                }),

            // Particular
            Group::make([
                TextInput::make('profession')
                    ->label(t('core.profession'))
                    ->columnSpanFull()
                    ->required(fn ($get) => in_array($particularTypeId, (array) $get('account_type_id') ?? []))
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ]),
                Select::make('age_range')
                    ->label(t('core.age_range'))
                    ->options($ageRanges)
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ])
                    ->columnSpanFull(),
            ])
                ->columns(1)
                ->columnSpanFull()
                ->extraAttributes(['class' => 'max-w-xl'])
                ->visible(fn ($get) => in_array($particularTypeId, (array) $get('account_type_id') ?? [])),

            // Professional
            Group::make([
                Select::make('professional_category_id')
                    ->label(t('core.professional_category'))
                    ->options($professionalCategories->pluck('name', 'id')->filter()->map(fn ($n) => __(ucwords($n))))
                    ->reactive()
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ])
                    ->columnSpanFull(),
                TextInput::make('professional_other_category')
                    ->label(t('core.other'))
                    ->columnSpanFull()
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ])
                    ->visible(fn ($get) =>
                        in_array($professionalTypeId, (array) $get('account_type_id') ?? []) &&
                        ($get('professional_category_id') && optional($professionalCategories->firstWhere('id', $get('professional_category_id')))?->is_other)),
            ])
                ->columns(1)
                ->columnSpanFull()
                ->extraAttributes(['class' => 'max-w-xl'])
                ->visible(fn ($get) => in_array($professionalTypeId, (array) $get('account_type_id') ?? [])),

            // Other
            Group::make([
                Select::make('other_category_id')
                    ->label(t('core.type_of_establishment'))
                    ->options($otherCategories->pluck('name', 'id')->filter()->map(fn ($n) => __(ucwords($n))))
                    ->reactive()
                    ->columnSpanFull()
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ]),
                TextInput::make('other_other_category')
                    ->label(t('core.other'))
                    ->columnSpanFull()
                    ->visible(fn ($get) =>
                        in_array($otherTypeId, (array) $get('account_type_id') ?? []) &&
                        ($get('other_category_id') && optional($otherCategories->firstWhere('id', $get('other_category_id')))?->is_other))
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ]),
            ])
                ->columns(1)
                ->columnSpanFull()
                ->extraAttributes(['class' => 'max-w-xl'])
                ->visible(fn ($get) => in_array($otherTypeId, (array) $get('account_type_id') ?? [])),
        ];
    }

    public function getFormUploadedFiles(string $statePath): ?array
    {
        // Filament passes something like "data.avatar_url" or "data.company_logo" here;
        // adjust the checks if your statePath is different.
        if (Str::endsWith($statePath, 'avatar_url') && $this->avatarUrl) {
            $path = $this->avatarUrl;

            if (Storage::disk('public')->exists($path)) {
                $fullPath = Storage::disk('public')->path($path);

                return [[
                    'name' => basename($path),
                    'size' => filesize($fullPath),
                    'type' => File::mimeType($fullPath),
                    'url'  => request()->getSchemeAndHttpHost() . Storage::url($path),
                ]];
            }
        }

        if (Str::endsWith($statePath, 'company_logo') && $this->companyLogoUrl) {
            $path = $this->companyLogoUrl;

            if (Storage::disk('public')->exists($path)) {
                $fullPath = Storage::disk('public')->path($path);

                return [[
                    'name' => basename($path),
                    'size' => filesize($fullPath),
                    'type' => File::mimeType($fullPath),
                    'url'  => request()->getSchemeAndHttpHost() . Storage::url($path),
                ]];
            }
        }

        return parent::getFormUploadedFiles($statePath);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getProfileFormSchema())
            ->columns(1)
            ->statePath('data');
    }

    public function submit(): void
    {
        $formState = $this->form->getState();

        $avatarPath = $formState['avatar_url'] ?? null;
        $companyLogoPath = $formState['company_logo'] ?? null;

        $this->avatarUrl = $avatarPath;
        $this->companyLogoUrl = $companyLogoPath;

        if ($avatarPath && Storage::disk('public')->exists($avatarPath)) {
            $this->storeUploadRecord($avatarPath, 'profile_picture');
        }

        if ($companyLogoPath && Storage::disk('public')->exists($companyLogoPath)) {
            $this->storeUploadRecord($companyLogoPath, 'company_logo');
        }

        // Update user information
        $user = auth()->guard()->user();
        $user->update([
            'name' => $formState['name'] ?? $user->name ?? '',
        ]);

        // Save account informations
        $typeId = $formState['account_type_id'][0] ?? null;
        $accountCategoryId = $formState['professional_category_id'] ?? $formState['other_category_id'] ?? null;
        $otherCategory = $formState['professional_other_category'] ?? $formState['other_other_category'] ?? null;
        AccountInformation::updateOrCreate(
            ['user_id' => auth()->guard()->id()],
            [
                'account_type_id' => $typeId,
                'account_category_id' => $accountCategoryId,
                'profession' => $formState['profession'] ?? null,
                'age_range' => $formState['age_range'] ?? null,
                'company_name' => null,
                'other_category' => $otherCategory,
            ]
        );

        Notification::make()
            ->title(t('core.profile.update_success_msg'))
            ->success()
            ->send();

        $this->mount();
    }

    protected function storeUploadRecord(string $path, string $type): void
    {
        $fullPath = Storage::disk('public')->path($path);

        \App\Models\Upload::create([
            'url' => $path,
            'name' => basename($path),
            'type' => $type,
            'file_size' => filesize($fullPath),
            'resource' => 'user',
            'resource_id' => auth()->guard()->id(),
        ]);
    }

    public static function getSort(): int
    {
        return 1;
    }

    public static function canView(): bool
    {
        return true;
    }
}
