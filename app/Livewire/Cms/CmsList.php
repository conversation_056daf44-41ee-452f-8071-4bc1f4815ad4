<?php

namespace App\Livewire\Cms;

use App\Services\CmsService;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;

class CmsList extends Component
{
    use WithPagination;

    protected CmsService $cmsService;

    public $cmsData = [];
    public $perPage = 50;
    public $key, $url, $title, $metaDescription, $additionalHeader, $status, $content = '';
    protected $queryString = ['page'];

    public function boot(CmsService $cmsService)
    {
        $this->cmsService = $cmsService;
    }

    public function mount()
    {
        // $this->loadCmsData();
    }

    protected function loadCmsData()
    {
        $currentPage = $this->getPage();
        $this->cmsData = $this->cmsService->getContentManagementSystems([
            'limit' => $this->perPage,
            'offset' => ($currentPage - 1) * $this->perPage,
        ]);
    }

    protected function getPage()
    {
        return $this->paginators['page'] ?? 1;
    }

    public function updated($property)
    {
        if ($property === 'page' || $property === 'paginators.page') {
            $this->loadCmsData();
        }
    }

    public function createCms($data) {
        $response = $this->cmsService->createContentManagementSystem($data);
        if (isset($response['error'])) {
            session()->flash('error', $response['error']);
        } else {
            session()->flash('success', 'CMS created successfully.');
            $this->loadCmsData();
        }
    }

    public function createCmsFromModal() {
        $data = [
            'key' => $this->key,
            'url' => $this->url,
            'title' => $this->title,
            'content' => $this->content,
            'metaDescription' => $this->metaDescription,
            'additionalHeader' => $this->additionalHeader,
            'status' => (int) $this->status,
            'lastChangedBy' => auth()->user()->id,
        ];

        Log::info('Creating CMS with data:', $data);
        Log::debug('Creating CMS with data:', $data);

        dd($data);

        // $this->reset(['key', 'url', 'title', 'metaDescription', 'additionalHeader', 'status', 'content']);
    }

    public function render()
    {
        $this->loadCmsData();
        $cmsDatas = $this->cmsData['data'] ?? [];
        $itemCount = $this->cmsData['meta']['itemCount'] ?? 0;
        return view('livewire.cms.cms-list', ['cmsDatas' => $cmsDatas, 'itemCount' => $itemCount]);
    }
}
