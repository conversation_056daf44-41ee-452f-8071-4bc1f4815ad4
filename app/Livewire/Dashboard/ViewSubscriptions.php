<?php

namespace App\Livewire\Dashboard;

use App\Constants\DiscountConstants;
use App\Constants\PlanPriceTierConstants;
use App\Constants\PlanPriceType;
use App\Constants\SubscriptionStatus;
use App\Filament\Dashboard\Resources\SubscriptionResource;
use App\Filament\Dashboard\Resources\SubscriptionResource\ActionHandlers\DiscardSubscriptionCancellationActionHandler;
use App\Mapper\SubscriptionStatusMapper;
use App\Mapper\TransactionStatusMapper;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Services\InvoiceService;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\HtmlString;
use Livewire\Component;
use Illuminate\Database\Eloquent\Builder;


class ViewSubscriptions extends Component implements HasTable, HasForms, HasActions, HasInfolists
{
    use InteractsWithActions;
    use InteractsWithForms;
    use InteractsWithInfolists;
    use InteractsWithTable;

    public ?Collection $subscriptions;

    // These services will be automatically injected
    protected SubscriptionService $subscriptionService;
    protected SubscriptionStatusMapper $statusMapper;
    public ?Subscription $subscription = null;
    public ?Subscription $subscriptionNext = null;
    public bool $disableBuyBtn = false;
    public bool $disableCancelSubscriptionBtn = false;

    public function boot(
        SubscriptionService $subscriptionService,
        SubscriptionStatusMapper $statusMapper
    ): void {
        $this->subscriptionService = $subscriptionService;
        $this->statusMapper = $statusMapper;
    }

    // When the component loads, fetch the subscriptions
    public function mount(): void
    {
        $this->loadCurrentSubscription();
        $this->loadNextSubscription();
    }

    protected function loadSubscriptions(): void
    {
        $this->subscriptions = Subscription::where('user_id', auth()->id())
            ->where("status", "=", "active")
            ->with(['plan.meter', 'currency', 'discounts'])
            ->orderBy('updated_at', 'desc')
            ->get();
    }

    protected function loadCurrentSubscription(): void
    {
        // KEY CHANGE: The query now finds the first active or trialing subscription.
        $this->subscription = Subscription::where('user_id', auth()->id())
            ->whereIn('status', [
                SubscriptionStatus::ACTIVE->value,
            ])
            ->with(['plan.product', 'plan.meter', 'currency', 'discounts'])
            ->first();
    }

    protected function loadNextSubscription(): void
    {
        // Look for a scheduled subscription that will start after the current one ends
        // This could be a subscription with status 'scheduled' or 'pending'
        // Adjust the status values based on your system's implementation
        $this->subscriptionNext = Subscription::where('user_id', auth()->id())
            ->whereIn('status', [
                SubscriptionStatus::ACTIVE->value,
            ])
            ->where('starts_at', '>', Carbon::now('UTC'))
            ->with(['plan.product', 'plan.meter', 'currency', 'discounts'])
            // ->orderBy('starts_at', 'asc')
            ->first();
        if ($this->subscriptionNext) {
            $this->disableBuyBtn = true; // Disable buy button if a next subscription is scheduled
            $this->disableCancelSubscriptionBtn = true;
        }
    }


    public function table(Table $table): Table
    {
        return $table
            ->query(Transaction::query()->where('user_id', auth()->id()))
            ->columns([
                TextColumn::make('created_at')
                    ->label(__('My invoice'))
                    ->dateTime(config('app.datetime_format')),

                TextColumn::make('amount')->formatStateUsing(
                    fn (string $state, $record) => money(
                        $state,
                        $record->currency->code
                    )
                ),
                TextColumn::make('owner')
                    ->label(__('Plan'))
                    ->getStateUsing(
                        fn (
                            Transaction $record
                        ) => $record->subscription_id !== null
                            ? ($record->subscription->plan?->name ?? '-')
                            : '-'
                    ),
                TextColumn::make('status')
                    ->badge()
                    ->color(
                        fn (
                            Transaction $record,
                            TransactionStatusMapper $mapper
                        ): string => $mapper->mapColor($record->status)
                    )
                    ->formatStateUsing(
                        fn (
                            string $state,
                            TransactionStatusMapper $mapper
                        ) => $mapper->mapForDisplay($state)
                    ),
                // TextColumn::make('owner')
                //     ->label(__('Owner'))
                //     // We removed the URL as it links to an admin page.
                //     ->getStateUsing(
                //         fn (
                //             Transaction $record
                //         ) => $record->subscription_id !== null
                //             ? ($record->subscription->plan?->name ?? '-')
                //             : ($record->order_id !== null
                //                 ? __('Order Nr. ') . $record->order_id)
                //     ),
                TextColumn::make('updated_at')
                    ->label(__('Updated At'))
                    ->dateTime(config('app.datetime_format'))
                    ->sortable(),
            ])
            ->defaultSort('updated_at', 'desc')
            ->actions([
                Tables\Actions\ActionGroup::make([
                    // Tables\Actions\ViewAction::make(),
                    Tables\Actions\Action::make('see-invoice')
                        ->label(__('See Invoice'))
                        ->icon('heroicon-o-document')
                        ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                        ->url(
                            fn (Transaction $record): string => route('invoice.generate', ['transactionUuid' => $record->uuid]),
                            shouldOpenInNewTab: true
                        ),
                     Tables\Actions\Action::make('see-receipt')
                        ->label(__('See Receipt'))
                        ->icon('heroicon-o-document')
                        ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                        ->url(
                            fn (Transaction $record): string => route('receipt.generate', ['transactionUuid' => $record->uuid]),
                            shouldOpenInNewTab: true
                        ),
                    Tables\Actions\Action::make('force-regenerate')
                        ->label(__('Force Regenerate Invoice'))
                        ->color('gray')
                        ->icon('heroicon-o-arrow-path')
                        ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                        ->url(
                            function (Transaction $record): string {
                                return route('invoice.generate', ['transactionUuid' => $record->uuid, 'regenerate' => true]);
                            },
                            shouldOpenInNewTab: true
                        ),
                         Tables\Actions\Action::make('force-regenerate')
                        ->label(__('Force Regenerate Receipt'))
                        ->color('gray')
                        ->icon('heroicon-o-arrow-path')
                        ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                        ->url(
                            function (Transaction $record): string {
                                return route('receipt.generate', ['transactionUuid' => $record->uuid, 'regenerate' => true]);
                            },
                            shouldOpenInNewTab: true
                        ),
                ]),
            ])
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'user',
                'currency',
                'paymentProvider',
                'order',
                'subscription',
                'subscription.plan',
            ]))
            ->bulkActions([

            ]);
    }


    // --- THE MISSING HELPER METHODS ---
    // These methods are called by your Blade view.

    public function requiresVerification(Subscription $subscription): bool
    {
        return $this->subscriptionService->subscriptionRequiresUserVerification($subscription);
    }

    public function isLocal(Subscription $subscription): bool
    {
        return $this->subscriptionService->isLocalSubscription($subscription);
    }

    public function canChangePlan(Subscription $subscription): bool
    {
        return $this->subscriptionService->canChangeSubscriptionPlan($subscription);
    }

    public function canCancel(Subscription $subscription): bool
    {
        return $this->subscriptionService->canCancelSubscription($subscription);
    }

    public function canDiscardCancellation(Subscription $subscription): bool
    {
        return $this->subscriptionService->canDiscardSubscriptionCancellation($subscription);
    }

    public function getStatusColor(string $status): string
    {
        return $this->statusMapper->mapColor($status);
    }

    public function getStatusForDisplay(string $status): string
    {
        return $this->statusMapper->mapForDisplay($status);
    }

    // --- END OF MISSING METHODS ---

    /**
     * This is our new Infolist definition.
     * We copied the schema directly from your SubscriptionResource.
     */
    public function subscriptionInfolist(Infolist $infolist): Infolist
    {
        return $infolist
        ->record($this->subscription) // Use the component's public property
        ->schema([
            // Use a Grid component for the main layout.
            // It will be 1 column on small screens and 2 columns on medium screens and up.
            Grid::make(2)->schema([

                // --- LEFT COLUMN ---
                // We use a Group to contain the main details section.
                Group::make()->schema([
                    Section::make(__('Subscription Details'))
                        ->description(__('View details about your subscription.'))
                        ->schema([
                            ViewEntry::make('status')
                                ->visible(fn (Subscription $record): bool => $record->status === SubscriptionStatus::PAST_DUE->value)
                                ->view('filament.common.infolists.entries.warning', [
                                    'message' => __('Your subscription is past due. Please update your payment details.'),
                                ]),
                            TextEntry::make('plan.name'),
                            TextEntry::make('price')->formatStateUsing(function (string $state, Subscription $record) {
                                $interval = $record->interval->name;
                                if ($record->interval_count > 1) {
                                    $interval = __('every ').$record->interval_count.' '.__(str()->of($record->interval->name)->plural()->toString());
                                }
                                return money($state, $record->currency->code).' / '.$interval;
                            }),
                            TextEntry::make('price_per_unit')
                                ->visible(fn (Subscription $record): bool => $record->price_type === PlanPriceType::USAGE_BASED_PER_UNIT->value && $record->price_per_unit !== null)
                                ->formatStateUsing(fn (string $state, Subscription $record) => money($state, $record->currency->code).' / '.__($record->plan->meter->name)),
                            TextEntry::make('price_tiers')
                                ->visible(fn (Subscription $record): bool => in_array($record->price_type, [PlanPriceType::USAGE_BASED_TIERED_VOLUME->value, PlanPriceType::USAGE_BASED_TIERED_GRADUATED->value]) && $record->price_tiers !== null)
                                ->getStateUsing(fn (Subscription $record) => $this->formatPriceTiers($record)),
                            TextEntry::make('ends_at')->dateTime(config('app.datetime_format'))->label(__('Next Renewal'))->visible(fn (Subscription $record): bool => ! $record->is_canceled_at_end_of_cycle),
                            TextEntry::make('status')
                                ->color(fn (Subscription $record): string => $this->getStatusColor($record->status))
                                ->badge()
                                ->formatStateUsing(fn (string $state): string => $this->getStatusForDisplay($state)),
                            TextEntry::make('is_canceled_at_end_of_cycle')
                                ->label(__('Renews automatically'))
                                ->visible(fn (Subscription $record): bool => $this->canCancel($record))
                                ->icon(fn ($state) => boolval($state) ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                                ->formatStateUsing(fn ($state) => boolval($state) ? __('No') : __('Yes')),

                            TextEntry::make('cancellation_notice')
                                ->label(__('Status'))
                                ->hidden(fn (Subscription $record): bool => !$record->is_canceled_at_end_of_cycle)
                                ->color('warning')
                                ->icon('heroicon-o-exclamation-triangle')
                                ->getStateUsing(fn (Subscription $record): string => __('Set to cancel on: ') . $record->ends_at),

                            Actions::make([
                                InfolistAction::make('cancelSubscription')
                                    ->label(__('Cancel Subscription'))
                                    ->icon('heroicon-m-x-circle')
                                    ->color('danger')
                                    ->url(fn (Subscription $record): string => SubscriptionResource::getUrl('cancel', ['record' => $record->uuid]))
                                    ->visible(fn (Subscription $record): bool => !$record->is_canceled_at_end_of_cycle && $this->canCancel($record))
                                    ->disabled(fn (Subscription $record): bool => $this->disableCancelSubscriptionBtn),
                                InfolistAction::make('reactivateSubscription')
                                    ->label(__('Reactivate Subscription'))
                                    ->icon('heroicon-m-arrow-path')
                                    ->color('success')
                                    ->action(function ($record, DiscardSubscriptionCancellationActionHandler $handler) {
                                        $handler->handle($record);
                                    })
                                    ->visible(fn (Subscription $record): bool => $record->is_canceled_at_end_of_cycle && $this->canDiscardCancellation($record)),
                            ])->alignment(Alignment::Start),
                        ]),
                ]),

                // --- RIGHT COLUMN ---
                // We use another Group to stack the Features and Discount sections.
                Group::make()->schema([
                    Section::make(__('Features'))
                        ->visible(fn (?Subscription $record): bool => !empty($record?->plan?->product?->features))
                        ->schema([
                            ViewEntry::make('features')
                                ->view('infolists.entries.features-list')
                                ->columnSpanFull(),
                        ]),
                    Section::make(__('Discount Details'))
                        ->hidden(fn (Subscription $record): bool => $record->discounts->isEmpty() ||
                            ($record->discounts[0]->valid_until !== null && $record->discounts[0]->valid_until < now())
                        )
                        ->description(__('View details about your discount.'))
                        ->schema([
                            TextEntry::make('discounts.amount')
                                ->label(__('Discount Amount'))
                                ->formatStateUsing(function (string $state, Subscription $record) {
                                    if ($record->discounts[0]->type === DiscountConstants::TYPE_PERCENTAGE) {
                                        return $state.'%';
                                    }
                                    return money($state, $record->discounts[0]->code);
                                }),
                            TextEntry::make('discounts.valid_until')
                                ->dateTime(config('app.datetime_format'))
                                ->visible(fn (Subscription $record): bool => $record->discounts[0]->valid_until !== null)
                                ->label(__('Valid Until')),
                        ]),
                ]),
            ]),
        ]);
    }


    public function subscriptionInfolistNext(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->subscriptionNext)
            ->schema([
                Grid::make(2)->schema([
                    Group::make()->schema([
                        Section::make(__('Next Subscription Details'))
                            ->description(__('View details about your upcoming subscription.'))
                            ->schema([
                                TextEntry::make('status')
                                    ->label(__('Status'))
                                    ->formatStateUsing(fn (Subscription $record): string => __('This subscription will start automatically when your current subscription ends.')),
                                TextEntry::make('plan.name'),
                                TextEntry::make('price')->formatStateUsing(function (string $state, Subscription $record) {
                                    $interval = $record->interval->name;
                                    if ($record->interval_count > 1) {
                                        $interval = __('every') . ' ' . $record->interval_count . ' ' . __(str()->of($record->interval->name)->plural()->toString());
                                    }
                                    return money($state, $record->currency->code) . ' / ' . $interval;
                                }),
                                TextEntry::make('price_per_unit')
                                    ->visible(fn (Subscription $record): bool => $record->price_type === PlanPriceType::USAGE_BASED_PER_UNIT->value && $record->price_per_unit !== null)
                                    ->formatStateUsing(fn (string $state, Subscription $record) => money($state, $record->currency->code) . ' / ' . __($record->plan->meter->name)),
                                TextEntry::make('price_tiers')
                                    ->visible(fn (Subscription $record): bool => in_array($record->price_type, [PlanPriceType::USAGE_BASED_TIERED_VOLUME->value, PlanPriceType::USAGE_BASED_TIERED_GRADUATED->value]) && $record->price_tiers !== null)
                                    ->getStateUsing(fn (Subscription $record) => $this->formatPriceTiers($record)),
                                TextEntry::make('starts_at')
                                    ->dateTime(config('app.datetime_format'))
                                    ->label(__('Starts At'))
                                    ->visible(fn (Subscription $record): bool => $record->starts_at !== null),
                            ]),
                    ]),

                    Group::make()->schema([
                        Section::make(__('Features'))
                            ->visible(fn (?Subscription $record): bool => !empty($record?->plan?->product?->features))
                            ->schema([
                                ViewEntry::make('features')
                                    ->view('infolists.entries.features-list')
                                    ->columnSpanFull(),
                            ]),
                        Section::make(__('Discount Details'))
                            ->hidden(fn (Subscription $record): bool => $record->discounts->isEmpty() ||
                                ($record->discounts[0]->valid_until !== null && $record->discounts[0]->valid_until < now())
                            )
                            ->description(__('View details about your discount.'))
                            ->schema([
                                TextEntry::make('discounts.amount')
                                    ->label(__('Discount Amount'))
                                    ->formatStateUsing(function (string $state, Subscription $record) {
                                        if ($record->discounts[0]->type === DiscountConstants::TYPE_PERCENTAGE) {
                                            return $state . '%';
                                        }
                                        return money($state, $record->discounts[0]->code);
                                    }),
                                TextEntry::make('discounts.valid_until')
                                    ->dateTime(config('app.datetime_format'))
                                    ->visible(fn (Subscription $record): bool => $record->discounts[0]->valid_until !== null)
                                    ->label(__('Valid Until')),
                            ]),
                    ]),
                ]),
            ]);
    }


    // This method defines all the interactive modals for our component
    public function getActions(): array
    {
        return [
            $this->getChangePlanAction(),
            $this->getCancelAction(),
            $this->getDiscardCancellationAction(),
        ];
    }

    protected function getChangePlanAction(): Action
    {
        return Action::make('changePlan')
            ->label(__('Change Plan'))
            ->icon('heroicon-o-rocket-launch')
            ->form([
                Select::make('new_plan_id')
                    ->label(__('New Plan'))
                    ->options(function (array $arguments) {
                        $subscription = Subscription::find($arguments['record']);
                        return Plan::where('id', '!=', $subscription->plan_id)
                            ->where('is_active', true)
                            ->pluck('name', 'id');
                    })
                    ->required(),
            ])
            ->action(function (array $data, array $arguments) {
                $subscription = Subscription::find($arguments['record']);
                $newPlan = Plan::find($data['new_plan_id']);
                try {
                    $this->subscriptionService->changePlan($subscription, $newPlan);
                    Notification::make()->title(__('Plan Changed Successfully'))->success()->send();
                } catch (Exception $e) {
                    Notification::make()->title(__('Error Changing Plan'))->body($e->getMessage())->danger()->send();
                }
                $this->loadSubscriptions(); // Refresh data
            });
    }

    protected function getCancelAction(): Action
    {
        return Action::make('cancel')
            ->label(__('Cancel Subscription'))
            ->icon('heroicon-m-x-circle')
            ->color('danger')
            ->requiresConfirmation()
            ->modalHeading(__('Cancel Subscription'))
            ->modalDescription(__('Are you sure? Your subscription will remain active until the end of the current billing period.'))
            ->form([
                Textarea::make('cancellation_reason')->label(__('Reason for cancellation (optional)')),
            ])
            ->action(function (array $data, array $arguments) {
                $subscription = Subscription::find($arguments['record']);
                try {
                    $this->subscriptionService->cancel($subscription, $data['cancellation_reason'] ?? null);
                    Notification::make()->title(__('Subscription Canceled'))->success()->send();
                } catch (Exception $e) {
                    Notification::make()->title(__('Error Canceling Subscription'))->body($e->getMessage())->danger()->send();
                }
                $this->loadSubscriptions(); // Refresh data
            });
    }

    protected function getDiscardCancellationAction(): Action
    {
        return Action::make('discardCancellation')
            ->label(__('Discard Cancellation'))
            ->icon('heroicon-m-check-circle')
            ->color('success')
            ->requiresConfirmation()
            ->action(function (array $arguments) {
                $subscription = Subscription::find($arguments['record']);
                try {
                    $this->subscriptionService->discardCancellation($subscription);
                    Notification::make()->title(__('Cancellation Discarded'))->body(__('Your subscription will now renew as scheduled.'))->success()->send();
                } catch (Exception $e) {
                    Notification::make()->title(__('Action Failed'))->body($e->getMessage())->danger()->send();
                }
                $this->loadSubscriptions(); // Refresh data
            });
    }

    // Helper methods to keep the view clean
    public function formatInterval(Subscription $record): string
    {
        $interval = $record->interval->name;
        if ($record->interval_count > 1) {
            $interval = $record->interval_count . ' ' . __(str()->of($record->interval->name)->plural()->toString());
        }
        return $interval;
    }

    public function formatPriceTiers(Subscription $record): HtmlString
    {
        $start = 0;
        $unitMeterName = $record->plan->meter->name;
        $currencyCode = $record->currency->code;
        $output = '';
        $startingPhrase = __('From');
        foreach ($record->price_tiers as $tier) {
            $output .= $startingPhrase . ' ' . $start . ' - ' . $tier[PlanPriceTierConstants::UNTIL_UNIT] . ' ' . __(str()->plural($unitMeterName)) . ' → ' . money($tier[PlanPriceTierConstants::PER_UNIT], $currencyCode) . ' / ' . __($unitMeterName);
            if ($tier[PlanPriceTierConstants::FLAT_FEE] > 0) {
                $output .= ' + ' . money($tier[PlanPriceTierConstants::FLAT_FEE], $currencyCode);
            }
            $start = intval($tier[PlanPriceTierConstants::UNTIL_UNIT]) + 1;
            $output .= '<br>';
            if ($record->price_type === PlanPriceType::USAGE_BASED_TIERED_GRADUATED->value) {
                $startingPhrase = __('Next');
            }
        }
        return new HtmlString($output);
    }

    public function render()
    {
        return view('livewire.dashboard.view-subscriptions');
    }
}
