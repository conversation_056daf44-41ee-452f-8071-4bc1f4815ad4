<?php

namespace App\Livewire;

use App\Models\MongoDatabaseNotification;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class NotificationsCount extends Component
{
    public int $unreadCount = 0;

    public function mount(): void
    {
        $this->updateUnreadCount();
    }

    public function render()
    {
        return view('livewire.notifications-count', [
            'count' => $this->unreadCount,
        ]);
    }

    public function updateUnreadCount(): void
    {
        $this->unreadCount = $this->getBaseQuery()->unread()->count();
    }

    /* Optional: keep the count fresh */
    public function getListeners()
    {
        return [
            "echo-private:App.Models.User." . auth()->id() . ",.Illuminate\\Notifications\\Events\\BroadcastNotificationCreated" => '$refresh',
            'notification-read' => '$refresh',
        ];
    }

    private function getBaseQuery()
    {
        return MongoDatabaseNotification::where('notifiable_id', Auth::id())
            ->where('notifiable_type', Auth::user()->getMorphClass());
    }
}
