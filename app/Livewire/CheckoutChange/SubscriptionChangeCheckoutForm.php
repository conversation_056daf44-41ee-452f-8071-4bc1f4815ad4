<?php

namespace App\Livewire\CheckoutChange;

use App\Constants\CheckoutStepEnum;
use App\Constants\PaymentProviderConstants;
use Livewire\Component;

use App\Exceptions\LoginException;
use App\Exceptions\NoPaymentProvidersAvailableException;
use App\Exceptions\SubscriptionCreationNotAllowedException;
use App\Services\CalculationService;
use App\Services\CheckoutService;
use App\Services\DiscountService;
use App\Services\LoginService;
use App\Services\PaymentProviders\PaymentService;
use App\Services\PlanService;
use App\Services\SessionService;
use App\Services\SubscriptionService;
use App\Services\UserService;
use App\Validator\LoginValidator;
use App\Validator\RegisterValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use App\Models\Country;
use App\Models\Address;
use App\Services\CartService;

class SubscriptionChangeCheckoutForm extends CheckoutChangeForm
{
    private PlanService $planService;

    private SessionService $sessionService;

    private CalculationService $calculationService;

    private SubscriptionService $subscriptionService;
    private CartService $cartService;

    public int $currentStep = 1;

    public $country = 'Madagascar';

    public $countryId;

    public $phoneCode = '+261';

    public $phoneNumber = '';

    public $name;

    public $email;

    public $city;

    public $isAdmin;

    public bool $loadingElementInCheckout = false;

    public $address;

    public $countryListFromDb;

    public $cart = null;

    public function boot(
        PlanService $planService,
        SessionService $sessionService,
        CalculationService $calculationService,
        SubscriptionService $subscriptionService,
        CartService $cartService,
        Request $request
    ) {
        $this->planService = $planService;
        $this->sessionService = $sessionService;
        $this->calculationService = $calculationService;
        $this->subscriptionService = $subscriptionService;
        $this->cartService = $cartService;
        $step = $request->query('step');

        if ($step && is_numeric($step) && $step >= 1 && $step <= 3) {
            $this->currentStep = (int)$step;
            $user = auth()->user();
            if(!$user && $this->currentStep == 3){
                $this->currentStep = 2;
            }

            // Update cart step if cart exists
            $this->cart = $this->cartService->getCartFromCookie();
            if ($this->cart) {
                $this->cartService->updateCartStep($this->cart, $this->currentStep);
            }
        }

    }


    public function addPhone()
    {
        $this->phoneNumbers[] = ['code' => '+261', 'number' => ''];
    }

    public function getCountryListPropertyFromDb()
    {
        return Country::query()
        ->orderBy('name')
            ->get();
    }


    public function removePhone($index)
    {
        unset($this->phoneNumbers[$index]);
        $this->phoneNumbers = array_values($this->phoneNumbers);
    }

    public function render(PaymentService $paymentService)
    {
        $subscriptionCheckoutDto = $this->sessionService->getSubscriptionCheckoutDto();
        $planSlug = $subscriptionCheckoutDto->planSlug;

        $plan = $this->planService->getActivePlanBySlug($planSlug);

        $totals = $this->calculationService->calculatePlanTotals(
            auth()->user(),
            $planSlug,
            $subscriptionCheckoutDto?->discountCode,
        );

        $canUserHaveSubscriptionTrial = $this->subscriptionService->canUserHaveSubscriptionTrial(auth()->user());

        $user = auth()->user();
        if($user){
            $this->name = $user['name'];
            $this->email = $user['email'];
            $this->isAdmin = $user['is_admin'];
            if ($user->address) {
                $this->countryId = $user->address->country_id;
                if($user->address->country){
                    $this->country = $user->address->country->name;
                }
                $this->address = $user->address->address_line_1;
                $this->city = $user->address->city;
                $this->phoneCode = $user->address->country_code;
                $this->phoneNumber = $user->address->phone;
            }
        }
        $subscription = $this->subscriptionService->findActiveUserSubscription($user['id']);
        $currentPlan = $subscription->plan;
        $paymentProcessParams = $this->subscriptionService->getPaymentProcessParams($plan, $subscription);
        $accounting = $this->subscriptionService->calculTotal($paymentProcessParams, $plan, $subscription->last_cart->plan_json);
        $statePayment = $this->subscriptionService->getCheckoutActionType($plan, $subscription);
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret_key'));

        /** @var StripeProvider $stripePaymentProvider */
        $stripePaymentProvider = $paymentService->getPaymentProviderBySlug(
            PaymentProviderConstants::STRIPE_SLUG
        );
        $oldSubscription = $this->subscriptionService->findCurrentActiveUserSubscription($user->id);
        $stripeSessionArgs =  $stripePaymentProvider->generateEmbeddedSessionArgs(
                $paymentProcessParams,
                $this->cart,
                $user,
                $plan,
                $oldSubscription,
        );
        $checkoutSession = $stripe->checkout->sessions->create( $stripeSessionArgs);

        $clientSecret = $checkoutSession->client_secret;

        $priceValue = 0;
        foreach ($plan->prices as $price) {
            if ($price->currency_id == 30 && $price->getOriginal('price')) {
                $priceValue = $price->getOriginal('price');
                break;
            }
        }

        $this->countryListFromDb = $this->getCountryListPropertyFromDb();

        return view('livewire.checkout-change.subscription-change-checkout-form', [
            'userExists' => $this->userExists($this->email),
            'paymentProviders' => $this->getPaymentProviders(
                $paymentService,
                ! $canUserHaveSubscriptionTrial,
            ),
            'stripePublishableKey' => config('services.stripe.publishable_key'),
            'plan' => $plan,
            'priceValue' => $priceValue,
            'currentPlan' => $currentPlan,
            'statePayment' => $statePayment,
            'accounting' => $accounting,
            'user' => $user->getOriginal(),
            'clientSecret' => $clientSecret,
            'totals' => $totals,
            'isAdmin' => $this->isAdmin,
            'isTrialSkipped' => ! $canUserHaveSubscriptionTrial,
        ]);
    }


    public function saveUserAddress()
    {
        $user = auth()->user();

        $existingAddress = Address::where('user_id', $user->id)->first();

        if ($existingAddress) {
            $existingAddress->update([
                'country_id'   => $this->countryId,
                'address_line_1' => $this->address,
                'city'           => $this->city,
                'phone'          => $this->phoneNumber,
            ]);
        } else {
            Address::create([
                'user_id'        => $user->id,
                'country_id'   => $this->countryId,
                'address_line_1' => $this->address,
                'city'           => $this->city,
                'phone'          => $this->phoneNumber,
            ]);
        }



        return redirect()->route('checkout.cart_wizard', ['stepSlug' => CheckoutStepEnum::PAYMENT->value]) ;
    }
}
