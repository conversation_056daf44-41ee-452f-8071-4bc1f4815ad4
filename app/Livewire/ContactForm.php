<?php

namespace App\Livewire;

use App\Models\ContactPlatform;
use App\Models\UserContactPlatformInformation;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Jeffgreco13\FilamentBreezy\Livewire\MyProfileComponent;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;
use Illuminate\Support\HtmlString;

class ContactForm extends MyProfileComponent
{
    protected string $view = 'livewire.contact-form';

    public array $data;

    private $key_prefix = 'contact_';

    public function mount(): void
    {
        $user = auth()->user();
        $contacts = $user->contacts()->get();

        if (count($contacts) > 0) {
            $defaultValue = [];
            foreach ($contacts as $contact) {
                $inputKey = $this->key_prefix . $contact['contact_platform_id'];
                $defaultValue[$inputKey] = $contact['value'];
            }
            $this->form->fill($defaultValue);
        }
    }

    public function form(Form $form): Form
    {
        $contactPlatforms = ContactPlatform::where('key', '!=', 'website')->get()->toArray();

        // Group platforms by type
        $groupedPlatforms = collect($contactPlatforms)->groupBy('type');
        $isSubUser = auth()->user()->is_sub_user;
        $formGroups = [];
        foreach ($groupedPlatforms as $type => $platforms) {
            $contactFields = [];
            foreach ($platforms as $platform) {
                $inputKey = $this->key_prefix . $platform['id'];
                $contactFields[] = TextInput::make($inputKey)
                    ->label(t($platform['label']))
                    // ->helperText(__($platform['type']))
                    ->extraAttributes([
                        'class' => 'text-lg w-full !rounded border-[1px] !border-primary_ui_high-900 focus-within:!ring-[1px] focus-within:!border-[-px] focus-within:!ring-primary_ui_high-900 focus:!border-primary_ui_high-900 ',
                    ])
                    ->prefix(new HtmlString(view('components.platform-icon', ['platform' => $platform['key']])->render()))
                    ->columnSpan(1)->disabled($isSubUser);
            }

            $formGroups[] = Section::make(t("core.account.".$type))
                ->description(t('core.description.contact_details',['type'=> $type]))
                ->schema($contactFields)
                ->columns(2);
        }

        return $form
            ->schema($formGroups)
            ->statePath('data');
    }

    public function submit()
    {
        if(auth()->user()->is_sub_user) return;
        $data = $this->form->getState();

        $user = auth()->user();

        // Get existing contacts indexed by contact_platform_id for faster access
        $existingContacts = $user->contacts->keyBy('contact_platform_id');

        foreach ($data as $key => $value) {
            if (!$value) {
                continue;
            }

            // Extract platform ID (assuming you prefixed the key with something like "contact_" and need to strip it)
            $platformId = str_replace($this->key_prefix, '', $key);

            if ($existingContacts->has($platformId)) {
                // Update existing contact
                $contact = $existingContacts->get($platformId);
                $contact->update(['value' => $value]);
            } else {
                // Create new contact
                UserContactPlatformInformation::create([
                    'value' => $value,
                    'user_id' => $user->id,
                    'contact_platform_id' => $platformId,
                ]);
            }
        }

        Notification::make()
            ->title(__('Contact Saved'))
            ->success()
            ->send();
    }

    public static function getSort(): int
    {
        return 3;
    }
}
