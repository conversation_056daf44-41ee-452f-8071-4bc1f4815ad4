<?php

namespace App\Livewire;

use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class LogoutModal extends Component implements HasForms, HasActions
{
    use InteractsWithActions;
    use InteractsWithForms;

    public function LogoutAction(): Action
    {
        return Action::make('logout')
            ->label(__('Sign out'))
            // ->requiresConfirmation()
            ->icon('heroicon-o-arrow-left-on-rectangle')
            ->color('gray')
            ->extraAttributes(['class' => 'w-full'])
            ->action(fn () => $this->logout());
    }

    public function logout()
    {
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();
        return redirect()->route('login');
    }

    public function render()
    {
        return <<<'HTML'
            <div>
                {{ $this->LogoutAction() }}
            </div>
        HTML;
    }
}
