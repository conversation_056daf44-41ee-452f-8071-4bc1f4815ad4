<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class NotificationBell extends Component
{
    public $unreadNotificationsCount = 0;
    public $notifications = [];
    public bool $showSheet = false;

    public function handleNewNotification($event)
    {
        Log::info('[NotificationBell] handleNewNotification called with event:', $event);

        if (is_array($event) && isset($event['id'])) {
            // Log::info('sdlkfjsladjfklasjdf  '. (object) $event["auth"]);

            $toasterDetail = [
                'id' => $event['id'],
                'type' => $event['type'] ?? 'info',
                'title' => $event['title'] ?? 'New Notification',
                'message' => $event['message'] ?? 'You have a new update.',
                'duration' => $event['toaster_duration'] ?? 5000,
            ];
            $this->dispatch('show-toaster-notification', detail: $toasterDetail);
        } else {
            $this->dispatch('show-toaster-notification', detail: [
                'id' => uniqid('ev-toaster-'), 'type' => 'info', 'title' => 'New Update', 'message' => 'A new event occurred.'
            ]);
        }

        $this->loadNotifications();
    }

    protected function getListeners()
    {
        if (!Auth::check()) {
            return ['refreshNotifications' => 'loadNotifications'];
        }

        $userId = Auth::id();
        if (!$userId) {
            return ['refreshNotifications' => 'loadNotifications'];
        }

        $channelName = 'App.Models.User.' . $userId;

        $eventName = '.' . \Illuminate\Notifications\Events\BroadcastNotificationCreated::class;

        Log::info('[NotificationBell] Setting up Echo listener for channel: private-' . $channelName . ' and event: ' . $eventName);

        return [
            'echo-private:' . $channelName . ',' . $eventName => 'handleNewNotification',
            'refreshNotifications' => 'loadNotifications',
        ];
    }

    public function mount()
    {
        $this->loadNotifications();
    }

    public function loadNotifications()
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if ($user) {
                $this->notifications = $user->notifications()->latest()->take(20)->get();
                $this->unreadNotificationsCount = $user->unreadNotifications()->count();
            } else {
                $this->resetData();
            }
        } else {
            $this->resetData();
        }
    }

    private function resetData()
    {
        $this->notifications = [];
        $this->unreadNotificationsCount = 0;
    }

    public function toggleSheet()
    {
        $this->showSheet = !$this->showSheet;
        if ($this->showSheet) {
            $this->loadNotifications();
        }
    }

    public function markAsRead(string $notificationId)
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if ($user) {
                $notification = $user->notifications()->find($notificationId);
                if ($notification) {
                    $notification->markAsRead();
                    $this->loadNotifications();
                }
            }
        }
    }

    public function markAllAsRead()
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if ($user) {
                $unreadNotifications = $user->unreadNotifications()->get();
                if ($unreadNotifications->isNotEmpty()) {
                    foreach ($unreadNotifications as $notification) {
                        $notification->markAsRead();
                    }
                }
                $this->loadNotifications();
            }
        }
    }

    public function dismissNotification(string $notificationId)
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if ($user) {
                $notification = $user->notifications()->find($notificationId);
                if ($notification) {
                    $notification->delete();
                    $this->loadNotifications();
                }
            }
        }
    }

    public function clearAllNotifications()
    {
        if (Auth::check()) {
            /** @var \App\Models\User $user */
            $user = Auth::user();
            if ($user) {
                $user->notifications()->delete();
                $this->loadNotifications();
            }
        }
    }

    public function getNotificationIcon(string $type): string
    {
        return match ($type) {
            'success' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'error' => 'heroicon-o-x-circle',
            default => 'heroicon-o-information-circle',
        };
    }

    public function getNotificationIconColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'text-green-500 dark:text-green-400',
            'warning' => 'text-yellow-500 dark:text-yellow-400',
            'error' => 'text-red-500 dark:text-red-400',
            default => 'text-blue-500 dark:text-blue-400',
        };
    }
     public function getNotificationIconBgColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'bg-green-500/10',
            'warning' => 'bg-yellow-500/10',
            'error' => 'bg-red-500/10',
            default => 'bg-blue-500/10',
        };
    }

    public function getNotificationBorderColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'border-green-500 dark:border-green-600',
            'warning' => 'border-yellow-500 dark:border-yellow-600',
            'error' => 'border-red-500 dark:border-red-600',
            default => 'border-blue-500 dark:border-blue-600',
        };
    }


    public function render()
    {
        return view('livewire.notification-bell');
    }
}
