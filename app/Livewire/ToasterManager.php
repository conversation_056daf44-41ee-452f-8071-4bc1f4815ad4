<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Log;
use Livewire\Attributes\On; // For Livewire v3 event listening
use Livewire\Component;

class ToasterManager extends Component
{
    public $toasters = []; // Array of toaster data objects

    // Listen for a global browser event (or a Livewire event from another component)
    #[On('show-toaster-notification')]
    public function addToast($detail) // $detail comes from the dispatched event
    {
        // Ensure each toast has a unique ID for Livewire's keying and Alpine's management
        $toastId = $detail['id'] ?? 'toast-' . uniqid();
        $newToast = [
            'id' => $toastId,
            'type' => $detail['type'] ?? 'info',
            'title' => $detail['title'] ?? '',
            'message' => $detail['message'] ?? 'Notification',
            'duration' => $detail['duration'] ?? 5000, // Default 5 seconds
        ];

        // Add to the beginning of the array so new toasters appear on top
        array_unshift($this->toasters, $newToast);

        // Limit the number of visible toasters (optional)
        if (count($this->toasters) > 5) { // Max 5 toasters
            array_pop($this->toasters); // Remove the oldest from the end
        }
    }

    /**
     * Called by Alpine.js from an individual toaster when its close animation finishes.
     */
    public function removeToast($toastId)
    {
        $this->toasters = array_filter($this->toasters, function ($toast) use ($toastId) {
            return $toast['id'] !== $toastId;
        });
        // Re-index array to prevent issues if Livewire relies on numeric keys for @foreach
        $this->toasters = array_values($this->toasters);
    }

    /**
     * This method is a bit of a workaround to pass specific toaster data
     * to the Alpine component within the @foreach loop, as direct @js()
     * in x-init might not always re-evaluate perfectly with Livewire's diffing.
     * Alternatively, you can pass all data via @js in the x-data attribute itself.
     */
    public function getToasterDataForAlpine($toastId)
    {
        foreach ($this->toasters as $toast) {
            if ($toast['id'] === $toastId) {
                return $toast;
            }
        }
        return null; // Should not happen if ID is correct
    }

    public function getNotificationIcon(string $type): string
    {
        return match ($type) {
            'success' => 'heroicon-o-check-circle',
            'warning' => 'heroicon-o-exclamation-triangle',
            'error' => 'heroicon-o-x-circle',
            default => 'heroicon-o-information-circle',
        };
    }

    public function getNotificationIconColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'text-green-500 dark:text-green-400',
            'warning' => 'text-yellow-500 dark:text-yellow-400',
            'error' => 'text-red-500 dark:text-red-400',
            default => 'text-blue-500 dark:text-blue-400',
        };
    }
     public function getNotificationIconBgColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'bg-green-500/10',
            'warning' => 'bg-yellow-500/10',
            'error' => 'bg-red-500/10',
            default => 'bg-blue-500/10',
        };
    }

    public function getNotificationBorderColorClass(string $type): string
    {
        return match ($type) {
            'success' => 'border-green-500 dark:border-green-600',
            'warning' => 'border-yellow-500 dark:border-yellow-600',
            'error' => 'border-red-500 dark:border-red-600',
            default => 'border-blue-500 dark:border-blue-600',
        };
    }

    public function render()
    {
        return view('livewire.toaster-manager');
    }
}
