<?php

namespace App\Livewire;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Jeffgreco13\FilamentBreezy\Livewire\MyProfileComponent;
use Parfaitementweb\FilamentCountryField\Forms\Components\Country;

class PublicInfoForm extends MyProfileComponent
{
    protected string $view = 'livewire.public-info-form';

    public array $data;

    public function mount(): void
    {
        $user = auth()->user();
        if ($user) {
            $this->form->fill([
                'name' => $user->name,
            ]);
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('profile_picture')
                    ->label(__('Profile Picture')),
                TextInput::make('name')
                    ->label(__('Name')),
            ])
            ->statePath('data');
    }

    public function submit()
    {
        $data = $this->form->getState();

        $user = auth()->user();

        if ($user) {
            $user->update($data);
        }

        Notification::make()
            ->title(__('Public Information Saved'))
            ->success()
            ->send();
    }
}
