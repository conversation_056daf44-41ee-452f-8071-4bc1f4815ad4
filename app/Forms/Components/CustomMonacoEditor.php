<?php

namespace App\Forms\Components; // Changed namespace

use Closure;
use Filament\Forms\Components\Field;
use Illuminate\Support\Facades\Log; // For logging if needed

class CustomMonacoEditor extends Field
{
    // Default values directly, or you can create your own config/custom-monaco-editor.php
    // and read from it in setUp() if you prefer.
    public bool | Closure $showPlaceholder = true;
    public bool | Closure $showLoader = true;
    public bool | Closure $automaticLayout = true;
    public int | Closure $lineNumbersMinChars = 3;
    public string | Closure $fontSize = '15px';
    public string | Closure $language = 'html';
    public string | Closure $placeholderText = 'Your code here...';
    public string | Closure $previewHeadEndContent = '';
    public array | Closure $previewBodyAttributes = ['class' => ''];
    public string | Closure $previewBodyStartContent = '';
    public string | Closure $previewBodyEndContent = '';
    public bool | Closure $enablePreview = true;
    public bool | Closure $showFullScreenToggle = true;
    public string | Closure $theme = 'blackboard'; // Default theme name
    public string | Closure $height = '500px';
    public string | Closure $monacoId = '1';

    // Custom themes would be defined here or loaded from a custom config
    // For simplicity, we'll just use the 'blackboard' example.
    // If you need multiple themes, you'll need a config similar to the plugin.
    protected array $themes = [
        'blackboard' => [ // Example theme definition
            'base' => 'vs-dark',
            'inherit' => true,
            'rules' => [/* ... rules ... */],
            'colors' => [
                "editor.foreground" => "#F8F8F8",
                "editor.background" => "#0C1021",
                // ... other colors ...
            ]
        ],
        // Add other themes here if needed
    ];

    protected string $view = 'forms.components.custom-monaco-editor'; // <<< Changed view path


    // public function getFieldWrapperView(): string
    // {
    //     return 'filament-forms::components.field-wrapper';
    // }

    public function setUp(): void
    {
        parent::setUp();

        $this->showPlaceholder = config('filament-monaco-editor.general.show-placeholder', true);
        $this->showLoader = config('filament-monaco-editor.general.show-loader', true);
        $this->automaticLayout = config('filament-monaco-editor.general.automatic-layout', true);
        $this->lineNumbersMinChars = config('filament-monaco-editor.general.line-numbers-min-chars', 3);
        $this->fontSize = config('filament-monaco-editor.general.font-size', '15px');
        $this->language = config('filament-monaco-editor.general.language', 'html');
        $this->placeholderText = config('filament-monaco-editor.general.placeholder-text', 'Your code here...');
        $this->enablePreview = config('filament-monaco-editor.general.enable-preview', true);
        $this->showFullScreenToggle = config('filament-monaco-editor.general.show-full-screen-toggle', true);
        $this->theme = config('filament-monaco-editor.general.default-theme', 'blackboard-light');
    }

    public function editorTheme()
    {
        $currentThemeName = $this->evaluate($this->theme);
        $themes = $this->getThemes();
        if (!isset($themes[$currentThemeName])) {
            // fallback logic
            Log::warning("CustomMonacoEditor: Theme '{$currentThemeName}' not found. Falling back.");
            $currentThemeName = array_key_first($themes) ?: 'blackboard'; // Ensure blackboard exists or handle error
            if (!isset($themes[$currentThemeName])) {
                 throw new \Exception("CustomMonacoEditor: Default theme '{$currentThemeName}' is also not defined.");
            }
        }
        return json_encode($themes[$currentThemeName], JSON_THROW_ON_ERROR);
    }

    // --- Copy ALL other public fluent methods from the plugin's class ---
    // (language, showPlaceholder, placeholderText, showLoader, fontSize, etc.)
    // Ensure they correctly set $this->propertyName

    public function language(string | Closure $lang = 'html'): static
    {
        if ($lang === 'blade' || $lang === 'blade.php') {
            $lang = 'html';
        }
        $this->language = $lang;
        return $this;
    }

    public function height(string | Closure $height = '500px'): static
    {
        $this->height = $height;
        return $this;
    }

    public function monacoId(string | Closure $id = '1'): static
    {
        $this->monacoId = $id;
        return $this;
    }

    public function theme(string | Closure $name = 'blackboard'): static
    {
        $this->theme = $name;
        return $this;
    }

    public function showPlaceholder(bool | Closure $condition = true): static
    {
        $this->showPlaceholder = $condition;
        return $this;
    }

    public function hidePlaceholder(): static
    {
        $this->showPlaceholder = false;
        return $this;
    }

    public function placeholderText(string | Closure $palceholder = ''): static
    {
        $this->placeholderText = $palceholder;
        return $this;
    }

    public function showLoader(bool | Closure $condition = true): static
    {
        $this->showLoader = $condition;
        return $this;
    }

    public function hideLoader(): static
    {
        $this->showLoader = false;
        return $this;
    }

    public function fontSize(string | Closure $size = '15px'): static
    {
        $this->fontSize = $size;
        return $this;
    }

    public function lineNumbersMinChars(int | Closure $value = 3): static
    {
        $this->lineNumbersMinChars = $value;
        return $this;
    }

    public function automaticLayout(bool | Closure $condition = true): static
    {
        $this->automaticLayout = $condition;
        return $this;
    }

    public function previewHeadEndContent(string | Closure $content = ''): static
    {
        $this->previewHeadEndContent = $content;
        return $this;
    }

    public function previewBodyAttributes(array | Closure $attributes = ['class' => '']): static
    {
        $this->previewBodyAttributes = $attributes;
        return $this;
    }

    public function previewBodyStartContent(string | Closure $content = ''): static
    {
        $this->previewBodyStartContent = $content;
        return $this;
    }

    public function previewBodyEndContent(string | Closure $content = ''): static
    {
        $this->previewBodyEndContent = $content;
        return $this;
    }

    public function enablePreview(bool | Closure $condition = true): static
    {
        $this->enablePreview = $condition;
        return $this;
    }

    public function disablePreview(): static
    {
        $this->enablePreview = false;
        return $this;
    }

    public function showFullScreenToggle(bool | Closure $condition = true): static
    {
        $this->showFullScreenToggle = $condition;
        return $this;
    }

    public function hideFullScreenButton() // Missing return type in original, added : static
    {
        $this->showFullScreenToggle = false;
        return $this;
    }


    // --- Copy ALL public getter methods (getLanguage, getShowPlaceholder, etc.) ---
    // These are used by the Blade view.

    public function getLanguage()
    {
        return $this->evaluate($this->language);
    }

    public function getShowPlaceholder()
    {
        return (bool) $this->evaluate($this->showPlaceholder);
    }

    public function getPlaceholderText()
    {
        return $this->evaluate($this->placeholderText);
    }

    public function getShowLoader()
    {
        return (bool) $this->evaluate($this->showLoader);
    }

    public function getFontSize()
    {
        return $this->evaluate($this->fontSize);
    }

    public function getLineNumbersMinChars()
    {
        return (int) $this->evaluate($this->lineNumbersMinChars);
    }

    public function getAutomaticLayout()
    {
        return (bool) $this->evaluate($this->automaticLayout);
    }

    public function getPreviewHeadEndContent()
    {
        return $this->evaluate($this->previewHeadEndContent);
    }

    public function getPreviewBodyAttributes()
    {
        $attributes = $this->evaluate($this->previewBodyAttributes);
        // Ensure $attributes is an array before implode
        if (!is_array($attributes)) {
            return '';
        }
        return implode(' ', array_map(fn ($key, $value) => "$key=&quot;$value&quot;", array_keys($attributes), $attributes));
    }

    public function getPreviewBodyStartContent()
    {
        return $this->evaluate($this->previewBodyStartContent);
    }

    public function getPreviewBodyEndContent()
    {
        return $this->evaluate($this->previewBodyEndContent);
    }

    public function getEnablePreview()
    {
        return (bool) $this->evaluate($this->enablePreview);
    }

    public function getShowFullScreenToggle()
    {
        return (bool) $this->evaluate($this->showFullScreenToggle);
    }

    public function getHeight()
    {
        return $this->evaluate($this->height);
    }

    public function getMonacoId()
    {
        return $this->evaluate($this->monacoId);
    }

    protected function getThemes(): array
    {
        return config('filament-monaco-editor.themes', $this->themes);
    }
}
