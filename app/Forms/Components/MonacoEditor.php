<?php

namespace App\Forms\Components;

use Filament\Forms\Components\Field;

class MonacoEditor extends Field
{
    protected string $view = 'forms.components.monaco-editor';

    protected string $language = 'html';
    protected string $theme = 'vs-dark';
    protected string $height = '500px';
    protected string|\Illuminate\Contracts\Support\Htmlable|\Closure|null $label;

    public function language(string $lang): static
    {
        $this->language = $lang;
        return $this;
    }

    public function theme(string $theme): static
    {
        $this->theme = $theme;
        return $this;
    }

    public function height(string $height): static
    {
        $this->height = $height;
        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function getTheme(): string
    {
        return $this->theme;
    }

    public function getHeight(): string
    {
        return $this->height;
    }

    public function getLabel(): string
    {
        return $this->label;
    }
}
