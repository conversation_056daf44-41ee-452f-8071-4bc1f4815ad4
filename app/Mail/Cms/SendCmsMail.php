<?php

namespace App\Mail\Cms;

use App\Mail\Cms\CmsEmail;
use App\Services\CmsService;
use Illuminate\Support\Facades\Mail;

/**
 * Builder for sending CMS-based emails with variable parsing and multi-recipient support.
 *
 * Example usage:
 * SendCmsMail::make()
 *     ->to(['<EMAIL>', '<EMAIL>'])
 *     ->template('core.delete-account')
 *     ->lang('en')
 *     ->vars(['name' => 'Mario'])
 *     ->send();
 */
class SendCmsMail
{
    protected array $emails = [];
    protected string $templateKey = '';
    protected string $lang = 'en';
    protected array $vars = [];
    protected CmsService $cmsService;

    public function __construct()
    {
        $this->cmsService = app(CmsService::class);
    }

    public static function make(): self
    {
        return new self();
    }

    /**
     * Set recipients (array or comma-separated string).
     */
    public function to(array|string $emails): self
    {
        if (is_string($emails)) {
            $emails = array_map('trim', explode(',', $emails));
        }
        $this->emails = $emails;
        return $this;
    }

    /**
     * Set the CMS template key.
     */
    public function template(string $templateKey): self
    {
        $this->templateKey = $templateKey;
        return $this;
    }

    /**
     * Set the language.
     */
    public function lang(string $lang): self
    {
        $this->lang = $lang;
        return $this;
    }

    /**
     * Set variables for parsing in the template.
     */
    public function vars(array $vars): self
    {
        $defaultVars = [
            "APP_NAME" => config('app.name'),
            "APP_URL" => config('app.url'),
        ];
        $this->vars = array_merge($defaultVars, $vars);
        return $this;
    }

    /**
     * Preview the email(s).
     */
    public function previewEmailContent()
    {
        $cms = $this->cmsService->getContentByKey($this->templateKey, $this->lang);
        $subject = $this->parseVars($cms['title'] ?? '');
        $body = $this->parseVars($cms['content'] ?? '');
        $email = new CmsEmail($subject, $body);
        return $email->content()->htmlString;
    }

    /**
     * Send the email(s).
     */
    public function send(): void
    {
        $cms = $this->cmsService->getContentByKey($this->templateKey, $this->lang);
        $subject = $this->parseVars($cms['title'] ?? '');
        $body = $this->parseVars($cms['content'] ?? '');

        $defaultMailer = config('mail.default');
        Mail::to($this->emails)->send(new CmsEmail($subject, $body));
    }

    /**
     * Replace {{var}} in the template with values from $vars.
     */
    protected function parseVars(string $text): string
    {
        foreach ($this->vars as $key => $value) {
            $text = preg_replace('/{{\s*' . preg_quote($key, '/') . '\s*}}/', $value, $text);
        }
        return $text;
    }

    public function toMailable(): CmsEmail
    {
        $cms = $this->cmsService->getContentByKey($this->templateKey, $this->lang);
        $subject = $this->parseVars($cms['title'] ?? '');
        $body = $this->parseVars($cms['content'] ?? '');
        return new CmsEmail($subject, $body);
    }
}
