<?php

namespace App\Models;

use App\Services\CmsService;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

class Cms extends Model
{
    use Sushi;

    protected $schema = [
        'id' => 'integer',
        'key' => 'string',
        'url' => 'string',
        'title' => 'string',
        'content' => 'text',
        'additionalHeader' => 'string',
        'metaDescription' => 'string',
        'style' => 'text',
        'status' => 'boolean',
        'createdAt' => 'datetime',
        'updatedAt' => 'datetime',
    ];

    protected $fillable = [
        'id',
        'key',
        'url',
        'title',
        'content',
        'additionalHeader',
        'metaDescription',
        'style',
        'status',
        'createdAt',
        'updatedAt',
    ];

    public $timestamps = false;

    private function normalize(array $item): array
    {
        return collect($item)->mapWithKeys(function ($value, $key) {
            if (is_array($value) || is_object($value)) {
                return [$key => json_encode($value)];
            }

            return [$key => $value];
        })->toArray();
    }

    public function getRows()
    {
        $cmsService = app(CmsService::class);
        $queryParams = [
            "orderBy" => "updatedAt",
            "order" => "DESC",
        ];
        $cmsData = $cmsService->getContentManagementSystems($queryParams);
        if (!is_array($cmsData)) {
            return [];
        }
        if (isset($cmsData['data']) && is_array($cmsData['data'])) {
            $rows = $cmsData['data'];
        } else {
            return [];
        }
        if (empty($rows)) {
            return [];
        }
        // $rows = collect($rows)->map(function ($row) {
        //     return $this->normalize($row);
        // })->toArray();
        $rows = collect($rows)
        ->filter(fn($row) => empty($row['deletedAt']) || is_null($row['deletedAt']))
        ->map(function ($row) {
            return $this->normalize($row);
        })
        ->toArray();
        return $rows;
    }

    public static function bustSushiCache()
        {
            // static::$sushiCache = [];
            // Or more specific:
            // unset(static::$sushiCache[static::class]);
        }
}
