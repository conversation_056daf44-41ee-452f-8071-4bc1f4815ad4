<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RoleApp extends Model
{
    use HasFactory;

    protected $table = 'role_apps';

    protected $fillable = [
        'appId',
        'role',
        'default',
        'deleted',
    ];

    protected $casts = [
        'default' => 'boolean',
        'deleted' => 'boolean',
    ];

    public function app()
    {
        return $this->belongsTo(AppModel::class, 'appId');
    }
}
