<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Region extends Model
{
    protected $fillable = [
        'name',
        'import_id',
        'translation',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    public function countries(): HasMany
    {
        return $this->hasMany(Country::class);
    }
}
