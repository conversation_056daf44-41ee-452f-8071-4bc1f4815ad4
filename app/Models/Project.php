<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Project extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'project_name',
        'project_info',
        'project_type',
        'location_type',
        'latitude',
        'longitude',
        'street_number',
        'street',
        'city',
        'country',
        'country_id',
        'subscription_id',
        'last_user_to_interact',
        'created_by',
    ];

    protected $dates = ['deleted_at'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    public function lastInteractor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_user_to_interact');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(ProjectItem::class);
    }
}
