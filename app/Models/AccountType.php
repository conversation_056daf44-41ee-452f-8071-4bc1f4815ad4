<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\AccountCategory;
use App\Models\AccountInformation;

class AccountType extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'name',
    ];

    public function categories(): HasMany
    {
        return $this->hasMany(AccountCategory::class);
    }

    public function accountInformations(): HasMany
    {
        return $this->hasMany(AccountInformation::class);
    }
}
