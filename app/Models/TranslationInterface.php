<?php
namespace App\Models;

use App\Services\TranslationApiService;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

class TranslationInterface extends Model
{
    use Sushi;

    public function getRows()
    {
        $apiService    = app(TranslationApiService::class);
        $languagesData = $apiService->getAllLanguages(['limit' => 1000]);

        return $languagesData['data'] ?? $languagesData ?? [];
    }

    /**
     * Optional: If you want to clear <PERSON>shi's internal cache for this model.
     * Call this after a CUD operation on the API if redirects don't refresh data.
     */
    public static function bustSushiCache()
    {
        // static::$sushiCache = [];
        // Or more specific:
        // unset(static::$sushiCache[static::class]);
    }
}
