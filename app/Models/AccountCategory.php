<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\AccountInformation;

class AccountCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_type_id',
        'name',
        'is_other',
    ];

    protected $casts = [
        'is_other' => 'boolean',
    ];

    public function accountType(): BelongsTo
    {
        return $this->belongsTo(AccountType::class);
    }

    public function accountInformations(): HasMany
    {
        return $this->hasMany(AccountInformation::class);
    }
}
