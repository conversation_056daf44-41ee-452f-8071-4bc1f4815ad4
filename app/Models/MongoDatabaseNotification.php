<?php
namespace App\Models;

use MongoDB\Laravel\Eloquent\Builder;
use MongoDB\Laravel\Eloquent\Model;

class MongoDatabaseNotification extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'mongo_database_notifications';
    protected $guarded    = [];

    public $incrementing = false;
    protected $keyType   = 'string';

    protected $fillable = [
        'id', // MongoDB will generate _id, <PERSON><PERSON> might expect 'id'
        'type', // This refers to the notification class name, e.g., App\Notifications\ExampleNotification
        'notifiable_type',
        'notifiable_id',
        'data', // This is where your toDatabase() array goes
        'read_at',
    ];

    protected $casts = [
        'data'    => 'array',
        'read_at' => 'datetime',
    ];

    public function notifiable()
    {
        return $this->morphTo();
    }

    public function scopeRead(Builder $query): Builder
    {
        return $query->whereNotNull('read_at');
    }

    public function scopeUnread(Builder $query): Builder
    {
        return $query->whereNull('read_at');
    }

    public function markAsRead(): bool
    {
        return $this->forceFill([
            'read_at' => $this->freshTimestamp(),
        ])->save();
    }

    public function markAsUnread(): bool
    {
        return $this->forceFill([
            'read_at' => null,
        ])->save();
    }
}
