<?php

namespace App\Models;

use App\Constants\NotificationSettingType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NotificationSetting extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['key', 'name', 'type'];

    protected $casts = [
        'type' => NotificationSettingType::class,
    ];
}
