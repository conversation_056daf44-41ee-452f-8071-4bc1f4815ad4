<?php
namespace App\Models;

use App\Models\MongoDatabaseNotification;
use App\Notifications\Auth\QueuedVerifyEmail;
use App\Services\OrderService;
use App\Services\SubscriptionService;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;
use Laragear\TwoFactor\Contracts\TwoFactorAuthenticatable;
use Laragear\TwoFactor\TwoFactorAuthentication;
use Lara<PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser, MustVerifyEmail, TwoFactorAuthenticatable, HasAvatar
{
    use Has<PERSON><PERSON>Tokens, HasFactory, HasRoles, Notifiable, TwoFactorAuthentication, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        // 'name',
        'first_name',
        'last_name',
        'email',
        'password',
        'is_admin',
        'public_name',
        'is_blocked',
        'notes',
        'phone_number',
        'phone_number_verified_at',
        'last_seen_at',
        'settings_json',
        'is_sub_user',
        'owner_id',
        'email_verified_at',
        'pin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_number_verified_at' => 'datetime',
        'password' => 'hashed',
        'last_seen_at' => 'datetime',
        'is_sub_user' => 'boolean',
    ];

    protected $appends = ['full_name'];


    public function getFullNameAttribute(): string
    {
        return trim(($this->first_name ?? '').' '.($this->last_name ?? ''));
    }

    public function getNameAttribute(): string
    {
        return $this->full_name;
    }

    public function getFilamentAvatarUrl(): ?string
    {
        $avatar = Upload::where('resource', 'user')
            ->where('resource_id', $this->id)
            ->where('type', 'profile_picture')
            ->latest()
            ->first();

        return $avatar ? Storage::url($avatar->url) : null;
    }

    public function getCompanyLogoUrl(): ?string
    {
        $logo = Upload::where('resource', 'user')
            ->where('resource_id', $this->id)
            ->where('type', 'company_logo')
            ->latest()
            ->first();

        return $logo ? Storage::url($logo->url) : null;
    }

    public function getSettingsAttribute(): array
    {
        return json_decode($this->settings_json, true) ?? [];
    }

    public function roadmapItems()
    {
        return $this->hasMany(RoadmapItem::class);
    }

    public function roadmapItemUpvotes()
    {
        return $this->belongsToMany(RoadmapItem::class, 'roadmap_item_user_upvotes');
    }

    public function userParameters(): HasMany
    {
        return $this->hasMany(UserParameter::class);
    }

    public function stripeData(): HasMany
    {
        return $this->hasMany(UserStripeData::class);
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function subscriptionTrials(): HasMany
    {
        return $this->hasMany(UserSubscriptionTrial::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function contacts(): HasMany
    {
        return $this->hasMany(UserContactPlatformInformation::class);
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() == 'admin' && !$this->is_admin) {
            return false;
        }

        return true;
    }

    public function getPublicName()
    {
        return $this->public_name ?? $this->name;
    }

    public function scopeAdmin($query)
    {
        return $query->where('is_admin', true);
    }

    public function isAdmin()
    {
        return $this->is_admin;
    }

    public function isPhoneNumberVerified()
    {
        return $this->phone_number_verified_at !== null;
    }

    public function canImpersonate()
    {
        return $this->hasPermissionTo('impersonate users') && $this->isAdmin();
    }

    public function isDeleted(): bool
    {
        return !is_null($this->to_delete_at) || !is_null($this->deleted_at);
    }

    public function isSubscribed(?string $productSlug = null): bool
    {
        /** @var SubscriptionService $subscriptionService */
        $subscriptionService = app(SubscriptionService::class);

        return $subscriptionService->isUserSubscribed($this, $productSlug);
    }

    public function isTrialing(?string $productSlug = null): bool
    {
        /** @var SubscriptionService $subscriptionService */
        $subscriptionService = app(SubscriptionService::class);

        return $subscriptionService->isUserTrialing($this, $productSlug);
    }

    public function hasPurchased(?string $productSlug = null): bool
    {
        /** @var OrderService $orderService */
        $orderService = app(OrderService::class);

        return $orderService->hasUserOrdered($this, $productSlug);
    }

    public function subscriptionProductMetadata()
    {
        /** @var SubscriptionService $subscriptionService */
        $subscriptionService = app(SubscriptionService::class);

        return $subscriptionService->getUserSubscriptionProductMetadata($this);

    }

    public function sendEmailVerificationNotification()
    {
        $this->notify(new QueuedVerifyEmail);
    }

    public function address(): HasOne
    {
        return $this->hasOne(Address::class);
    }

    public function accountInformation(): HasOne
    {
        return $this->hasOne(AccountInformation::class);
    }

    public function notifications()
    {
        return $this
            ->morphMany(
                MongoDatabaseNotification::class,
                'notifiable'
            )
            ->orderBy('created_at', 'desc');
    }

    public function unreadNotifications()
    {
        return $this->notifications()->whereNull('read_at');
    }

    public function preferences(): BelongsToMany
    {
        return $this->belongsToMany(
            NotificationSetting::class,
            'user_notification_setting'
        )
            ->withPivot('is_enabled')
            ->withTimestamps();
    }

    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function subUsers()
    {
        return $this->hasMany(User::class, 'owner_id');
    }
}
