<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Feature extends Model
{
    protected $fillable = [
        'name',
        'description',
        'key',
        'ui_order',
        'display_for_free',
        'allow_for_free',
        'hide',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withPivot('enabled') 
            ->withTimestamps();
    }
}
