<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContactPlatform extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'label',
        'type',
    ];

    public function contacts(): HasMany
    {
        return $this->hasMany(UserContactPlatformInformation::class);
    }
}
