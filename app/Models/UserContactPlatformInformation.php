<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserContactPlatformInformation extends Model
{
    use HasFactory;

    protected $table = 'user_contact_platform_informations';

    protected $fillable = [
        'value',
        'user_id',
        'contact_platform_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function contactPlatform(): BelongsTo
    {
        return $this->belongsTo(ContactPlatform::class);
    }

    /**
     * Get the user contact platform information by user ID and platform key.
     *
     * @param int $userId
     * @param string $platformKey
     * @return static|null
     */
    public static function getByKey(int $userId, string $platformKey): ?static
    {
        $platform = ContactPlatform::where('key', $platformKey)->first();

        if (! $platform) {
            return null;
        }

        return self::where('user_id', $userId)
            ->where('contact_platform_id', $platform->id)
            ->first();
    }


    /**
     * Update or create user contact platform information by user ID and platform key.
     *
     * @param int $userId
     * @param string $platformKey
     * @param string|null $value
     * @return static
     */
    public static function updateOrCreateByKey(int $userId, string $platformKey, ?string $value = null): static
    {
        $platform = ContactPlatform::where('key', $platformKey)->firstOrFail();

        return self::updateOrCreate(
            [
                'user_id' => $userId,
                'contact_platform_id' => $platform->id,
            ],
            [
                'value' => $value,
            ]
        );
    }
}
