<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

// THIS MODEL IS NOT USED. AN INVOICE IS A TRANSACTION
class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'transaction_id',
        'status',
        'filename',
    ];

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    public function receipts(): HasMany
    {
        return $this->hasMany(Receipt::class);
    }
}
