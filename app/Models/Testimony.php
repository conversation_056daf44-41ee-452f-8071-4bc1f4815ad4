<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Testimony extends Model
{
    protected $fillable = [
        'country_id',
        'import_id',
        'name',
        'testimony',
        'language_iso_2',
        'is_default',
        'context',
    ];

    public static function getItemsByLanguage($languageIso2 = null, $context = 'global')
    {
        $query = Testimony::with('country');

        if ($context !== 'all') {
            $query->where('context', $context);
        }

        if ($languageIso2) {
            $languageQuery = clone $query;
            $items = $languageQuery->where('language_iso_2', $languageIso2)->limit(20)->get();
            if ($items->isNotEmpty()) {
                return $items;
            }
        }

        return $query->where('is_default', true)->limit(20)->get();
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
