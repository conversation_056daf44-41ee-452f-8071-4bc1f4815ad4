<?php

namespace App\Mapper;

use App\Constants\TransactionStatus;

class TransactionStatusMapper
{
    public function mapForDisplay(string $status)
    {
        return match ($status) {
            TransactionStatus::NOT_STARTED->value => __('Not Started'),
            TransactionStatus::SUCCESS->value => __('Success'),
            TransactionStatus::FAILED->value => __('Failed'),
            TransactionStatus::REFUNDED->value => __('Refunded'),
            TransactionStatus::DISPUTED->value => __('Disputed'),
            default => __('Pending'),
        };
    }

    public function mapColor(string $status)
    {
        return match ($status) {
            TransactionStatus::SUCCESS->value => 'success',
            TransactionStatus::FAILED->value => 'danger',
            TransactionStatus::REFUNDED->value => 'warning',
            TransactionStatus::DISPUTED->value => 'danger',
            TransactionStatus::PENDING->value => 'info',
            TransactionStatus::NOT_STARTED->value => 'gray',
            default => 'warning',
        };
    }
}
