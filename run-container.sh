#!/bin/bash

# Fix Storage Permissions Script
# Run this script before starting your Docker containers
# Usage: ./fix-permissions.sh [USER_ID] [GROUP_ID] [ENVIRONMENT]
# Example: ./fix-permissions.sh 1000 1000 dev
# Example: ./fix-permissions.sh 33 33 prod

set -e  # Exit on any error

# Default values
DEFAULT_USER_ID=33      # www-data UID
DEFAULT_GROUP_ID=33     # www-data GID
DEFAULT_ENV="dev"       # Default environment

# Parse command line arguments
USER_ID=$DEFAULT_USER_ID
GROUP_ID=$DEFAULT_GROUP_ID
ENVIRONMENT=${1:-$DEFAULT_ENV}

# Validate environment parameter
if [[ ! "$ENVIRONMENT" =~ ^(dev|prod|development|production)$ ]]; then
    echo "❌ Invalid environment. Use: dev, prod, development, or production"
    exit 1
fi

# Normalize environment names
case "$ENVIRONMENT" in
    "development") ENVIRONMENT="dev" ;;
    "production") ENVIRONMENT="prod" ;;
esac

echo "🔧 Fixing Laravel storage permissions for $ENVIRONMENT environment..."
echo "📋 Using User ID: $USER_ID, Group ID: $GROUP_ID"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Function to show usage
show_usage() {
    echo ""
    echo "Usage: $0 [USER_ID] [GROUP_ID] [ENVIRONMENT]"
    echo ""
    echo "Parameters:"
    echo "  USER_ID      User ID to set ownership (default: 33 for www-data)"
    echo "  GROUP_ID     Group ID to set ownership (default: 33 for www-data)"
    echo "  ENVIRONMENT  Environment type: dev/development or prod/production (default: dev)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Use defaults (33:33, dev)"
    echo "  $0 1000 1000 dev     # Use host user 1000:1000 for development"
    echo "  $0 33 33 prod        # Use www-data for production"
    echo ""
}

# Check for help flag
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# Check if we're in a Laravel project
if [ ! -f "artisan" ]; then
    print_error "artisan file not found. Are you in a Laravel project root?"
    show_usage
    exit 1
fi

print_status "Found Laravel project"
print_info "Environment: $ENVIRONMENT"
print_info "Target User:Group = $USER_ID:$GROUP_ID"

# Create storage directories if they don't exist
print_status "Creating storage directories..."
mkdir -p ./storage/app/public
mkdir -p ./storage/framework/sessions
mkdir -p ./storage/framework/views
mkdir -p ./storage/framework/cache
mkdir -p ./storage/logs
touch ./storage/logs/laravel.log

# Create additional directories based on environment
if [ "$ENVIRONMENT" = "prod" ]; then
    print_info "Production environment: Creating additional secure directories..."
    mkdir -p ./storage/app/private
    mkdir -p ./storage/backups
else
    print_info "Development environment: Creating development directories..."
    mkdir -p ./storage/debugbar
fi

print_status "Storage directories created"

# Check if running with sudo privileges for chown (only if not already root)
if [ "$EUID" -ne 0 ] && [ "$USER_ID" != "$(id -u)" ]; then
    print_warning "This script needs sudo privileges to change ownership to $USER_ID:$GROUP_ID"
    print_warning "You may be prompted for your password"
    SUDO_CMD="sudo"
else
    SUDO_CMD=""
fi

# Set ownership based on parameters
print_status "Setting ownership to $USER_ID:$GROUP_ID..."
$SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/app/public
$SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/framework/sessions
$SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/framework/views
$SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/framework/cache
$SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/logs

# Set ownership for environment-specific directories
if [ "$ENVIRONMENT" = "prod" ]; then
    if [ -d "./storage/app/private" ]; then
        $SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/app/private
    fi
    if [ -d "./storage/backups" ]; then
        $SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/backups
    fi
else
    if [ -d "./storage/debugbar" ]; then
        $SUDO_CMD chown -R $USER_ID:$GROUP_ID ./storage/debugbar
    fi
fi

print_status "Ownership set successfully"

# Set appropriate permissions based on environment
if [ "$ENVIRONMENT" = "prod" ]; then
    print_status "Setting production permissions (755/644)..."
    FOLDER_PERM=755
    FILE_PERM=644
    print_info "Production: Using more restrictive permissions for security"
else
    print_status "Setting development permissions (775/664)..."
    FOLDER_PERM=775
    FILE_PERM=664
    print_info "Development: Using permissive permissions for easier development"
fi

# Apply folder permissions
find ./storage -type d -exec chmod $FOLDER_PERM {} \;
# Apply file permissions
find ./storage -type f -exec chmod $FILE_PERM {} \;

print_status "Permissions set successfully"

# Also fix bootstrap/cache if it exists
if [ -d "./bootstrap/cache" ]; then
    print_status "Fixing bootstrap/cache permissions..."
    $SUDO_CMD chown -R $USER_ID:$GROUP_ID ./bootstrap/cache
    find ./bootstrap/cache -type d -exec chmod $FOLDER_PERM {} \;
    find ./bootstrap/cache -type f -exec chmod $FILE_PERM {} \;
    print_status "Bootstrap cache permissions fixed"
fi

# Verify the changes
print_status "Verifying permissions..."
echo ""
echo "Current ownership and permissions:"
ls -la ./storage/app/ | grep public || echo "public directory not found"
ls -la ./storage/framework/ | grep sessions || echo "sessions directory not found"

echo ""
print_status "✅ All storage permissions have been fixed!"
print_status "Environment: $ENVIRONMENT"
print_status "User:Group: $USER_ID:$GROUP_ID"
print_status "Folder permissions: $FOLDER_PERM"
print_status "File permissions: $FILE_PERM"

# Show environment-specific recommendations
if [ "$ENVIRONMENT" = "prod" ]; then
    echo ""
    print_info "🔒 Production environment recommendations:"
    print_info "• Ensure your .env file has proper permissions (600)"
    print_info "• Consider using separate user for sensitive operations"
    print_info "• Regular backup of storage/backups directory"
else
    echo ""
    print_info "🔧 Development environment notes:"
    print_info "• Permissive permissions set for easier development"
    print_info "• Don't use these settings in production!"
fi

# Optional: Ask if user wants to start containers
echo ""
read -p "Do you want to start Docker containers now? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Determine which docker-compose command to use and which file
    COMPOSE_CMD="docker compose"
    COMPOSE_FILE=""
    
    if command -v docker &> /dev/null && docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    fi

    # Look for environment-specific compose files first
    if [ "$ENVIRONMENT" = "prod" ]; then
        COMPOSE_FILE="docker-compose.prod.yml"
        cp .env.sassykit.production .env
    else
        COMPOSE_FILE="docker-compose.local.yml"
        cp .env.sassykit.development .env
    fi
    
    # Fall back to default files
    if [ -z "$COMPOSE_FILE" ]; then
        if [ -f "docker-compose.yml" ]; then
            COMPOSE_FILE="docker-compose.yml"
        elif [ -f "compose.yml" ]; then
            COMPOSE_FILE="compose.yml"
        fi
    fi
    
    if [ -n "$COMPOSE_CMD" ] && [ -n "$COMPOSE_FILE" ]; then
        print_status "Starting Docker containers with $COMPOSE_FILE..."
        if [ "$COMPOSE_CMD" = "docker-compose" ]; then
            $COMPOSE_CMD -f $COMPOSE_FILE up --build 
        else
            $COMPOSE_CMD -f $COMPOSE_FILE up --build
        fi
    else
        if [ -z "$COMPOSE_CMD" ]; then
            print_warning "Docker compose command not found"
        fi
        if [ -z "$COMPOSE_FILE" ]; then
            print_warning "No docker-compose file found"
            print_info "Looked for: docker-compose.$ENVIRONMENT.yml, compose.$ENVIRONMENT.yml, docker-compose.yml, compose.yml"
        fi
    fi
fi